<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版应用测试</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        #app {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .loading {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e4e7ed;
            border-top: 4px solid #409eff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="loading">
            <div class="spinner"></div>
            <p>正在加载简化版应用...</p>
            <p style="font-size: 12px; color: #666;">如果长时间无响应，请检查控制台错误</p>
        </div>
    </div>

    <script type="module">
        // 错误监控
        window.addEventListener('error', (event) => {
            console.error('🚨 页面错误:', event.error);
            
            const appElement = document.getElementById('app');
            if (appElement) {
                appElement.innerHTML = `
                    <div style="padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px; max-width: 600px;">
                        <h2>❌ 页面加载错误</h2>
                        <p><strong>错误信息:</strong> ${event.message}</p>
                        <p><strong>文件:</strong> ${event.filename}</p>
                        <p><strong>行号:</strong> ${event.lineno}:${event.colno}</p>
                        <button onclick="window.location.reload()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            重新加载
                        </button>
                        <button onclick="window.open('/debug-blank.html', '_blank')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px 0 5px;">
                            打开调试工具
                        </button>
                    </div>
                `;
            }
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 未处理的Promise错误:', event.reason);
        });

        // 尝试加载简化版应用
        try {
            import('/src/main-simple.ts').then(() => {
                console.log('✅ 简化版应用模块加载成功');
            }).catch(error => {
                console.error('❌ 简化版应用模块加载失败:', error);
                
                const appElement = document.getElementById('app');
                if (appElement) {
                    appElement.innerHTML = `
                        <div style="padding: 20px; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px; max-width: 600px;">
                            <h2>❌ 模块加载失败</h2>
                            <p><strong>错误信息:</strong> ${error.message}</p>
                            <p>这通常表示以下问题之一：</p>
                            <ul>
                                <li>TypeScript编译错误</li>
                                <li>模块导入路径错误</li>
                                <li>依赖包缺失或版本不兼容</li>
                                <li>Vite配置问题</li>
                            </ul>
                            <button onclick="window.location.reload()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                                重新加载
                            </button>
                            <button onclick="window.open('/debug-blank.html', '_blank')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px 0 5px;">
                                打开调试工具
                            </button>
                        </div>
                    `;
                }
            });
        } catch (error) {
            console.error('❌ 导入失败:', error);
        }

        // 5秒后如果还在加载，显示提示
        setTimeout(() => {
            const appElement = document.getElementById('app');
            if (appElement && appElement.innerHTML.includes('正在加载')) {
                appElement.innerHTML = `
                    <div style="padding: 20px; background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; border-radius: 4px; margin: 20px; max-width: 600px;">
                        <h2>⚠️ 加载超时</h2>
                        <p>应用加载时间过长，可能存在以下问题：</p>
                        <ul>
                            <li>网络连接问题</li>
                            <li>服务器响应慢</li>
                            <li>JavaScript执行错误</li>
                            <li>资源文件过大</li>
                        </ul>
                        <p>请检查浏览器控制台获取更多信息。</p>
                        <button onclick="window.location.reload()" style="padding: 8px 16px; background: #ffc107; color: #212529; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            重新加载
                        </button>
                        <button onclick="window.open('/debug-blank.html', '_blank')" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px 0 5px;">
                            打开调试工具
                        </button>
                    </div>
                `;
            }
        }, 5000);
    </script>
</body>
</html>
