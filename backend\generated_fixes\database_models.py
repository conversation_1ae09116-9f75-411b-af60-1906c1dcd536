
# 数据库模型定义
from sqlalchemy import Column, String, Float, Integer, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from app.core.database import Base
from datetime import datetime

class Order(Base):
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    order_id = Column(String, unique=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    symbol = Column(String, index=True)
    exchange = Column(String)
    direction = Column(String)  # BUY/SELL
    order_type = Column(String)  # LIMIT/MARKET
    status = Column(String, index=True)  # PENDING/FILLED/CANCELLED
    price = Column(Float)
    volume = Column(Integer)
    filled_volume = Column(Integer, default=0)
    submit_time = Column(DateTime, default=datetime.utcnow)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="orders")
    trades = relationship("Trade", back_populates="order")

class Position(Base):
    __tablename__ = "positions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    symbol = Column(String, index=True)
    exchange = Column(String)
    volume = Column(Integer)
    available_volume = Column(Integer)
    avg_price = Column(Float)
    current_price = Column(Float)
    profit_loss = Column(Float)
    profit_rate = Column(Float)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="positions")

class Account(Base):
    __tablename__ = "accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    account_id = Column(String, unique=True)
    total_assets = Column(Float, default=0)
    available_cash = Column(Float, default=0)
    frozen_cash = Column(Float, default=0)
    market_value = Column(Float, default=0)
    total_profit = Column(Float, default=0)
    day_profit = Column(Float, default=0)
    update_time = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="account", uselist=False)
