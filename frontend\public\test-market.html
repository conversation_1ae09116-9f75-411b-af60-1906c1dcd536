<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>市场数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>市场数据API测试</h1>
    
    <div class="test-section">
        <h2>1. 市场概览测试</h2>
        <button onclick="testMarketOverview()">测试市场概览</button>
        <div id="overview-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. 股票列表测试</h2>
        <button onclick="testStockList(5)">测试股票列表 (pageSize=5)</button>
        <button onclick="testStockList(100)">测试股票列表 (pageSize=100)</button>
        <div id="stocks-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. 板块数据测试</h2>
        <button onclick="testSectors()">测试板块数据</button>
        <div id="sectors-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. 行业数据提取测试</h2>
        <button onclick="testIndustryExtraction()">测试行业数据提取</button>
        <div id="industry-result" class="result"></div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';
        
        async function apiCall(url, resultId) {
            const resultElement = document.getElementById(resultId);
            resultElement.innerHTML = '⏳ 请求中...';
            
            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.innerHTML = `✅ 成功:\n${JSON.stringify(data, null, 2)}`;
                    resultElement.className = 'result success';
                    return data;
                } else {
                    resultElement.innerHTML = `❌ 错误: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                    resultElement.className = 'result error';
                    return null;
                }
            } catch (error) {
                resultElement.innerHTML = `❌ 网络错误: ${error.message}`;
                resultElement.className = 'result error';
                return null;
            }
        }
        
        async function testMarketOverview() {
            await apiCall(`${BASE_URL}/api/v1/market/overview`, 'overview-result');
        }
        
        async function testStockList(pageSize) {
            await apiCall(`${BASE_URL}/api/v1/market/stocks?pageSize=${pageSize}`, 'stocks-result');
        }
        
        async function testSectors() {
            await apiCall(`${BASE_URL}/api/v1/market/sectors`, 'sectors-result');
        }
        
        async function testIndustryExtraction() {
            const resultElement = document.getElementById('industry-result');
            resultElement.innerHTML = '⏳ 测试行业数据提取...';
            
            try {
                // 1. 获取股票列表
                const stocksResponse = await fetch(`${BASE_URL}/api/v1/market/stocks?pageSize=100`);
                const stocksData = await stocksResponse.json();
                
                if (!stocksResponse.ok) {
                    throw new Error('获取股票列表失败');
                }
                
                const stocks = stocksData.data?.items || stocksData.data || [];
                
                // 2. 提取行业数据
                const industrySet = new Set();
                const industries = [];
                
                stocks.forEach(stock => {
                    if (stock.industry && !industrySet.has(stock.industry)) {
                        industrySet.add(stock.industry);
                        industries.push({
                            code: stock.industry,
                            name: stock.industry
                        });
                    }
                });
                
                // 3. 获取板块数据
                const sectorsResponse = await fetch(`${BASE_URL}/api/v1/market/sectors`);
                const sectorsData = await sectorsResponse.json();
                const sectors = sectorsData.data || [];
                
                const sectorIndustries = sectors.map(sector => ({
                    code: sector.name,
                    name: sector.name
                }));
                
                const result = {
                    stockCount: stocks.length,
                    industriesFromStocks: industries,
                    industriesFromSectors: sectorIndustries,
                    totalIndustries: industries.length + sectorIndustries.length
                };
                
                resultElement.innerHTML = `✅ 行业数据提取成功:\n${JSON.stringify(result, null, 2)}`;
                resultElement.className = 'result success';
                
            } catch (error) {
                resultElement.innerHTML = `❌ 行业数据提取失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            testMarketOverview();
        };
    </script>
</body>
</html>
