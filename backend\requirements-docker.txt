# Docker构建专用 - 简化依赖列表
# 只包含核心必需的依赖，确保快速构建

# FastAPI核心
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0

# 基础工具
requests==2.31.0
python-multipart==0.0.6
python-dotenv==1.0.0

# 数据处理 (简化版)
pandas==2.1.4
numpy==1.24.3

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
bcrypt==4.1.2

# 缓存
redis==5.0.1

# 图像处理 (验证码)
Pillow==10.1.0

# 时间处理
python-dateutil==2.8.2

# 网络请求
httpx==0.25.2

# 数据验证
email-validator==2.1.0

# 异步支持
aiofiles==23.2.1

# 开发工具
python-json-logger==2.0.7
