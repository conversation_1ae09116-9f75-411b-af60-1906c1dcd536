"""
用户模型
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Table
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

# 用户角色关联表
user_roles = Table(
    'user_roles',
    Base.metadata,
    Column('user_id', Integer, ForeignKey('users.id'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True)
)

# 角色权限关联表
role_permissions = Table(
    'role_permissions',
    Base.metadata,
    Column('role_id', Integer, ForeignKey('roles.id'), primary_key=True),
    Column('permission_id', Integer, ForeignKey('permissions.id'), primary_key=True)
)


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    hashed_password = Column(String(255), nullable=False)
    full_name = Column(String(100))
    phone = Column(String(20))
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_superuser = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = Column(DateTime)
    
    # 用户配置
    avatar_url = Column(String(255))
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="zh-CN")
    theme = Column(String(20), default="light")
    
    # 交易相关
    trading_level = Column(String(20), default="beginner")  # beginner, intermediate, advanced, professional
    risk_tolerance = Column(String(20), default="conservative")  # conservative, moderate, aggressive
    
    # 关联关系
    roles = relationship("Role", secondary=user_roles, back_populates="users")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    trading_accounts = relationship("TradingAccount", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"


class Role(Base):
    """角色模型"""
    __tablename__ = "roles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)  # 系统角色不可删除
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    users = relationship("User", secondary=user_roles, back_populates="roles")
    permissions = relationship("Permission", secondary=role_permissions, back_populates="roles")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}')>"


class Permission(Base):
    """权限模型"""
    __tablename__ = "permissions"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(100), nullable=False)
    description = Column(Text)
    resource = Column(String(50), nullable=False)  # 资源类型：user, trading, data, system
    action = Column(String(50), nullable=False)    # 操作类型：create, read, update, delete, execute
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    roles = relationship("Role", secondary=role_permissions, back_populates="permissions")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}')>"


class UserSession(Base):
    """用户会话模型"""
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=False, index=True)
    
    # 会话信息
    ip_address = Column(String(45))  # 支持IPv6
    user_agent = Column(Text)
    device_info = Column(Text)
    location = Column(String(100))
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_revoked = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=False)
    last_activity = Column(DateTime, default=datetime.utcnow)
    
    # 关联关系
    user = relationship("User", back_populates="sessions")
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, active={self.is_active})>"


class TradingAccount(Base):
    """交易账户模型"""
    __tablename__ = "trading_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 账户信息
    account_name = Column(String(100), nullable=False)
    account_type = Column(String(50), nullable=False)  # demo, live, paper
    platform = Column(String(50), nullable=False)     # binance, alpaca, ib, futu
    account_id = Column(String(100), nullable=False)  # 平台账户ID
    
    # 认证信息（加密存储）
    api_key = Column(String(255))
    api_secret = Column(String(255))
    api_passphrase = Column(String(255))
    
    # 账户配置
    base_currency = Column(String(10), default="USD")
    initial_balance = Column(String(20), default="0")  # 使用字符串存储精确数值
    current_balance = Column(String(20), default="0")
    
    # 风险设置
    max_daily_loss = Column(String(20), default="1000")
    max_position_size = Column(String(20), default="10000")
    max_orders_per_day = Column(Integer, default=100)
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    is_connected = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_connected = Column(DateTime)
    
    # 关联关系
    user = relationship("User", back_populates="trading_accounts")
    
    def __repr__(self):
        return f"<TradingAccount(id={self.id}, name='{self.account_name}', platform='{self.platform}')>"


class LoginAttempt(Base):
    """登录尝试记录"""
    __tablename__ = "login_attempts"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), index=True)
    email = Column(String(100), index=True)
    ip_address = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text)
    
    # 尝试结果
    success = Column(Boolean, nullable=False)
    failure_reason = Column(String(100))  # invalid_credentials, account_locked, etc.
    
    # 时间字段
    attempted_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    def __repr__(self):
        return f"<LoginAttempt(id={self.id}, username='{self.username}', success={self.success})>"


class PasswordResetToken(Base):
    """密码重置令牌"""
    __tablename__ = "password_reset_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    token = Column(String(255), unique=True, nullable=False, index=True)
    
    # 状态字段
    is_used = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=False)
    used_at = Column(DateTime)
    
    def __repr__(self):
        return f"<PasswordResetToken(id={self.id}, user_id={self.user_id}, used={self.is_used})>"


class EmailVerificationToken(Base):
    """邮箱验证令牌"""
    __tablename__ = "email_verification_tokens"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    token = Column(String(255), unique=True, nullable=False, index=True)
    email = Column(String(100), nullable=False)
    
    # 状态字段
    is_used = Column(Boolean, default=False)
    
    # 时间字段
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=False)
    verified_at = Column(DateTime)
    
    def __repr__(self):
        return f"<EmailVerificationToken(id={self.id}, user_id={self.user_id}, used={self.is_used})>"
