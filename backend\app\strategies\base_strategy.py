"""
策略框架基类
提供统一的策略接口和基础功能
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import numpy as np
from datetime import datetime, date
from dataclasses import dataclass
from enum import Enum

class SignalType(Enum):
    """信号类型"""
    BUY = 1
    SELL = -1
    HOLD = 0

class StrategyType(Enum):
    """策略类型"""
    MOMENTUM = "momentum"           # 动量策略
    MEAN_REVERSION = "mean_reversion"  # 均值回归
    PAIRS_TRADING = "pairs_trading"    # 配对交易
    ARBITRAGE = "arbitrage"            # 套利策略
    FACTOR = "factor"                  # 因子策略
    ML = "machine_learning"            # 机器学习策略

@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    description: str
    strategy_type: StrategyType
    version: str = "1.0.0"
    
    # 参数配置
    parameters: Dict[str, Any] = None
    
    # 交易设置
    universe: List[str] = None  # 股票池
    max_positions: int = 10     # 最大持仓数
    position_size: float = 0.1  # 单股仓位
    
    # 风控设置
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    max_drawdown: float = 0.2
    
    # 回测设置
    start_date: Optional[Union[str, date]] = None
    end_date: Optional[Union[str, date]] = None
    benchmark: str = "000300.SH"
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.universe is None:
            self.universe = []

@dataclass
class Signal:
    """交易信号"""
    symbol: str
    signal_type: SignalType
    strength: float  # 信号强度 [0, 1]
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        self.description = config.description
        self.strategy_type = config.strategy_type
        self.parameters = config.parameters
        
        # 内部状态
        self.data: Dict[str, pd.DataFrame] = {}
        self.signals: List[Signal] = []
        self.positions: Dict[str, float] = {}
        self.indicators: Dict[str, pd.DataFrame] = {}
        
        # 初始化策略
        self.initialize()
        
    @abstractmethod
    def initialize(self) -> None:
        """策略初始化"""
        pass
        
    @abstractmethod
    def generate_signals(self, data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """
        生成交易信号
        
        Args:
            data: 股票数据字典，key为股票代码，value为OHLCV数据
            
        Returns:
            信号矩阵，index为日期，columns为股票代码，values为信号强度[-1, 1]
        """
        pass
        
    @abstractmethod
    def calculate_position_size(
        self, 
        symbol: str, 
        signal: float, 
        current_price: float,
        account_value: float
    ) -> float:
        """
        计算持仓大小
        
        Args:
            symbol: 股票代码
            signal: 信号强度
            current_price: 当前价格
            account_value: 账户总价值
            
        Returns:
            目标持仓数量（股数）
        """
        pass
        
    def preprocess_data(self, data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """数据预处理"""
        processed_data = {}
        
        for symbol, df in data.items():
            # 基础数据验证
            if df.empty or not self._validate_data(df):
                continue
                
            # 数据清洗
            cleaned_df = self._clean_data(df.copy())
            
            # 计算基础指标
            enhanced_df = self._add_basic_indicators(cleaned_df)
            
            processed_data[symbol] = enhanced_df
            
        self.data = processed_data
        return processed_data
        
    def _validate_data(self, df: pd.DataFrame) -> bool:
        """验证数据质量"""
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        
        # 检查必要列
        if not all(col in df.columns for col in required_columns):
            return False
            
        # 检查数据完整性
        if df[required_columns].isnull().sum().sum() > len(df) * 0.1:  # 缺失值超过10%
            return False
            
        # 检查价格逻辑
        invalid_price = (
            (df['high'] < df['low']) |
            (df['high'] < df['close']) |
            (df['high'] < df['open']) |
            (df['low'] > df['close']) |
            (df['low'] > df['open'])
        )
        
        if invalid_price.sum() > len(df) * 0.05:  # 价格逻辑错误超过5%
            return False
            
        return True
        
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清洗数据"""
        # 处理缺失值
        df = df.fillna(method='ffill').fillna(method='bfill')
        
        # 处理异常值
        price_columns = ['open', 'high', 'low', 'close']
        for col in price_columns:
            if col in df.columns:
                # 使用3倍标准差规则检测异常值
                mean_val = df[col].mean()
                std_val = df[col].std()
                lower_bound = mean_val - 3 * std_val
                upper_bound = mean_val + 3 * std_val
                
                # 将异常值替换为边界值
                df[col] = df[col].clip(lower_bound, upper_bound)
                
        # 确保成交量为正数
        if 'volume' in df.columns:
            df['volume'] = df['volume'].clip(lower=0)
            
        return df
        
    def _add_basic_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加基础技术指标"""
        # 收益率
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # 价格相关
        df['hl_ratio'] = df['high'] / df['low']
        df['co_ratio'] = df['close'] / df['open']
        
        # 成交量相关
        if 'volume' in df.columns:
            df['volume_ma5'] = df['volume'].rolling(5).mean()
            df['volume_ma20'] = df['volume'].rolling(20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma20']
            
        # 移动平均线
        for period in [5, 10, 20, 60]:
            df[f'ma{period}'] = df['close'].rolling(period).mean()
            df[f'ma{period}_ratio'] = df['close'] / df[f'ma{period}']
            
        # 波动率
        df['volatility_5'] = df['returns'].rolling(5).std()
        df['volatility_20'] = df['returns'].rolling(20).std()
        
        return df
        
    def calculate_indicators(self) -> Dict[str, pd.DataFrame]:
        """计算技术指标"""
        indicators = {}
        
        for symbol, df in self.data.items():
            symbol_indicators = {}
            
            # RSI
            symbol_indicators['rsi'] = self._calculate_rsi(df['close'])
            
            # MACD
            macd_data = self._calculate_macd(df['close'])
            symbol_indicators.update(macd_data)
            
            # 布林带
            bb_data = self._calculate_bollinger_bands(df['close'])
            symbol_indicators.update(bb_data)
            
            # KDJ
            kdj_data = self._calculate_kdj(df['high'], df['low'], df['close'])
            symbol_indicators.update(kdj_data)
            
            indicators[symbol] = pd.DataFrame(symbol_indicators, index=df.index)
            
        self.indicators = indicators
        return indicators
        
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
        
    def _calculate_macd(
        self, 
        prices: pd.Series, 
        fast: int = 12, 
        slow: int = 26, 
        signal: int = 9
    ) -> Dict[str, pd.Series]:
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'macd_signal': signal_line,
            'macd_histogram': histogram
        }
        
    def _calculate_bollinger_bands(
        self, 
        prices: pd.Series, 
        period: int = 20, 
        std_dev: int = 2
    ) -> Dict[str, pd.Series]:
        """计算布林带"""
        sma = prices.rolling(period).mean()
        std = prices.rolling(period).std()
        
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        
        return {
            'bb_upper': upper_band,
            'bb_middle': sma,
            'bb_lower': lower_band,
            'bb_width': (upper_band - lower_band) / sma,
            'bb_position': (prices - lower_band) / (upper_band - lower_band)
        }
        
    def _calculate_kdj(
        self, 
        high: pd.Series, 
        low: pd.Series, 
        close: pd.Series, 
        period: int = 9
    ) -> Dict[str, pd.Series]:
        """计算KDJ指标"""
        lowest_low = low.rolling(period).min()
        highest_high = high.rolling(period).max()
        
        rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
        
        k = rsv.ewm(com=2).mean()
        d = k.ewm(com=2).mean() 
        j = 3 * k - 2 * d
        
        return {'k': k, 'd': d, 'j': j}
        
    def apply_risk_management(self, signals: pd.DataFrame) -> pd.DataFrame:
        """应用风险管理规则"""
        adjusted_signals = signals.copy()
        
        # 最大持仓数限制
        for date in adjusted_signals.index:
            daily_signals = adjusted_signals.loc[date]
            active_signals = daily_signals[daily_signals.abs() > 0.1]
            
            if len(active_signals) > self.config.max_positions:
                # 按信号强度排序，保留最强的信号
                top_signals = active_signals.abs().nlargest(self.config.max_positions)
                
                # 将其他信号设为0
                for symbol in daily_signals.index:
                    if symbol not in top_signals.index:
                        adjusted_signals.loc[date, symbol] = 0
                        
        # 单股仓位限制
        max_position = self.config.position_size
        adjusted_signals = adjusted_signals.clip(-max_position, max_position)
        
        return adjusted_signals
        
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        return {
            'name': self.name,
            'description': self.description,
            'strategy_type': self.strategy_type.value,
            'version': self.config.version,
            'parameters': self.parameters,
            'universe_size': len(self.config.universe),
            'max_positions': self.config.max_positions,
            'position_size': self.config.position_size
        }
        
    def validate_parameters(self) -> bool:
        """验证策略参数"""
        # 基础验证
        if not self.name or not self.description:
            return False
            
        if self.config.max_positions <= 0:
            return False
            
        if not (0 < self.config.position_size <= 1):
            return False
            
        # 子类可以重写此方法添加特定验证
        return self._validate_strategy_specific_parameters()
        
    def _validate_strategy_specific_parameters(self) -> bool:
        """策略特定参数验证（子类重写）"""
        return True
        
    def get_signals_summary(self, signals: pd.DataFrame) -> Dict[str, Any]:
        """获取信号统计摘要"""
        if signals.empty:
            return {}
            
        # 统计信息
        total_signals = (signals != 0).sum().sum()
        buy_signals = (signals > 0).sum().sum()
        sell_signals = (signals < 0).sum().sum()
        
        # 信号强度统计
        signal_strength = signals[signals != 0].abs()
        
        return {
            'total_signals': int(total_signals),
            'buy_signals': int(buy_signals),
            'sell_signals': int(sell_signals),
            'avg_signal_strength': float(signal_strength.mean()),
            'max_signal_strength': float(signal_strength.max()),
            'signal_frequency': float(total_signals / (len(signals) * len(signals.columns))),
            'active_symbols': int((signals != 0).any().sum())
        }