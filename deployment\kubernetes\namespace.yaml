apiVersion: v1
kind: Namespace
metadata:
  name: quant-trading
  labels:
    name: quant-trading
    environment: production
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: quant-resource-quota
  namespace: quant-trading
spec:
  hard:
    requests.cpu: "10"
    requests.memory: 20Gi
    limits.cpu: "20"
    limits.memory: 40Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: quant-limit-range
  namespace: quant-trading
spec:
  limits:
  - default:
      cpu: "1"
      memory: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
    type: Container