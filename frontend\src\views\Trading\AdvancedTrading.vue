<template>
  <div class="advanced-trading">
    <div class="page-header">
      <h2>高级交易功能</h2>
      <p>算法交易、止损止盈、条件单等专业交易工具</p>
    </div>

    <el-tabs v-model="activeTab" class="trading-tabs">
      <!-- 算法交易 -->
      <el-tab-pane label="算法交易" name="algorithm">
        <div class="algorithm-section">
          <div class="algorithm-grid">
            <div class="algorithm-selector">
              <h3>选择交易算法</h3>
              <div class="algorithm-cards">
                <div 
                  v-for="algo in algorithms" 
                  :key="algo.name"
                  class="algorithm-card"
                  :class="{ active: selectedAlgorithm?.name === algo.name }"
                  @click="selectAlgorithm(algo)"
                >
                  <div class="algo-header">
                    <h4>{{ algo.display_name }}</h4>
                    <el-tag :type="getRiskColor(algo.risk_level)" size="small">
                      {{ algo.risk_level }}风险
                    </el-tag>
                  </div>
                  <p class="algo-description">{{ algo.description }}</p>
                  <div class="algo-features">
                    <span v-for="feature in algo.suitable_for" :key="feature" class="feature-tag">
                      {{ feature }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="algorithm-config" v-if="selectedAlgorithm">
              <h3>算法参数配置</h3>
              <el-form :model="algorithmForm" label-width="120px">
                <el-form-item label="股票代码">
                  <el-input v-model="algorithmForm.symbol" placeholder="请输入股票代码" />
                </el-form-item>
                
                <el-form-item label="交易方向">
                  <el-radio-group v-model="algorithmForm.side">
                    <el-radio label="buy">买入</el-radio>
                    <el-radio label="sell">卖出</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="总数量">
                  <el-input-number 
                    v-model="algorithmForm.quantity" 
                    :min="100" 
                    :step="100"
                    style="width: 100%"
                  />
                </el-form-item>

                <!-- 动态参数配置 -->
                <el-form-item 
                  v-for="param in selectedAlgorithm.parameters" 
                  :key="param.name"
                  :label="param.display_name"
                >
                  <el-input-number 
                    v-if="param.type === 'integer'"
                    v-model="algorithmForm.params[param.name]"
                    :min="1"
                    style="width: 100%"
                  />
                  <el-select 
                    v-else-if="param.options"
                    v-model="algorithmForm.params[param.name]"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="option in param.options" 
                      :key="option"
                      :label="option" 
                      :value="option"
                    />
                  </el-select>
                  <el-input 
                    v-else
                    v-model="algorithmForm.params[param.name]"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="submitAlgorithmOrder" :loading="submitting">
                    提交算法订单
                  </el-button>
                  <el-button @click="resetAlgorithmForm">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 止损止盈 -->
      <el-tab-pane label="止损止盈" name="stop">
        <div class="stop-section">
          <el-form :model="stopForm" label-width="120px" class="stop-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <h3>止损止盈设置</h3>
                
                <el-form-item label="股票代码">
                  <el-input v-model="stopForm.symbol" placeholder="请输入股票代码" />
                </el-form-item>
                
                <el-form-item label="交易方向">
                  <el-radio-group v-model="stopForm.side">
                    <el-radio label="buy">买入</el-radio>
                    <el-radio label="sell">卖出</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="数量">
                  <el-input-number 
                    v-model="stopForm.quantity" 
                    :min="100" 
                    :step="100"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="当前价格">
                  <el-input-number 
                    v-model="stopForm.currentPrice" 
                    :precision="2"
                    :step="0.01"
                    style="width: 100%"
                    disabled
                  />
                </el-form-item>
                
                <el-form-item label="止损价格">
                  <el-input-number 
                    v-model="stopForm.stopLoss" 
                    :precision="2"
                    :step="0.01"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="止盈价格">
                  <el-input-number 
                    v-model="stopForm.stopProfit" 
                    :precision="2"
                    :step="0.01"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="submitStopOrder" :loading="submitting">
                    设置止损止盈
                  </el-button>
                  <el-button @click="resetStopForm">重置</el-button>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <div class="stop-preview">
                  <h3>风险预览</h3>
                  <div class="risk-chart">
                    <div class="price-line current" :style="{ bottom: '50%' }">
                      <span>当前价格: {{ stopForm.currentPrice }}</span>
                    </div>
                    <div class="price-line profit" :style="{ bottom: getProfitPosition() }">
                      <span>止盈价格: {{ stopForm.stopProfit }}</span>
                    </div>
                    <div class="price-line loss" :style="{ bottom: getLossPosition() }">
                      <span>止损价格: {{ stopForm.stopLoss }}</span>
                    </div>
                  </div>
                  
                  <div class="risk-metrics">
                    <div class="metric">
                      <label>潜在收益:</label>
                      <span class="profit">+{{ calculateProfit() }}%</span>
                    </div>
                    <div class="metric">
                      <label>潜在亏损:</label>
                      <span class="loss">{{ calculateLoss() }}%</span>
                    </div>
                    <div class="metric">
                      <label>风险收益比:</label>
                      <span>{{ calculateRiskReward() }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 条件单 -->
      <el-tab-pane label="条件单" name="conditional">
        <div class="conditional-section">
          <el-form :model="conditionalForm" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <h3>条件设置</h3>
                
                <el-form-item label="股票代码">
                  <el-input v-model="conditionalForm.symbol" placeholder="请输入股票代码" />
                </el-form-item>
                
                <el-form-item label="条件类型">
                  <el-select v-model="conditionalForm.conditionType" style="width: 100%">
                    <el-option label="价格条件" value="price" />
                    <el-option label="时间条件" value="time" />
                    <el-option label="成交量条件" value="volume" />
                    <el-option label="技术指标" value="indicator" />
                  </el-select>
                </el-form-item>
                
                <el-form-item v-if="conditionalForm.conditionType === 'price'" label="价格条件">
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-select v-model="conditionalForm.priceOperator">
                        <el-option label="大于" value=">" />
                        <el-option label="小于" value="<" />
                        <el-option label="大于等于" value=">=" />
                        <el-option label="小于等于" value="<=" />
                      </el-select>
                    </el-col>
                    <el-col :span="16">
                      <el-input-number 
                        v-model="conditionalForm.targetPrice" 
                        :precision="2"
                        style="width: 100%"
                      />
                    </el-col>
                  </el-row>
                </el-form-item>
                
                <el-form-item v-if="conditionalForm.conditionType === 'time'" label="触发时间">
                  <el-date-picker
                    v-model="conditionalForm.triggerTime"
                    type="datetime"
                    placeholder="选择触发时间"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <h3 style="margin-top: 30px;">订单设置</h3>
                
                <el-form-item label="交易方向">
                  <el-radio-group v-model="conditionalForm.side">
                    <el-radio label="buy">买入</el-radio>
                    <el-radio label="sell">卖出</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-form-item label="订单类型">
                  <el-select v-model="conditionalForm.orderType" style="width: 100%">
                    <el-option label="市价单" value="market" />
                    <el-option label="限价单" value="limit" />
                  </el-select>
                </el-form-item>
                
                <el-form-item v-if="conditionalForm.orderType === 'limit'" label="限价价格">
                  <el-input-number 
                    v-model="conditionalForm.limitPrice" 
                    :precision="2"
                    style="width: 100%"
                  />
                </el-form-item>
                
                <el-form-item label="数量">
                  <el-input-number 
                    v-model="conditionalForm.quantity" 
                    :min="100" 
                    :step="100"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item>
                  <el-button type="primary" @click="submitConditionalOrder" :loading="submitting">
                    提交条件单
                  </el-button>
                  <el-button @click="resetConditionalForm">重置</el-button>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <div class="condition-preview">
                  <h3>条件预览</h3>
                  <div class="condition-summary">
                    <p><strong>触发条件:</strong></p>
                    <p v-if="conditionalForm.conditionType === 'price'">
                      当 {{ conditionalForm.symbol }} 价格 {{ conditionalForm.priceOperator }} {{ conditionalForm.targetPrice }} 时
                    </p>
                    <p v-else-if="conditionalForm.conditionType === 'time'">
                      当时间到达 {{ formatTime(conditionalForm.triggerTime) }} 时
                    </p>
                    <p v-else>
                      条件待设置
                    </p>
                    
                    <p><strong>执行动作:</strong></p>
                    <p>
                      {{ conditionalForm.side === 'buy' ? '买入' : '卖出' }} 
                      {{ conditionalForm.quantity }} 股 {{ conditionalForm.symbol }}
                      <span v-if="conditionalForm.orderType === 'limit'">
                        ，限价 {{ conditionalForm.limitPrice }} 元
                      </span>
                      <span v-else>
                        ，市价成交
                      </span>
                    </p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-tab-pane>

      <!-- 订单监控 -->
      <el-tab-pane label="订单监控" name="monitor">
        <div class="monitor-section">
          <div class="monitor-header">
            <h3>高级订单监控</h3>
            <el-button @click="refreshOrders" :loading="loading">刷新</el-button>
          </div>
          
          <el-table :data="advancedOrders" style="width: 100%">
            <el-table-column prop="order_id" label="订单ID" width="180" />
            <el-table-column prop="symbol" label="股票" width="100" />
            <el-table-column prop="order_type" label="类型" width="100">
              <template #default="scope">
                <el-tag :type="getOrderTypeColor(scope.row.order_type)">
                  {{ getOrderTypeName(scope.row.order_type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="side" label="方向" width="80">
              <template #default="scope">
                <span :class="scope.row.side === 'buy' ? 'text-red' : 'text-green'">
                  {{ scope.row.side === 'buy' ? '买入' : '卖出' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="100" />
            <el-table-column prop="filled_quantity" label="已成交" width="100" />
            <el-table-column label="进度" width="120">
              <template #default="scope">
                <el-progress 
                  :percentage="scope.row.progress || 0" 
                  :status="scope.row.status === 'filled' ? 'success' : ''"
                />
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusColor(scope.row.status)">
                  {{ getStatusName(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间" width="150" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button 
                  size="small" 
                  @click="viewOrderDetail(scope.row)"
                >
                  详情
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="cancelOrder(scope.row.order_id)"
                  v-if="scope.row.status === 'pending' || scope.row.status === 'partial'"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 订单详情对话框 -->
    <el-dialog v-model="orderDetailVisible" title="订单详情" width="80%">
      <div v-if="selectedOrder" class="order-detail">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>基本信息</h4>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="订单ID">{{ selectedOrder.order_id }}</el-descriptions-item>
              <el-descriptions-item label="股票">{{ selectedOrder.symbol }} - {{ selectedOrder.name }}</el-descriptions-item>
              <el-descriptions-item label="类型">{{ getOrderTypeName(selectedOrder.order_type) }}</el-descriptions-item>
              <el-descriptions-item label="状态">
                <el-tag :type="getStatusColor(selectedOrder.status)">
                  {{ getStatusName(selectedOrder.status) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="进度">{{ selectedOrder.progress || 0 }}%</el-descriptions-item>
            </el-descriptions>
          </el-col>
          
          <el-col :span="12">
            <h4>执行详情</h4>
            <div v-if="selectedOrder.child_orders" class="child-orders">
              <el-table :data="selectedOrder.child_orders" size="small">
                <el-table-column prop="slice_id" label="切片ID" />
                <el-table-column prop="quantity" label="数量" />
                <el-table-column prop="price" label="价格" />
                <el-table-column prop="status" label="状态" />
              </el-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('algorithm')
const loading = ref(false)
const submitting = ref(false)
const algorithms = ref([])
const selectedAlgorithm = ref(null)
const advancedOrders = ref([])
const orderDetailVisible = ref(false)
const selectedOrder = ref(null)

// 表单数据
const algorithmForm = reactive({
  symbol: '',
  side: 'buy',
  quantity: 1000,
  params: {}
})

const stopForm = reactive({
  symbol: '',
  side: 'buy',
  quantity: 1000,
  currentPrice: 12.45,
  stopLoss: 11.50,
  stopProfit: 13.50
})

const conditionalForm = reactive({
  symbol: '',
  conditionType: 'price',
  priceOperator: '>',
  targetPrice: 0,
  triggerTime: null,
  side: 'buy',
  orderType: 'market',
  limitPrice: 0,
  quantity: 1000
})

// 计算属性
const getProfitPosition = computed(() => {
  if (!stopForm.stopProfit || !stopForm.currentPrice) return '50%'
  const diff = (stopForm.stopProfit - stopForm.currentPrice) / stopForm.currentPrice
  return `${50 + diff * 100}%`
})

const getLossPosition = computed(() => {
  if (!stopForm.stopLoss || !stopForm.currentPrice) return '50%'
  const diff = (stopForm.stopLoss - stopForm.currentPrice) / stopForm.currentPrice
  return `${50 + diff * 100}%`
})

// 方法
const loadAlgorithms = async () => {
  try {
    const response = await fetch('/api/v1/trading/algorithms')
    const data = await response.json()
    if (data.success) {
      algorithms.value = data.data.algorithms
    }
  } catch (error) {
    console.error('加载算法失败:', error)
  }
}

const selectAlgorithm = (algo) => {
  selectedAlgorithm.value = algo
  algorithmForm.params = {}
  
  // 初始化参数默认值
  algo.parameters.forEach(param => {
    algorithmForm.params[param.name] = param.default
  })
}

const submitAlgorithmOrder = async () => {
  submitting.value = true
  try {
    const response = await fetch('/api/v1/trading/advanced/order', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...algorithmForm,
        order_type: selectedAlgorithm.value.name.toLowerCase(),
        algorithm_params: algorithmForm.params
      })
    })
    
    const data = await response.json()
    if (data.success) {
      ElMessage.success('算法订单提交成功')
      refreshOrders()
    } else {
      ElMessage.error(data.message)
    }
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const submitStopOrder = async () => {
  submitting.value = true
  try {
    // 模拟提交止损止盈订单
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('止损止盈设置成功')
    refreshOrders()
  } catch (error) {
    ElMessage.error('设置失败')
  } finally {
    submitting.value = false
  }
}

const submitConditionalOrder = async () => {
  submitting.value = true
  try {
    // 模拟提交条件单
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('条件单提交成功')
    refreshOrders()
  } catch (error) {
    ElMessage.error('提交失败')
  } finally {
    submitting.value = false
  }
}

const refreshOrders = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/v1/trading/advanced/orders')
    const data = await response.json()
    if (data.success) {
      advancedOrders.value = data.data.orders
    }
  } catch (error) {
    console.error('加载订单失败:', error)
  } finally {
    loading.value = false
  }
}

const viewOrderDetail = (order) => {
  selectedOrder.value = order
  orderDetailVisible.value = true
}

const cancelOrder = async (orderId) => {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
      type: 'warning'
    })
    
    const response = await fetch(`/api/v1/trading/advanced/cancel/${orderId}`, {
      method: 'POST'
    })
    
    const data = await response.json()
    if (data.success) {
      ElMessage.success('订单已取消')
      refreshOrders()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消失败')
    }
  }
}

// 辅助方法
const getRiskColor = (level) => {
  const colors = { '低': 'success', '中': 'warning', '高': 'danger' }
  return colors[level] || 'info'
}

const getOrderTypeColor = (type) => {
  const colors = {
    'twap': 'primary',
    'vwap': 'success', 
    'iceberg': 'warning',
    'stop_loss': 'danger',
    'stop_profit': 'success'
  }
  return colors[type] || 'info'
}

const getOrderTypeName = (type) => {
  const names = {
    'twap': 'TWAP',
    'vwap': 'VWAP',
    'iceberg': '冰山单',
    'stop_loss': '止损单',
    'stop_profit': '止盈单',
    'conditional': '条件单'
  }
  return names[type] || type
}

const getStatusColor = (status) => {
  const colors = {
    'pending': 'warning',
    'partial': 'primary',
    'filled': 'success',
    'cancelled': 'info'
  }
  return colors[status] || 'info'
}

const getStatusName = (status) => {
  const names = {
    'pending': '待成交',
    'partial': '部分成交',
    'filled': '已成交',
    'cancelled': '已取消'
  }
  return names[status] || status
}

const calculateProfit = () => {
  if (!stopForm.stopProfit || !stopForm.currentPrice) return 0
  return ((stopForm.stopProfit - stopForm.currentPrice) / stopForm.currentPrice * 100).toFixed(2)
}

const calculateLoss = () => {
  if (!stopForm.stopLoss || !stopForm.currentPrice) return 0
  return ((stopForm.stopLoss - stopForm.currentPrice) / stopForm.currentPrice * 100).toFixed(2)
}

const calculateRiskReward = () => {
  const profit = Math.abs(parseFloat(calculateProfit()))
  const loss = Math.abs(parseFloat(calculateLoss()))
  if (loss === 0) return '∞'
  return (profit / loss).toFixed(2)
}

const formatTime = (time) => {
  if (!time) return ''
  return new Date(time).toLocaleString()
}

const resetAlgorithmForm = () => {
  Object.assign(algorithmForm, {
    symbol: '',
    side: 'buy',
    quantity: 1000,
    params: {}
  })
}

const resetStopForm = () => {
  Object.assign(stopForm, {
    symbol: '',
    side: 'buy',
    quantity: 1000,
    currentPrice: 12.45,
    stopLoss: 11.50,
    stopProfit: 13.50
  })
}

const resetConditionalForm = () => {
  Object.assign(conditionalForm, {
    symbol: '',
    conditionType: 'price',
    priceOperator: '>',
    targetPrice: 0,
    triggerTime: null,
    side: 'buy',
    orderType: 'market',
    limitPrice: 0,
    quantity: 1000
  })
}

// 生命周期
onMounted(() => {
  loadAlgorithms()
  refreshOrders()
})
</script>

<style scoped>
.advanced-trading {
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.trading-tabs {
  margin-top: 20px;
}

.algorithm-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.algorithm-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.algorithm-card {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.algorithm-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.algorithm-card.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.algo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.algo-header h4 {
  margin: 0;
  color: #303133;
}

.algo-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 15px;
}

.algo-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  background: #f4f4f5;
  color: #909399;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.stop-preview {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
}

.risk-chart {
  position: relative;
  height: 200px;
  background: linear-gradient(to top, #f56c6c 0%, #f56c6c 33%, #e6a23c 33%, #e6a23c 66%, #67c23a 66%, #67c23a 100%);
  border-radius: 4px;
  margin: 20px 0;
}

.price-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 2px;
  background: #fff;
  display: flex;
  align-items: center;
}

.price-line span {
  background: #fff;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: 10px;
}

.risk-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 15px;
}

.metric {
  text-align: center;
}

.metric label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.metric .profit {
  color: #67c23a;
  font-weight: bold;
}

.metric .loss {
  color: #f56c6c;
  font-weight: bold;
}

.condition-preview {
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
}

.condition-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.order-detail {
  padding: 20px 0;
}

.child-orders {
  max-height: 300px;
  overflow-y: auto;
}

.text-red {
  color: #f56c6c;
}

.text-green {
  color: #67c23a;
}
</style>
