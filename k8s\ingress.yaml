apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: quant-platform-ingress
  namespace: quant-platform
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    # WebSocket支持
    nginx.ingress.kubernetes.io/websocket-services: "quant-backend-service"
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    nginx.ingress.kubernetes.io/configuration-snippet: |
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
    # SSL配置（如果需要）
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  # tls:
  # - hosts:
  #   - quant.example.com
  #   secretName: quant-tls
  rules:
  - host: quant.example.com  # 替换为您的域名
    http:
      paths:
      # 前端路由
      - path: /
        pathType: Prefix
        backend:
          service:
            name: quant-frontend-service
            port:
              number: 80
      # API路由
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: quant-backend-service
            port:
              number: 8000
      # WebSocket路由
      - path: /ws
        pathType: Prefix
        backend:
          service:
            name: quant-backend-service
            port:
              number: 8000
      # API文档
      - path: /docs
        pathType: Prefix
        backend:
          service:
            name: quant-backend-service
            port:
              number: 8000
      - path: /redoc
        pathType: Prefix
        backend:
          service:
            name: quant-backend-service
            port:
              number: 8000