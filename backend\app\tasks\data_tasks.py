"""
数据同步任务
"""

import asyncio
import logging
import os
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import psutil
from celery import Celery
from sqlalchemy import select, delete, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_async_session
from app.db.models.market import MarketData, StockInfo
from app.db.models.trading import Order, Trade, Position
from app.db.models.user import User
from app.services.market_data_service import MarketDataService
from app.services.tushare_service import TushareService
from app.services.risk_service import RiskService
from app.tasks.celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=3)
def sync_market_data(self):
    """同步市场数据"""
    try:
        logger.info("开始同步市场数据")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_sync_market_data())
            logger.info(f"市场数据同步完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"同步市场数据失败: {e}")
        raise self.retry(exc=e, countdown=30)


async def _sync_market_data() -> Dict:
    """异步同步市场数据"""
    async with get_async_session() as session:
        market_service = MarketDataService()

        # 获取需要同步的股票列表
        stock_query = select(StockInfo).where(StockInfo.is_active == True)
        result = await session.execute(stock_query)
        stocks = result.scalars().all()

        sync_stats = {
            "total_stocks": len(stocks),
            "success_count": 0,
            "error_count": 0,
            "errors": [],
        }

        for stock in stocks:
            try:
                # 获取最新行情数据
                market_data = await market_service.get_realtime_data(stock.symbol)

                if market_data:
                    # 保存到数据库
                    await _save_market_data(session, stock.symbol, market_data)
                    sync_stats["success_count"] += 1
                else:
                    sync_stats["error_count"] += 1
                    sync_stats["errors"].append(f"无法获取 {stock.symbol} 的数据")

            except Exception as e:
                sync_stats["error_count"] += 1
                sync_stats["errors"].append(f"{stock.symbol}: {str(e)}")
                logger.error(f"同步股票 {stock.symbol} 数据失败: {e}")

        await session.commit()

        return {
            "status": "completed",
            "stats": sync_stats,
            "sync_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def sync_tushare_data(self):
    """同步Tushare数据"""
    try:
        logger.info("开始同步Tushare数据")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_sync_tushare_data())
            logger.info(f"Tushare数据同步完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"同步Tushare数据失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _sync_tushare_data() -> Dict:
    """异步同步Tushare数据"""
    async with get_async_session() as session:
        tushare_service = TushareService()

        # 获取今日交易数据
        today = datetime.now().date()

        sync_stats = {
            "daily_data": 0,
            "basic_data": 0,
            "financial_data": 0,
            "errors": [],
        }

        try:
            # 同步日线数据
            daily_data = await tushare_service.get_daily_data(today)
            if daily_data:
                await _save_daily_data(session, daily_data)
                sync_stats["daily_data"] = len(daily_data)

            # 同步基本面数据
            basic_data = await tushare_service.get_basic_data()
            if basic_data:
                await _save_basic_data(session, basic_data)
                sync_stats["basic_data"] = len(basic_data)

            # 同步财务数据（每季度更新）
            if today.day <= 7:  # 每月前7天检查财务数据
                financial_data = await tushare_service.get_financial_data()
                if financial_data:
                    await _save_financial_data(session, financial_data)
                    sync_stats["financial_data"] = len(financial_data)

            await session.commit()

        except Exception as e:
            sync_stats["errors"].append(str(e))
            await session.rollback()
            raise

        return {
            "status": "completed",
            "stats": sync_stats,
            "sync_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def update_stock_list(self):
    """更新股票列表"""
    try:
        logger.info("开始更新股票列表")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_update_stock_list())
            logger.info(f"股票列表更新完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"更新股票列表失败: {e}")
        raise self.retry(exc=e, countdown=300)


async def _update_stock_list() -> Dict:
    """异步更新股票列表"""
    async with get_async_session() as session:
        tushare_service = TushareService()

        # 获取最新股票列表
        stock_list = await tushare_service.get_stock_list()

        update_stats = {
            "total_stocks": len(stock_list),
            "new_stocks": 0,
            "updated_stocks": 0,
            "delisted_stocks": 0,
        }

        for stock_info in stock_list:
            # 检查股票是否已存在
            existing_stock = await session.execute(
                select(StockInfo).where(StockInfo.symbol == stock_info["symbol"])
            )
            stock = existing_stock.scalar_one_or_none()

            if stock:
                # 更新现有股票信息
                stock.name = stock_info["name"]
                stock.industry = stock_info.get("industry")
                stock.market = stock_info.get("market")
                stock.list_date = stock_info.get("list_date")
                stock.is_active = stock_info.get("is_active", True)
                update_stats["updated_stocks"] += 1
            else:
                # 添加新股票
                new_stock = StockInfo(
                    symbol=stock_info["symbol"],
                    name=stock_info["name"],
                    industry=stock_info.get("industry"),
                    market=stock_info.get("market"),
                    list_date=stock_info.get("list_date"),
                    is_active=stock_info.get("is_active", True),
                )
                session.add(new_stock)
                update_stats["new_stocks"] += 1

        await session.commit()

        return {
            "status": "completed",
            "stats": update_stats,
            "update_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=5)
def risk_monitoring_check(self):
    """风险监控检查"""
    try:
        logger.info("开始风险监控检查")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_risk_monitoring_check())
            logger.info(f"风险监控检查完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"风险监控检查失败: {e}")
        raise self.retry(exc=e, countdown=10)


async def _risk_monitoring_check() -> Dict:
    """异步风险监控检查"""
    async with get_async_session() as session:
        risk_service = RiskService()

        # 获取所有活跃用户的持仓
        positions_query = select(Position).where(Position.quantity > 0)
        result = await session.execute(positions_query)
        positions = result.scalars().all()

        risk_alerts = []

        for position in positions:
            # 检查持仓风险
            risk_check = await risk_service.check_position_risk(position)

            if risk_check["risk_level"] == "high":
                risk_alerts.append(
                    {
                        "type": "position_risk",
                        "user_id": position.user_id,
                        "symbol": position.symbol,
                        "risk_level": risk_check["risk_level"],
                        "message": risk_check["message"],
                        "timestamp": datetime.now().isoformat(),
                    }
                )

        # 检查市场风险
        market_risk = await risk_service.check_market_risk()
        if market_risk["risk_level"] == "high":
            risk_alerts.append(
                {
                    "type": "market_risk",
                    "risk_level": market_risk["risk_level"],
                    "message": market_risk["message"],
                    "timestamp": datetime.now().isoformat(),
                }
            )

        # 发送风险告警
        if risk_alerts:
            await _send_risk_alerts(risk_alerts)

        return {
            "status": "completed",
            "alerts_count": len(risk_alerts),
            "alerts": risk_alerts,
            "check_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=5)
def position_risk_check(self):
    """持仓风险检查"""
    try:
        logger.info("开始持仓风险检查")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_position_risk_check())
            logger.info(f"持仓风险检查完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"持仓风险检查失败: {e}")
        raise self.retry(exc=e, countdown=30)


async def _position_risk_check() -> Dict:
    """异步持仓风险检查"""
    async with get_async_session() as session:
        risk_service = RiskService()

        # 获取所有用户的持仓汇总
        positions_query = select(Position).where(Position.quantity > 0)
        result = await session.execute(positions_query)
        positions = result.scalars().all()

        risk_summary = {
            "total_positions": len(positions),
            "high_risk_positions": 0,
            "medium_risk_positions": 0,
            "low_risk_positions": 0,
            "risk_details": [],
        }

        for position in positions:
            risk_check = await risk_service.check_position_risk(position)

            if risk_check["risk_level"] == "high":
                risk_summary["high_risk_positions"] += 1
            elif risk_check["risk_level"] == "medium":
                risk_summary["medium_risk_positions"] += 1
            else:
                risk_summary["low_risk_positions"] += 1

            risk_summary["risk_details"].append(
                {
                    "user_id": position.user_id,
                    "symbol": position.symbol,
                    "quantity": position.quantity,
                    "risk_level": risk_check["risk_level"],
                    "risk_score": risk_check.get("risk_score", 0),
                }
            )

        return {
            "status": "completed",
            "summary": risk_summary,
            "check_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def system_health_check(self):
    """系统健康检查"""
    try:
        logger.info("开始系统健康检查")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_system_health_check())
            logger.info(f"系统健康检查完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"系统健康检查失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _system_health_check() -> Dict:
    """异步系统健康检查"""
    health_status = {
        "timestamp": datetime.now().isoformat(),
        "overall_status": "healthy",
        "components": {},
    }

    # 检查CPU使用率
    cpu_usage = psutil.cpu_percent(interval=1)
    health_status["components"]["cpu"] = {
        "status": (
            "healthy" if cpu_usage < 80 else "warning" if cpu_usage < 90 else "critical"
        ),
        "usage": cpu_usage,
        "threshold": 80,
    }

    # 检查内存使用率
    memory = psutil.virtual_memory()
    memory_usage = memory.percent
    health_status["components"]["memory"] = {
        "status": (
            "healthy"
            if memory_usage < 80
            else "warning" if memory_usage < 90 else "critical"
        ),
        "usage": memory_usage,
        "available": memory.available,
        "threshold": 80,
    }

    # 检查磁盘使用率
    disk = psutil.disk_usage("/")
    disk_usage = disk.percent
    health_status["components"]["disk"] = {
        "status": (
            "healthy"
            if disk_usage < 80
            else "warning" if disk_usage < 90 else "critical"
        ),
        "usage": disk_usage,
        "free": disk.free,
        "threshold": 80,
    }

    # 检查数据库连接
    try:
        async with get_async_session() as session:
            await session.execute(select(1))
            health_status["components"]["database"] = {
                "status": "healthy",
                "message": "Database connection successful",
            }
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "critical",
            "message": f"Database connection failed: {str(e)}",
        }
        health_status["overall_status"] = "critical"

    # 检查Redis连接
    try:
        # 这里应该检查Redis连接
        health_status["components"]["redis"] = {
            "status": "healthy",
            "message": "Redis connection successful",
        }
    except Exception as e:
        health_status["components"]["redis"] = {
            "status": "critical",
            "message": f"Redis connection failed: {str(e)}",
        }
        health_status["overall_status"] = "critical"

    # 确定整体状态
    if any(
        comp["status"] == "critical" for comp in health_status["components"].values()
    ):
        health_status["overall_status"] = "critical"
    elif any(
        comp["status"] == "warning" for comp in health_status["components"].values()
    ):
        health_status["overall_status"] = "warning"

    return health_status


@celery_app.task(bind=True, max_retries=3)
def cleanup_expired_data(self):
    """清理过期数据"""
    try:
        logger.info("开始清理过期数据")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_cleanup_expired_data())
            logger.info(f"过期数据清理完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"清理过期数据失败: {e}")
        raise self.retry(exc=e, countdown=300)


async def _cleanup_expired_data() -> Dict:
    """异步清理过期数据"""
    async with get_async_session() as session:
        cleanup_stats = {
            "market_data": 0,
            "old_orders": 0,
            "old_trades": 0,
            "session_data": 0,
        }

        # 清理30天前的市场数据
        thirty_days_ago = datetime.now() - timedelta(days=30)

        # 清理旧的市场数据
        market_data_delete = delete(MarketData).where(
            MarketData.timestamp < thirty_days_ago
        )
        result = await session.execute(market_data_delete)
        cleanup_stats["market_data"] = result.rowcount

        # 清理90天前的已完成订单
        ninety_days_ago = datetime.now() - timedelta(days=90)

        old_orders_delete = delete(Order).where(
            Order.created_at < ninety_days_ago,
            Order.status.in_(["filled", "cancelled"]),
        )
        result = await session.execute(old_orders_delete)
        cleanup_stats["old_orders"] = result.rowcount

        # 清理90天前的成交记录
        old_trades_delete = delete(Trade).where(Trade.created_at < ninety_days_ago)
        result = await session.execute(old_trades_delete)
        cleanup_stats["old_trades"] = result.rowcount

        await session.commit()

        return {
            "status": "completed",
            "stats": cleanup_stats,
            "cleanup_time": datetime.now().isoformat(),
        }


@celery_app.task(bind=True, max_retries=3)
def cleanup_old_logs(self):
    """清理旧日志"""
    try:
        logger.info("开始清理旧日志")

        logs_dir = settings.LOGS_DIR
        if not os.path.exists(logs_dir):
            return {"status": "skipped", "message": "Logs directory not found"}

        cleanup_stats = {"files_deleted": 0, "space_freed": 0}

        # 删除30天前的日志文件
        cutoff_date = datetime.now() - timedelta(days=30)

        for filename in os.listdir(logs_dir):
            file_path = os.path.join(logs_dir, filename)

            if os.path.isfile(file_path):
                file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                if file_mtime < cutoff_date:
                    file_size = os.path.getsize(file_path)
                    os.remove(file_path)
                    cleanup_stats["files_deleted"] += 1
                    cleanup_stats["space_freed"] += file_size

        logger.info(f"日志清理完成: {cleanup_stats}")

        return {
            "status": "completed",
            "stats": cleanup_stats,
            "cleanup_time": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"清理旧日志失败: {e}")
        raise self.retry(exc=e, countdown=300)


@celery_app.task(bind=True, max_retries=3)
def backup_database(self):
    """数据库备份"""
    try:
        logger.info("开始数据库备份")

        backup_dir = settings.BACKUP_DIR
        os.makedirs(backup_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"database_backup_{timestamp}.sql"
        backup_path = os.path.join(backup_dir, backup_filename)

        # 这里应该根据实际数据库类型执行备份命令
        # 以PostgreSQL为例
        backup_command = f"pg_dump {settings.DATABASE_URL} > {backup_path}"

        result = os.system(backup_command)

        if result == 0:
            # 备份成功，删除7天前的备份
            seven_days_ago = datetime.now() - timedelta(days=7)

            for filename in os.listdir(backup_dir):
                if filename.startswith("database_backup_"):
                    file_path = os.path.join(backup_dir, filename)
                    file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                    if file_mtime < seven_days_ago:
                        os.remove(file_path)

            return {
                "status": "completed",
                "backup_file": backup_filename,
                "backup_time": datetime.now().isoformat(),
            }
        else:
            raise Exception(f"Database backup failed with exit code {result}")

    except Exception as e:
        logger.error(f"数据库备份失败: {e}")
        raise self.retry(exc=e, countdown=600)


@celery_app.task(bind=True, max_retries=3)
def update_strategy_signals(self):
    """更新策略信号"""
    try:
        logger.info("开始更新策略信号")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_update_strategy_signals())
            logger.info(f"策略信号更新完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"更新策略信号失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _update_strategy_signals() -> Dict:
    """异步更新策略信号"""
    # 这里应该实现策略信号更新逻辑
    # 暂时返回模拟结果
    return {
        "status": "completed",
        "signals_updated": 0,
        "update_time": datetime.now().isoformat(),
    }


@celery_app.task(bind=True, max_retries=3)
def rebalance_portfolios(self):
    """组合再平衡"""
    try:
        logger.info("开始组合再平衡")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_rebalance_portfolios())
            logger.info(f"组合再平衡完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"组合再平衡失败: {e}")
        raise self.retry(exc=e, countdown=300)


async def _rebalance_portfolios() -> Dict:
    """异步组合再平衡"""
    # 这里应该实现组合再平衡逻辑
    # 暂时返回模拟结果
    return {
        "status": "completed",
        "portfolios_rebalanced": 0,
        "rebalance_time": datetime.now().isoformat(),
    }


# 辅助函数
async def _save_market_data(session: AsyncSession, symbol: str, data: Dict):
    """保存市场数据"""
    market_data = MarketData(
        symbol=symbol,
        timestamp=datetime.now(),
        open_price=data.get("open"),
        high_price=data.get("high"),
        low_price=data.get("low"),
        close_price=data.get("close"),
        volume=data.get("volume"),
        amount=data.get("amount"),
    )
    session.add(market_data)


async def _save_daily_data(session: AsyncSession, data: List[Dict]):
    """保存日线数据"""
    for item in data:
        # 实现日线数据保存逻辑
        pass


async def _save_basic_data(session: AsyncSession, data: List[Dict]):
    """保存基本面数据"""
    for item in data:
        # 实现基本面数据保存逻辑
        pass


async def _save_financial_data(session: AsyncSession, data: List[Dict]):
    """保存财务数据"""
    for item in data:
        # 实现财务数据保存逻辑
        pass


async def _send_risk_alerts(alerts: List[Dict]):
    """发送风险告警"""
    # 这里应该实现风险告警发送逻辑
    # 可以通过邮件、短信、WebSocket等方式发送
    for alert in alerts:
        logger.warning(f"风险告警: {alert}")
