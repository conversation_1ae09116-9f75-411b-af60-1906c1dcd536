<template>
  <div class="algorithm-trading">
    <!-- 顶部操作栏 -->
    <div class="header-bar">
      <div class="header-left">
        <h1 class="page-title">
          <el-icon><Setting /></el-icon>
          算法交易
        </h1>
        <div class="status-info">
          <el-tag :type="systemStatus === 'active' ? 'success' : 'info'" size="small">
            {{ systemStatus === 'active' ? '系统运行中' : '系统待机' }}
          </el-tag>
          <span class="active-algorithms">活跃算法: {{ activeAlgorithmsCount }}</span>
        </div>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button type="primary" @click="showCreateAlgorithm = true">
            <el-icon><Plus /></el-icon>
            创建算法
          </el-button>
          <el-button @click="showTemplates = true">
            <el-icon><Document /></el-icon>
            算法模板
          </el-button>
          <el-button @click="showBacktest = true">
            <el-icon><DataAnalysis /></el-icon>
            回测中心
          </el-button>
          <el-button @click="emergencyStop" type="danger">
            <el-icon><Warning /></el-icon>
            紧急停止
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧：算法列表 -->
        <el-col :span="8">
          <el-card class="algorithm-list-panel">
            <template #header>
              <div class="panel-header">
                <span>算法列表</span>
                <el-dropdown @command="handleAlgorithmAction">
                  <el-button size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="startAll">启动全部</el-dropdown-item>
                      <el-dropdown-item command="stopAll">停止全部</el-dropdown-item>
                      <el-dropdown-item command="import">导入算法</el-dropdown-item>
                      <el-dropdown-item command="export">导出算法</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <div class="algorithm-categories">
              <el-tabs v-model="activeCategory" type="card" size="small">
                <el-tab-pane label="条件单" name="conditional">
                  <div class="algorithms-grid">
                    <div
                      v-for="algo in getAlgorithmsByCategory('conditional')"
                      :key="algo.id"
                      class="algorithm-card"
                      :class="{ active: selectedAlgorithm?.id === algo.id, running: algo.status === 'running' }"
                      @click="selectAlgorithm(algo)"
                    >
                      <div class="card-header">
                        <div class="algorithm-name">{{ algo.name }}</div>
                        <el-switch
                          v-model="algo.enabled"
                          size="small"
                          @change="toggleAlgorithm(algo)"
                        />
                      </div>
                      <div class="algorithm-type">{{ algo.type }}</div>
                      <div class="algorithm-stats">
                        <div class="stat-item">
                          <span class="label">触发:</span>
                          <span class="value">{{ algo.triggerCount }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">成功率:</span>
                          <span class="value">{{ algo.successRate }}%</span>
                        </div>
                      </div>
                      <div class="algorithm-actions">
                        <el-button-group size="small">
                          <el-button @click.stop="editAlgorithm(algo)">编辑</el-button>
                          <el-button @click.stop="cloneAlgorithm(algo)">复制</el-button>
                          <el-button @click.stop="deleteAlgorithm(algo)" type="danger">删除</el-button>
                        </el-button-group>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="止盈止损" name="stoploss">
                  <div class="algorithms-grid">
                    <div
                      v-for="algo in getAlgorithmsByCategory('stoploss')"
                      :key="algo.id"
                      class="algorithm-card"
                      :class="{ active: selectedAlgorithm?.id === algo.id, running: algo.status === 'running' }"
                      @click="selectAlgorithm(algo)"
                    >
                      <div class="card-header">
                        <div class="algorithm-name">{{ algo.name }}</div>
                        <el-switch
                          v-model="algo.enabled"
                          size="small"
                          @change="toggleAlgorithm(algo)"
                        />
                      </div>
                      <div class="algorithm-type">{{ algo.type }}</div>
                      <div class="algorithm-stats">
                        <div class="stat-item">
                          <span class="label">保护:</span>
                          <span class="value">{{ algo.protectedPositions }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">执行:</span>
                          <span class="value">{{ algo.executionCount }}</span>
                        </div>
                      </div>
                      <div class="algorithm-actions">
                        <el-button-group size="small">
                          <el-button @click.stop="editAlgorithm(algo)">编辑</el-button>
                          <el-button @click.stop="cloneAlgorithm(algo)">复制</el-button>
                          <el-button @click.stop="deleteAlgorithm(algo)" type="danger">删除</el-button>
                        </el-button-group>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="网格交易" name="grid">
                  <div class="algorithms-grid">
                    <div
                      v-for="algo in getAlgorithmsByCategory('grid')"
                      :key="algo.id"
                      class="algorithm-card"
                      :class="{ active: selectedAlgorithm?.id === algo.id, running: algo.status === 'running' }"
                      @click="selectAlgorithm(algo)"
                    >
                      <div class="card-header">
                        <div class="algorithm-name">{{ algo.name }}</div>
                        <el-switch
                          v-model="algo.enabled"
                          size="small"
                          @change="toggleAlgorithm(algo)"
                        />
                      </div>
                      <div class="algorithm-type">{{ algo.type }}</div>
                      <div class="algorithm-stats">
                        <div class="stat-item">
                          <span class="label">网格:</span>
                          <span class="value">{{ algo.gridCount }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">盈利:</span>
                          <span class="value" :class="algo.profit >= 0 ? 'profit' : 'loss'">
                            {{ formatCurrency(algo.profit) }}
                          </span>
                        </div>
                      </div>
                      <div class="algorithm-actions">
                        <el-button-group size="small">
                          <el-button @click.stop="editAlgorithm(algo)">编辑</el-button>
                          <el-button @click.stop="cloneAlgorithm(algo)">复制</el-button>
                          <el-button @click.stop="deleteAlgorithm(algo)" type="danger">删除</el-button>
                        </el-button-group>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="定投" name="dca">
                  <div class="algorithms-grid">
                    <div
                      v-for="algo in getAlgorithmsByCategory('dca')"
                      :key="algo.id"
                      class="algorithm-card"
                      :class="{ active: selectedAlgorithm?.id === algo.id, running: algo.status === 'running' }"
                      @click="selectAlgorithm(algo)"
                    >
                      <div class="card-header">
                        <div class="algorithm-name">{{ algo.name }}</div>
                        <el-switch
                          v-model="algo.enabled"
                          size="small"
                          @change="toggleAlgorithm(algo)"
                        />
                      </div>
                      <div class="algorithm-type">{{ algo.type }}</div>
                      <div class="algorithm-stats">
                        <div class="stat-item">
                          <span class="label">频率:</span>
                          <span class="value">{{ algo.frequency }}</span>
                        </div>
                        <div class="stat-item">
                          <span class="label">累计:</span>
                          <span class="value">{{ formatCurrency(algo.totalInvested) }}</span>
                        </div>
                      </div>
                      <div class="algorithm-actions">
                        <el-button-group size="small">
                          <el-button @click.stop="editAlgorithm(algo)">编辑</el-button>
                          <el-button @click.stop="cloneAlgorithm(algo)">复制</el-button>
                          <el-button @click.stop="deleteAlgorithm(algo)" type="danger">删除</el-button>
                        </el-button-group>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-card>
        </el-col>

        <!-- 中间：算法详情和配置 -->
        <el-col :span="10">
          <div class="center-panels">
            <!-- 算法详情 -->
            <el-card class="algorithm-detail-panel" v-if="selectedAlgorithm">
              <template #header>
                <div class="panel-header">
                  <span>{{ selectedAlgorithm.name }} - 详细配置</span>
                  <el-button-group size="small">
                    <el-button @click="saveAlgorithm">
                      <el-icon><Check /></el-icon>
                      保存
                    </el-button>
                    <el-button @click="testAlgorithm">
                      <el-icon><VideoPlay /></el-icon>
                      测试
                    </el-button>
                  </el-button-group>
                </div>
              </template>

              <div class="algorithm-config">
                <!-- 基本信息 -->
                <div class="config-section">
                  <h4>基本信息</h4>
                  <el-form :model="selectedAlgorithm" label-width="100px" size="small">
                    <el-form-item label="算法名称">
                      <el-input v-model="selectedAlgorithm.name" />
                    </el-form-item>
                    <el-form-item label="交易标的">
                      <el-select v-model="selectedAlgorithm.symbol" style="width: 100%">
                        <el-option v-for="symbol in availableSymbols" :key="symbol" :label="symbol" :value="symbol" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 条件设置 -->
                <div class="config-section" v-if="selectedAlgorithm.category === 'conditional'">
                  <h4>触发条件</h4>
                  <div class="condition-builder">
                    <div v-for="(condition, index) in selectedAlgorithm.conditions" :key="index" class="condition-item">
                      <el-select v-model="condition.field" placeholder="选择字段" style="width: 120px">
                        <el-option label="价格" value="price" />
                        <el-option label="涨跌幅" value="change_percent" />
                        <el-option label="成交量" value="volume" />
                        <el-option label="技术指标" value="indicator" />
                      </el-select>
                      <el-select v-model="condition.operator" placeholder="操作符" style="width: 80px">
                        <el-option label=">" value="gt" />
                        <el-option label="<" value="lt" />
                        <el-option label="=" value="eq" />
                        <el-option label=">=" value="gte" />
                        <el-option label="<=" value="lte" />
                      </el-select>
                      <el-input-number v-model="condition.value" :precision="2" style="width: 120px" />
                      <el-button @click="removeCondition(index)" type="danger" size="small">删除</el-button>
                    </div>
                    <el-button @click="addCondition" type="primary" size="small">添加条件</el-button>
                  </div>
                </div>

                <!-- 止盈止损设置 -->
                <div class="config-section" v-if="selectedAlgorithm.category === 'stoploss'">
                  <h4>止盈止损设置</h4>
                  <el-form :model="selectedAlgorithm.stopLossConfig" label-width="100px" size="small">
                    <el-form-item label="止损比例">
                      <el-input-number
                        v-model="selectedAlgorithm.stopLossConfig.stopLossPercent"
                        :min="0"
                        :max="50"
                        :step="0.5"
                        :precision="1"
                        style="width: 100%"
                      />
                      <span class="unit">%</span>
                    </el-form-item>
                    <el-form-item label="止盈比例">
                      <el-input-number
                        v-model="selectedAlgorithm.stopLossConfig.takeProfitPercent"
                        :min="0"
                        :max="100"
                        :step="0.5"
                        :precision="1"
                        style="width: 100%"
                      />
                      <span class="unit">%</span>
                    </el-form-item>
                    <el-form-item label="移动止损">
                      <el-switch v-model="selectedAlgorithm.stopLossConfig.trailingStop" />
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 网格交易设置 -->
                <div class="config-section" v-if="selectedAlgorithm.category === 'grid'">
                  <h4>网格交易设置</h4>
                  <el-form :model="selectedAlgorithm.gridConfig" label-width="100px" size="small">
                    <el-form-item label="网格数量">
                      <el-input-number
                        v-model="selectedAlgorithm.gridConfig.gridCount"
                        :min="3"
                        :max="50"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="价格区间">
                      <div class="price-range">
                        <el-input-number
                          v-model="selectedAlgorithm.gridConfig.minPrice"
                          :precision="2"
                          placeholder="最低价"
                          style="width: 120px"
                        />
                        <span>-</span>
                        <el-input-number
                          v-model="selectedAlgorithm.gridConfig.maxPrice"
                          :precision="2"
                          placeholder="最高价"
                          style="width: 120px"
                        />
                      </div>
                    </el-form-item>
                    <el-form-item label="每格投入">
                      <el-input-number
                        v-model="selectedAlgorithm.gridConfig.amountPerGrid"
                        :min="100"
                        :step="100"
                        style="width: 100%"
                      />
                      <span class="unit">元</span>
                    </el-form-item>
                  </el-form>
                </div>

                <!-- 执行动作 -->
                <div class="config-section">
                  <h4>执行动作</h4>
                  <el-form :model="selectedAlgorithm.action" label-width="100px" size="small">
                    <el-form-item label="动作类型">
                      <el-select v-model="selectedAlgorithm.action.type" style="width: 100%">
                        <el-option label="买入" value="buy" />
                        <el-option label="卖出" value="sell" />
                        <el-option label="撤单" value="cancel" />
                        <el-option label="调仓" value="rebalance" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="数量">
                      <el-input-number
                        v-model="selectedAlgorithm.action.quantity"
                        :min="100"
                        :step="100"
                        style="width: 100%"
                      />
                    </el-form-item>
                    <el-form-item label="价格类型">
                      <el-select v-model="selectedAlgorithm.action.priceType" style="width: 100%">
                        <el-option label="市价" value="market" />
                        <el-option label="限价" value="limit" />
                        <el-option label="当前价" value="current" />
                      </el-select>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </el-card>

            <!-- 默认状态 -->
            <el-card class="empty-detail-panel" v-else>
              <div class="empty-content">
                <el-icon class="empty-icon"><DocumentCopy /></el-icon>
                <h3>请选择一个算法</h3>
                <p>选择左侧算法以查看详细配置</p>
                <el-button type="primary" @click="showCreateAlgorithm = true">
                  创建新算法
                </el-button>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧：监控和日志 -->
        <el-col :span="6">
          <div class="right-panels">
            <!-- 执行监控 -->
            <el-card class="monitor-panel">
              <template #header>
                <span>执行监控</span>
              </template>
              
              <div class="monitor-metrics">
                <div class="metric-item">
                  <div class="metric-label">今日执行</div>
                  <div class="metric-value">{{ monitorData.todayExecutions }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">成功率</div>
                  <div class="metric-value">{{ monitorData.successRate }}%</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">平均延迟</div>
                  <div class="metric-value">{{ monitorData.avgLatency }}ms</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">错误次数</div>
                  <div class="metric-value error">{{ monitorData.errorCount }}</div>
                </div>
              </div>

              <div class="execution-chart">
                <div class="chart-title">执行趋势</div>
                <div class="mock-chart">
                  <!-- 简单的模拟图表 -->
                  <div class="chart-bars">
                    <div v-for="i in 24" :key="i" class="chart-bar" :style="{ height: Math.random() * 100 + '%' }"></div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 执行日志 -->
            <el-card class="log-panel">
              <template #header>
                <div class="panel-header">
                  <span>执行日志</span>
                  <el-button size="small" @click="clearLogs">
                    <el-icon><Delete /></el-icon>
                    清空
                  </el-button>
                </div>
              </template>

              <div class="log-list">
                <div
                  v-for="log in executionLogs"
                  :key="log.id"
                  class="log-item"
                  :class="log.level"
                >
                  <div class="log-time">{{ formatTime(log.timestamp) }}</div>
                  <div class="log-content">
                    <div class="log-algorithm">{{ log.algorithm }}</div>
                    <div class="log-message">{{ log.message }}</div>
                  </div>
                  <div class="log-level">
                    <el-tag :type="getLogLevelType(log.level)" size="small">
                      {{ log.level }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Setting, Plus, Document, DataAnalysis, Warning, MoreFilled,
  Check, VideoPlay, DocumentCopy, Delete
} from '@element-plus/icons-vue'

// 状态数据
const systemStatus = ref<'active' | 'idle'>('active')
const activeCategory = ref('conditional')
const selectedAlgorithm = ref(null)

// 弹窗状态
const showCreateAlgorithm = ref(false)
const showTemplates = ref(false)
const showBacktest = ref(false)

// 可用标的
const availableSymbols = ref(['000001', '000002', '600036', '600519', '000858'])

// 算法列表
const algorithms = ref([
  // 条件单
  {
    id: 'algo_001',
    name: '突破买入',
    type: '价格突破',
    category: 'conditional',
    enabled: true,
    status: 'running',
    symbol: '000001',
    triggerCount: 15,
    successRate: 85.2,
    conditions: [
      { field: 'price', operator: 'gt', value: 13.0 },
      { field: 'volume', operator: 'gt', value: 1000000 }
    ],
    action: {
      type: 'buy',
      quantity: 1000,
      priceType: 'market'
    }
  },
  {
    id: 'algo_002',
    name: '跌破卖出',
    type: '价格跌破',
    category: 'conditional',
    enabled: false,
    status: 'stopped',
    symbol: '600036',
    triggerCount: 8,
    successRate: 92.5,
    conditions: [
      { field: 'price', operator: 'lt', value: 45.0 }
    ],
    action: {
      type: 'sell',
      quantity: 500,
      priceType: 'limit'
    }
  },
  
  // 止盈止损
  {
    id: 'algo_003',
    name: '智能止损',
    type: '移动止损',
    category: 'stoploss',
    enabled: true,
    status: 'running',
    symbol: '000002',
    protectedPositions: 3,
    executionCount: 12,
    stopLossConfig: {
      stopLossPercent: 5.0,
      takeProfitPercent: 10.0,
      trailingStop: true
    },
    action: {
      type: 'sell',
      quantity: 0,
      priceType: 'market'
    }
  },
  
  // 网格交易
  {
    id: 'algo_004',
    name: '网格策略A',
    type: '等差网格',
    category: 'grid',
    enabled: true,
    status: 'running',
    symbol: '600519',
    gridCount: 10,
    profit: 1250.80,
    gridConfig: {
      gridCount: 10,
      minPrice: 180.0,
      maxPrice: 220.0,
      amountPerGrid: 1000
    },
    action: {
      type: 'buy',
      quantity: 100,
      priceType: 'limit'
    }
  },
  
  // 定投
  {
    id: 'algo_005',
    name: 'ETF定投',
    type: '定期定额',
    category: 'dca',
    enabled: true,
    status: 'running',
    symbol: '510300',
    frequency: '每周',
    totalInvested: 25000,
    action: {
      type: 'buy',
      quantity: 1000,
      priceType: 'market'
    }
  }
])

// 监控数据
const monitorData = reactive({
  todayExecutions: 48,
  successRate: 88.5,
  avgLatency: 125,
  errorCount: 3
})

// 执行日志
const executionLogs = ref([
  {
    id: 'log_001',
    timestamp: Date.now() - 60000,
    algorithm: '突破买入',
    message: '价格突破13.0，执行买入1000股',
    level: 'success'
  },
  {
    id: 'log_002',
    timestamp: Date.now() - 120000,
    algorithm: '智能止损',
    message: '持仓000002触发5%止损',
    level: 'warning'
  },
  {
    id: 'log_003',
    timestamp: Date.now() - 180000,
    algorithm: '网格策略A',
    message: '网格买入订单执行成功',
    level: 'info'
  },
  {
    id: 'log_004',
    timestamp: Date.now() - 240000,
    algorithm: 'ETF定投',
    message: '定投任务执行完成',
    level: 'success'
  },
  {
    id: 'log_005',
    timestamp: Date.now() - 300000,
    algorithm: '跌破卖出',
    message: '算法已暂停，等待手动启动',
    level: 'info'
  }
])

// 计算属性
const activeAlgorithmsCount = computed(() => {
  return algorithms.value.filter(algo => algo.enabled).length
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(amount)
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 方法
const getAlgorithmsByCategory = (category: string) => {
  return algorithms.value.filter(algo => algo.category === category)
}

const selectAlgorithm = (algorithm: any) => {
  selectedAlgorithm.value = { ...algorithm }
}

const toggleAlgorithm = (algorithm: any) => {
  const action = algorithm.enabled ? '启用' : '禁用'
  algorithm.status = algorithm.enabled ? 'running' : 'stopped'
  ElMessage.success(`算法 ${algorithm.name} 已${action}`)
}

const editAlgorithm = (algorithm: any) => {
  selectedAlgorithm.value = { ...algorithm }
}

const cloneAlgorithm = (algorithm: any) => {
  const cloned = {
    ...algorithm,
    id: `algo_${Date.now()}`,
    name: `${algorithm.name} (副本)`,
    enabled: false,
    status: 'stopped'
  }
  algorithms.value.push(cloned)
  ElMessage.success('算法复制成功')
}

const deleteAlgorithm = async (algorithm: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除算法 "${algorithm.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = algorithms.value.findIndex(a => a.id === algorithm.id)
    if (index !== -1) {
      algorithms.value.splice(index, 1)
      if (selectedAlgorithm.value?.id === algorithm.id) {
        selectedAlgorithm.value = null
      }
      ElMessage.success('算法删除成功')
    }
  } catch (error) {
    // 用户取消
  }
}

const handleAlgorithmAction = (command: string) => {
  switch (command) {
    case 'startAll':
      algorithms.value.forEach(algo => {
        algo.enabled = true
        algo.status = 'running'
      })
      ElMessage.success('所有算法已启动')
      break
    case 'stopAll':
      algorithms.value.forEach(algo => {
        algo.enabled = false
        algo.status = 'stopped'
      })
      ElMessage.success('所有算法已停止')
      break
    case 'import':
      ElMessage.info('导入算法功能开发中')
      break
    case 'export':
      ElMessage.info('导出算法功能开发中')
      break
  }
}

const emergencyStop = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要紧急停止所有算法吗？这将立即停止所有正在运行的算法。',
      '紧急停止',
      {
        confirmButtonText: '确认停止',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    algorithms.value.forEach(algo => {
      algo.enabled = false
      algo.status = 'stopped'
    })
    
    systemStatus.value = 'idle'
    ElMessage.success('紧急停止成功，所有算法已停止')
  } catch (error) {
    // 用户取消
  }
}

const saveAlgorithm = () => {
  if (!selectedAlgorithm.value) return
  
  const index = algorithms.value.findIndex(a => a.id === selectedAlgorithm.value.id)
  if (index !== -1) {
    algorithms.value[index] = { ...selectedAlgorithm.value }
    ElMessage.success('算法保存成功')
  }
}

const testAlgorithm = () => {
  if (!selectedAlgorithm.value) return
  
  ElMessage.info('算法测试功能开发中')
}

const addCondition = () => {
  if (!selectedAlgorithm.value?.conditions) {
    selectedAlgorithm.value.conditions = []
  }
  selectedAlgorithm.value.conditions.push({
    field: 'price',
    operator: 'gt',
    value: 0
  })
}

const removeCondition = (index: number) => {
  selectedAlgorithm.value?.conditions?.splice(index, 1)
}

const clearLogs = () => {
  executionLogs.value = []
  ElMessage.success('日志已清空')
}

const getLogLevelType = (level: string) => {
  const types: Record<string, string> = {
    success: 'success',
    warning: 'warning',
    error: 'danger',
    info: 'info'
  }
  return types[level] || 'info'
}

// 生命周期
onMounted(() => {
  console.log('算法交易模块已加载')
  
  // 模拟实时日志更新
  setInterval(() => {
    if (Math.random() < 0.1) { // 10%概率添加新日志
      const newLog = {
        id: `log_${Date.now()}`,
        timestamp: Date.now(),
        algorithm: algorithms.value[Math.floor(Math.random() * algorithms.value.length)].name,
        message: '算法执行成功',
        level: ['success', 'info', 'warning'][Math.floor(Math.random() * 3)]
      }
      
      executionLogs.value.unshift(newLog)
      if (executionLogs.value.length > 50) {
        executionLogs.value.pop()
      }
    }
  }, 5000)
})
</script>

<style scoped lang="scss">
.algorithm-trading {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.header-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;

  .header-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .page-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }

    .status-info {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;

      .active-algorithms {
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: auto;
  padding: 0 16px;
}

.algorithm-list-panel {
  height: 600px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .algorithms-grid {
    height: 520px;
    overflow-y: auto;

    .algorithm-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }

      &.active {
        border-color: #409eff;
        background: #f0f9ff;
      }

      &.running {
        border-left: 4px solid #67c23a;
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .algorithm-name {
          font-weight: 600;
          color: #303133;
        }
      }

      .algorithm-type {
        font-size: 13px;
        color: #606266;
        margin-bottom: 8px;
      }

      .algorithm-stats {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .stat-item {
          font-size: 12px;

          .label {
            color: #909399;
          }

          .value {
            color: #303133;
            font-weight: 500;

            &.profit {
              color: #67c23a;
            }
            &.loss {
              color: #f56c6c;
            }
          }
        }
      }

      .algorithm-actions {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.algorithm-detail-panel {
  height: 600px;
  margin-bottom: 16px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .algorithm-config {
    height: 520px;
    overflow-y: auto;

    .config-section {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #303133;
        font-weight: 600;
      }

      .unit {
        margin-left: 8px;
        color: #909399;
      }

      .condition-builder {
        .condition-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
        }
      }

      .price-range {
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }
  }
}

.empty-detail-panel {
  height: 600px;
  margin-bottom: 16px;

  .empty-content {
    height: 520px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0 0 16px 0;
      color: #606266;
    }
  }
}

.monitor-panel {
  height: 280px;
  margin-bottom: 16px;

  .monitor-metrics {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;

    .metric-item {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      text-align: center;

      .metric-label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 18px;
        font-weight: 600;
        color: #303133;

        &.error {
          color: #f56c6c;
        }
      }
    }
  }

  .execution-chart {
    .chart-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
    }

    .mock-chart {
      height: 80px;
      background: #f8f9fa;
      border-radius: 6px;
      display: flex;
      align-items: flex-end;
      padding: 8px;

      .chart-bars {
        display: flex;
        align-items: flex-end;
        gap: 2px;
        width: 100%;
        height: 100%;

        .chart-bar {
          flex: 1;
          background: linear-gradient(to top, #409eff, #67c23a);
          border-radius: 2px 2px 0 0;
          min-height: 4px;
        }
      }
    }
  }
}

.log-panel {
  height: 304px;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .log-list {
    height: 240px;
    overflow-y: auto;

    .log-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;

      &:last-child {
        border-bottom: none;
      }

      .log-time {
        width: 80px;
        color: #909399;
      }

      .log-content {
        flex: 1;

        .log-algorithm {
          font-weight: 500;
          color: #303133;
          margin-bottom: 2px;
        }

        .log-message {
          color: #606266;
        }
      }

      .log-level {
        width: 60px;
        text-align: right;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content .el-row .el-col {
    margin-bottom: 16px;
  }
}
</style>
