apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-platform-backend
  namespace: quant-platform
  labels:
    app: quant-platform
    component: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: quant-platform
      component: backend
  template:
    metadata:
      labels:
        app: quant-platform
        component: backend
    spec:
      containers:
      - name: backend
        image: quant-platform/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: APP_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: redis-url
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-platform-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
        volumeMounts:
        - name: data
          mountPath: /data
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: data
        persistentVolumeClaim:
          claimName: quant-platform-data-pvc
      - name: config
        configMap:
          name: quant-platform-config
---
apiVersion: v1
kind: Service
metadata:
  name: quant-platform-backend
  namespace: quant-platform
  labels:
    app: quant-platform
    component: backend
spec:
  selector:
    app: quant-platform
    component: backend
  ports:
  - name: http
    port: 8000
    targetPort: 8000
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quant-platform-backend-hpa
  namespace: quant-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quant-platform-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80