import { ref, computed, onMounted, onUnmounted, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useWebSocket } from '@/composables/useWebSocket'
import { formatCurrency, formatPercent } from '@/utils/formatters'
// import { tradingApi } from '@/api/trading'
import type { Position, AccountSummary } from '@/types/trading'
import { Channel, MessageType } from '@/services/websocket.service'

// 扩展的持仓统计接口
export interface EnhancedPositionStats {
  sharpeRatio: number
  maxDrawdown: number
  winRate: number
  avgLossPercent: number
  positionUtilization: number
  totalReturnRate: number
  annualizedReturn: number
  volatility: number
  informationRatio: number
}

// 行业分布接口
export interface IndustryDistribution {
  name: string
  percentage: number
  value: number
  count: number
  pnl: number
  returnRate: number
}

// 风险指标接口
export interface RiskIndicator {
  level: '低' | '中' | '高'
  score: number
  description: string
}

// 风险建议接口
export interface RiskSuggestion {
  id: string
  priority: '低' | '中' | '高'
  title: string
  description: string
}

// 绩效归因接口
export interface PerformanceAttribution {
  source: string
  contribution: number
  returnRate: number
}

/**
 * usePositions 组合式函数
 * 提供持仓列表管理、账户概览、买卖操作、风险分析、绩效分析等功能
 */
export const usePositions = () => {
  const loading = ref(false)
  const positions = ref<Position[]>([])
  const accountSummary = ref<AccountSummary>({
    totalAssets: 0,
    availableCash: 0,
    todayPnl: 0,
    totalPnl: 0,
    totalMarketValue: 0,
    totalCostValue: 0,
    totalAssetsChange: 0,
    totalAssetsChangePercent: 0,
    todayPnlPercent: 0,
    cashRatio: 0
  })

  // 增强的持仓统计数据
  const enhancedStats = reactive<EnhancedPositionStats>({
    sharpeRatio: 1.25,
    maxDrawdown: -8.5,
    winRate: 65,
    avgLossPercent: -3.2,
    positionUtilization: 75,
    totalReturnRate: 15.68,
    annualizedReturn: 18.5,
    volatility: 22.3,
    informationRatio: 0.72
  })

  // 行业分布数据
  const industryDistribution = ref<IndustryDistribution[]>([
    { name: '信息技术', percentage: 35, value: 350000, count: 8, pnl: 25000, returnRate: 7.8 },
    { name: '金融服务', percentage: 25, value: 250000, count: 5, pnl: 12000, returnRate: 5.2 },
    { name: '消费品', percentage: 20, value: 200000, count: 6, pnl: -8000, returnRate: -3.8 },
    { name: '医疗保健', percentage: 15, value: 150000, count: 4, pnl: 18000, returnRate: 13.6 },
    { name: '其他', percentage: 5, value: 50000, count: 2, pnl: 2000, returnRate: 4.2 }
  ])

  // 风险指标
  const riskIndicators = reactive({
    concentration: {
      level: '中' as const,
      score: 65,
      description: '前5大持仓占比58%，建议适当分散'
    },
    liquidity: {
      level: '低' as const,
      score: 25,
      description: '持仓流动性良好，可快速变现'
    },
    market: {
      level: '中' as const,
      score: 72,
      description: '组合贝塔系数1.15，波动略高于市场'
    }
  })

  // 风险建议
  const riskSuggestions = ref<RiskSuggestion[]>([
    {
      id: 'risk001',
      priority: '高',
      title: '降低行业集中度',
      description: '信息技术行业占比过高，建议减持部分科技股，增加其他行业配置'
    },
    {
      id: 'risk002', 
      priority: '中',
      title: '增加防御性资产',
      description: '考虑增加公用事业、必需消费品等防御性行业配置，降低组合波动'
    },
    {
      id: 'risk003',
      priority: '低',
      title: '优化仓位规模',
      description: '部分个股仓位偏小，考虑适当集中持仓以提高管理效率'
    }
  ])

  // 绩效归因数据
  const performanceAttribution = ref<PerformanceAttribution[]>([
    { source: '行业配置', contribution: 3.2, returnRate: 15.8 },
    { source: '个股选择', contribution: 8.5, returnRate: 22.3 },
    { source: '时机选择', contribution: 2.1, returnRate: 8.7 },
    { source: '现金管理', contribution: -0.8, returnRate: -2.1 },
    { source: '交易成本', contribution: -1.5, returnRate: -4.2 }
  ])

  // WebSocket连接状态
  const wsConnected = ref(false)
  let unsubscribePositions: (() => void) | null = null
  const ws = useWebSocket()

  // 计算属性
  const totalPnl = computed(() => {
    return positions.value.reduce((sum, pos) => sum + pos.unrealizedPnl, 0)
  })

  const totalPnlPercent = computed(() => {
    const totalCost = positions.value.reduce((sum, pos) => sum + (pos.avgPrice * pos.size), 0)
    return totalCost > 0 ? (totalPnl.value / totalCost) * 100 : 0
  })

  const totalMarketValue = computed(() => {
    return positions.value.reduce((sum, pos) => sum + pos.markPrice * pos.size, 0)
  })

  const profitablePositions = computed(() => {
    return positions.value.filter(pos => pos.unrealizedPnl > 0).length
  })

  const losingPositions = computed(() => {
    return positions.value.filter(pos => pos.unrealizedPnl < 0).length
  })

  /**
   * 获取持仓列表
   */
  const fetchPositions = async () => {
    try {
      loading.value = true

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 模拟更多持仓数据
      positions.value = [
        {
          symbol: '000001',
          side: 'long',
          size: 1000,
          avgPrice: 12.30,
          markPrice: 13.20,
          unrealizedPnl: 900,
          unrealizedPnlPercent: 7.32,
          margin: 0,
          marginRatio: 0,
          liquidationPrice: 0,
          leverage: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        {
          symbol: '000002',
          side: 'long',
          size: 500,
          avgPrice: 18.50,
          markPrice: 17.80,
          unrealizedPnl: -350,
          unrealizedPnlPercent: -3.78,
          margin: 0,
          marginRatio: 0,
          liquidationPrice: 0,
          leverage: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        {
          symbol: '600036',
          side: 'long',
          size: 300,
          avgPrice: 41.20,
          markPrice: 42.80,
          unrealizedPnl: 480,
          unrealizedPnlPercent: 3.88,
          margin: 0,
          marginRatio: 0,
          liquidationPrice: 0,
          leverage: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        {
          symbol: '000858',
          side: 'long',
          size: 200,
          avgPrice: 168.50,
          markPrice: 175.30,
          unrealizedPnl: 1360,
          unrealizedPnlPercent: 4.04,
          margin: 0,
          marginRatio: 0,
          liquidationPrice: 0,
          leverage: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        },
        {
          symbol: '002415',
          side: 'long',
          size: 800,
          avgPrice: 42.50,
          markPrice: 38.90,
          unrealizedPnl: -2880,
          unrealizedPnlPercent: -8.47,
          margin: 0,
          marginRatio: 0,
          liquidationPrice: 0,
          leverage: 1,
          createTime: new Date().toISOString(),
          updateTime: new Date().toISOString()
        }
      ]

      // 计算账户概览
      const totalMarketVal = totalMarketValue.value
      const totalCostValue = positions.value.reduce((sum, pos) => sum + pos.avgPrice * pos.size, 0)
      const totalPnlVal = totalPnl.value
      const todayPnl = positions.value.reduce((sum, pos) => sum + pos.unrealizedPnl * 0.3, 0) // 模拟今日盈亏
      const availableCash = 50000

      accountSummary.value = {
        totalAssets: totalMarketVal + availableCash,
        availableCash,
        todayPnl,
        totalPnl: totalPnlVal,
        totalMarketValue: totalMarketVal,
        totalCostValue,
        totalAssetsChange: totalPnlVal + todayPnl * 0.5,
        totalAssetsChangePercent: totalCostValue > 0 ? ((totalPnlVal + todayPnl * 0.5) / totalCostValue) * 100 : 0,
        todayPnlPercent: totalCostValue > 0 ? (todayPnl / totalCostValue) * 100 : 0,
        cashRatio: availableCash / (totalMarketVal + availableCash) * 100
      }

      ElMessage.success('持仓数据加载成功')
    } catch (error) {
      console.error('获取持仓列表失败:', error)
      ElMessage.error('获取持仓列表失败')
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取账户概览
   */
  const fetchAccountSummary = async () => {
    // 在fetchPositions中一起计算了
  }

  /**
   * 买入股票
   */
  const buyStock = async (symbol: string, name?: string) => {
    try {
      // 这里应该打开交易弹窗或跳转到交易页面
      ElMessage.info(`准备买入 ${name || symbol}`)
      // 可以触发一个事件或调用路由跳转
    } catch (error) {
      console.error('买入操作失败:', error)
      ElMessage.error('买入操作失败')
    }
  }

  /**
   * 卖出股票
   */
  const sellStock = async (position: Position) => {
    try {
      // 这里应该打开卖出弹窗
      ElMessage.info(`准备卖出 ${position.symbol}`)
      // 可以触发一个事件或调用路由跳转
    } catch (error) {
      console.error('卖出操作失败:', error)
      ElMessage.error('卖出操作失败')
    }
  }

  /**
   * 卖出持仓
   */
  const sellPosition = async (positionId: string, quantity: number, price?: number) => {
    try {
      const position = positions.value.find(p => p.symbol === positionId)
      if (!position) {
        throw new Error('持仓不存在')
      }

      if (quantity > position.size) {
        throw new Error('卖出数量超过持仓数量')
      }

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      ElMessage.success(`成功提交卖出订单: ${position.symbol} ${quantity}股`)

      // 刷新持仓数据
      await fetchPositions()
    } catch (error) {
      console.error('卖出失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '卖出失败')
    }
  }

  /**
   * 买入更多
   */
  const buyMore = async (symbol: string, quantity: number, price?: number) => {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      ElMessage.success(`成功提交买入订单: ${symbol} ${quantity}股`)

      // 刷新持仓数据
      await fetchPositions()
    } catch (error) {
      console.error('买入失败:', error)
      ElMessage.error('买入失败')
    }
  }

  /**
   * 一键清仓
   */
  const clearAllPositions = async () => {
    try {
      await ElMessageBox.confirm(
        '确定要清空所有持仓吗？此操作不可撤销。',
        '确认清仓',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      ElMessage.success('清仓订单已提交')

      // 刷新持仓数据
      await fetchPositions()
    } catch (error) {
      if (error !== 'cancel') {
        console.error('清仓失败:', error)
        ElMessage.error('清仓失败')
      }
    }
  }

  /**
   * 导出持仓数据
   */
  const exportPositions = async (data?: Position[]) => {
    try {
      const exportData = data || positions.value

      // 增强的导出数据，包含更多字段
      const headers = [
        '股票代码', '股票名称', '持仓数量', '成本价', '现价', '市值', 
        '浮动盈亏', '盈亏比例', '持仓占比', '创建时间', '更新时间'
      ]
      
      const csvContent = [
        headers.join(','),
        ...exportData.map(pos => {
          const marketValue = pos.markPrice * pos.size
          const positionRatio = totalMarketValue.value > 0 ? (marketValue / totalMarketValue.value) * 100 : 0
          const stockName = getStockName(pos.symbol)
          
          return [
            pos.symbol,
            stockName,
            pos.size,
            pos.avgPrice.toFixed(2),
            pos.markPrice.toFixed(2),
            marketValue.toFixed(2),
            pos.unrealizedPnl.toFixed(2),
            `${pos.unrealizedPnlPercent.toFixed(2)}%`,
            `${positionRatio.toFixed(2)}%`,
            new Date(pos.createTime).toLocaleString(),
            new Date(pos.updateTime).toLocaleString()
          ].join(',')
        })
      ].join('\n')

      // 创建下载链接
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `持仓数据_${new Date().toISOString().slice(0, 10)}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      ElMessage.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }

  /**
   * 获取股票名称映射
   */
  const getStockName = (symbol: string): string => {
    const stockNames: Record<string, string> = {
      '000001': '平安银行',
      '000002': '万科A',
      '600036': '招商银行',
      '000858': '五粮液',
      '002415': '海康威视'
    }
    return stockNames[symbol] || symbol
  }

  /**
   * 应用风险建议
   */
  const applyRiskSuggestion = async (suggestion: RiskSuggestion): Promise<void> => {
    try {
      // 模拟应用建议的逻辑
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      ElMessage.success(`已应用建议: ${suggestion.title}`)
      
      // 这里可以根据建议类型执行相应的操作
      switch (suggestion.id) {
        case 'risk001':
          // 降低行业集中度的逻辑
          break
        case 'risk002':
          // 增加防御性资产的逻辑
          break
        case 'risk003':
          // 优化仓位规模的逻辑
          break
      }
      
      // 刷新数据
      await refreshAll()
    } catch (error) {
      console.error('应用建议失败:', error)
      ElMessage.error('应用建议失败')
    }
  }

  /**
   * 更新增强统计数据
   */
  const updateEnhancedStats = (): void => {
    // 根据实际持仓数据计算增强统计指标
    const profitableCount = profitablePositions.value
    const totalCount = positions.value.length
    
    if (totalCount > 0) {
      enhancedStats.winRate = Math.round((profitableCount / totalCount) * 100)
    }
    
    // 计算平均亏损比例
    const losingPos = positions.value.filter(pos => pos.unrealizedPnl < 0)
    if (losingPos.length > 0) {
      enhancedStats.avgLossPercent = losingPos.reduce((sum, pos) => sum + pos.unrealizedPnlPercent, 0) / losingPos.length
    }
    
    // 计算仓位使用率
    const totalAssets = accountSummary.value.totalAssets
    if (totalAssets > 0) {
      enhancedStats.positionUtilization = Math.round((totalMarketValue.value / totalAssets) * 100)
    }
    
    // 其他指标可以根据历史数据计算
    // 这里使用模拟数据
    enhancedStats.totalReturnRate = totalPnlPercent.value
  }

  const handlePositionUpdate = (data: any) => {
    if (!data) return
    const index = positions.value.findIndex(p => p.symbol === data.symbol)
    if (index >= 0) {
      positions.value[index] = {
        ...positions.value[index],
        ...data
      }
    } else {
      positions.value.push(data as Position)
    }
  }

  const subscribePositionUpdates = () => {
    if (unsubscribePositions) return
    unsubscribePositions = ws.websocketService.subscribe(Channel.TRADING, MessageType.POSITION, handlePositionUpdate)
    wsConnected.value = true
  }

  const unsubscribePositionUpdates = () => {
    if (unsubscribePositions) {
      unsubscribePositions()
      unsubscribePositions = null
    }
    wsConnected.value = false
  }

  onMounted(() => {
    subscribePositionUpdates()
  })

  onUnmounted(() => {
    unsubscribePositionUpdates()
  })

  /**
   * 刷新所有数据
   */
  const refreshAll = async () => {
    await Promise.all([
      fetchPositions(),
      fetchAccountSummary()
    ])
  }

  return {
    // 基础状态
    loading,
    positions,
    accountSummary,
    totalPnl,
    totalPnlPercent,
    totalMarketValue,
    profitablePositions,
    losingPositions,
    wsConnected,
    
    // 增强统计数据
    enhancedStats,
    industryDistribution,
    riskIndicators,
    riskSuggestions,
    performanceAttribution,
    
    // 基础方法
    fetchPositions,
    fetchAccountSummary,
    buyStock,
    sellStock,
    sellPosition,
    buyMore,
    clearAllPositions,
    exportPositions,
    subscribePositionUpdates,
    unsubscribePositionUpdates,
    refreshAll,
    
    // 增强方法
    getStockName,
    applyRiskSuggestion,
    updateEnhancedStats
  }
}
