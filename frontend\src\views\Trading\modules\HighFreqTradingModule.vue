<template>
  <div class="high-freq-trading">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <h2 class="module-title">
          <el-icon><Lightning /></el-icon>
          高频交易系统
        </h2>
        <div class="system-status">
          <el-tag :type="systemStatus === 'running' ? 'success' : 'info'" size="small">
            <el-icon v-if="systemStatus === 'running'"><VideoPlay /></el-icon>
            <el-icon v-else><VideoPause /></el-icon>
            {{ systemStatus === 'running' ? '运行中' : '已停止' }}
          </el-tag>
          <span class="latency">延迟: {{ latency }}ms</span>
          <span class="throughput">吞吐量: {{ throughput }}/s</span>
        </div>
      </div>
      <div class="status-right">
        <el-button-group>
          <el-button
            :type="systemStatus === 'running' ? 'danger' : 'success'"
            @click="toggleSystem"
            :loading="systemLoading"
          >
            <el-icon v-if="systemStatus === 'running'"><VideoPause /></el-icon>
            <el-icon v-else><VideoPlay /></el-icon>
            {{ systemStatus === 'running' ? '停止系统' : '启动系统' }}
          </el-button>
          <el-button @click="emergencyStop" type="danger" plain>
            <el-icon><Warning /></el-icon>
            紧急停止
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧：策略管理 -->
        <el-col :span="8">
          <el-card class="strategy-panel">
            <template #header>
              <div class="card-header">
                <span>策略管理</span>
                <el-button type="primary" size="small" @click="showCreateStrategy = true">
                  <el-icon><Plus /></el-icon>
                  新建策略
                </el-button>
              </div>
            </template>
            
            <div class="strategy-list">
              <div
                v-for="strategy in strategies"
                :key="strategy.id"
                class="strategy-item"
                :class="{ active: selectedStrategy?.id === strategy.id }"
                @click="selectStrategy(strategy)"
              >
                <div class="strategy-header">
                  <div class="strategy-name">{{ strategy.name }}</div>
                  <el-tag 
                    :type="getStrategyStatusType(strategy.status)" 
                    size="small"
                  >
                    {{ getStrategyStatusText(strategy.status) }}
                  </el-tag>
                </div>
                <div class="strategy-info">
                  <div class="strategy-type">{{ strategy.type }}</div>
                  <div class="strategy-performance">
                    收益率: 
                    <span :class="strategy.profitRate >= 0 ? 'profit' : 'loss'">
                      {{ strategy.profitRate >= 0 ? '+' : '' }}{{ strategy.profitRate.toFixed(2) }}%
                    </span>
                  </div>
                </div>
                <div class="strategy-actions">
                  <el-button-group size="small">
                    <el-button
                      :type="strategy.status === 'running' ? 'danger' : 'success'"
                      @click.stop="toggleStrategy(strategy)"
                      size="small"
                    >
                      {{ strategy.status === 'running' ? '停止' : '启动' }}
                    </el-button>
                    <el-button
                      type="info"
                      @click.stop="editStrategy(strategy)"
                      size="small"
                    >
                      编辑
                    </el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 中间：实时监控 -->
        <el-col :span="10">
          <el-card class="monitor-panel">
            <template #header>
              <div class="card-header">
                <span>实时监控</span>
                <el-button-group size="small">
                  <el-button @click="refreshMonitor">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                  <el-button @click="showSettings = true">
                    <el-icon><Setting /></el-icon>
                    设置
                  </el-button>
                </el-button-group>
              </div>
            </template>

            <!-- 实时指标 -->
            <div class="metrics-grid">
              <div class="metric-card">
                <div class="metric-label">总订单数</div>
                <div class="metric-value">{{ metrics.totalOrders }}</div>
                <div class="metric-change positive">+{{ metrics.ordersChange }}</div>
              </div>
              <div class="metric-card">
                <div class="metric-label">成交率</div>
                <div class="metric-value">{{ metrics.fillRate }}%</div>
                <div class="metric-change" :class="metrics.fillRateChange >= 0 ? 'positive' : 'negative'">
                  {{ metrics.fillRateChange >= 0 ? '+' : '' }}{{ metrics.fillRateChange }}%
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">平均延迟</div>
                <div class="metric-value">{{ latency }}ms</div>
                <div class="metric-change" :class="metrics.latencyChange <= 0 ? 'positive' : 'negative'">
                  {{ metrics.latencyChange <= 0 ? '' : '+' }}{{ metrics.latencyChange }}ms
                </div>
              </div>
              <div class="metric-card">
                <div class="metric-label">今日盈亏</div>
                <div class="metric-value" :class="metrics.todayPnl >= 0 ? 'profit' : 'loss'">
                  {{ formatCurrency(metrics.todayPnl) }}
                </div>
                <div class="metric-change" :class="metrics.pnlChange >= 0 ? 'positive' : 'negative'">
                  {{ metrics.pnlChange >= 0 ? '+' : '' }}{{ formatCurrency(metrics.pnlChange) }}
                </div>
              </div>
            </div>

            <!-- 实时图表 -->
            <div class="chart-container">
              <div class="chart-header">
                <span>实时P&L曲线</span>
                <el-button-group size="small">
                  <el-button 
                    v-for="period in chartPeriods"
                    :key="period.value"
                    :type="selectedChartPeriod === period.value ? 'primary' : 'default'"
                    @click="selectedChartPeriod = period.value"
                    size="small"
                  >
                    {{ period.label }}
                  </el-button>
                </el-button-group>
              </div>
              <div class="mock-chart">
                <!-- 这里应该集成真实的图表库，如ECharts -->
                <div class="chart-placeholder">
                  <div class="chart-line"></div>
                  <div class="chart-info">实时P&L图表 ({{ selectedChartPeriod }})</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：控制面板 -->
        <el-col :span="6">
          <el-card class="control-panel">
            <template #header>
              <span>控制面板</span>
            </template>

            <div class="control-sections">
              <!-- 风险控制 -->
              <div class="control-section">
                <h4>风险控制</h4>
                <el-form label-width="80px" size="small">
                  <el-form-item label="最大损失">
                    <el-input-number
                      v-model="riskSettings.maxLoss"
                      :min="0"
                      :step="1000"
                      style="width: 100%"
                    />
                  </el-form-item>
                  <el-form-item label="最大持仓">
                    <el-input-number
                      v-model="riskSettings.maxPosition"
                      :min="0"
                      :step="10000"
                      style="width: 100%"
                    />
                  </el-form-item>
                  <el-form-item label="止损比例">
                    <el-input-number
                      v-model="riskSettings.stopLossPercent"
                      :min="0"
                      :max="100"
                      :step="0.1"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 系统参数 -->
              <div class="control-section">
                <h4>系统参数</h4>
                <el-form label-width="80px" size="small">
                  <el-form-item label="刷新频率">
                    <el-select v-model="systemSettings.refreshRate" style="width: 100%">
                      <el-option label="100ms" :value="100" />
                      <el-option label="200ms" :value="200" />
                      <el-option label="500ms" :value="500" />
                      <el-option label="1s" :value="1000" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="并发数">
                    <el-input-number
                      v-model="systemSettings.maxConcurrency"
                      :min="1"
                      :max="1000"
                      style="width: 100%"
                    />
                  </el-form-item>
                  <el-form-item label="超时时间">
                    <el-input-number
                      v-model="systemSettings.timeout"
                      :min="100"
                      :max="10000"
                      :step="100"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-form>
              </div>

              <!-- 快速操作 -->
              <div class="control-section">
                <h4>快速操作</h4>
                <div class="quick-actions">
                  <el-button type="danger" size="small" @click="pauseAllStrategies" block>
                    暂停所有策略
                  </el-button>
                  <el-button type="success" size="small" @click="resumeAllStrategies" block>
                    恢复所有策略
                  </el-button>
                  <el-button type="warning" size="small" @click="clearAllPositions" block>
                    清除所有持仓
                  </el-button>
                  <el-button type="info" size="small" @click="exportLogs" block>
                    导出日志
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 订单流水 -->
      <el-card class="orders-panel" style="margin-top: 16px;">
        <template #header>
          <div class="card-header">
            <span>实时订单流</span>
            <div class="orders-stats">
              <span>今日: {{ orderStats.today }}</span>
              <span>成功: {{ orderStats.success }}</span>
              <span>失败: {{ orderStats.failed }}</span>
              <span>成交率: {{ orderStats.fillRate }}%</span>
            </div>
          </div>
        </template>
        
        <div class="orders-stream">
          <div class="stream-header">
            <div class="header-item">时间</div>
            <div class="header-item">策略</div>
            <div class="header-item">代码</div>
            <div class="header-item">方向</div>
            <div class="header-item">数量</div>
            <div class="header-item">价格</div>
            <div class="header-item">状态</div>
            <div class="header-item">延迟</div>
          </div>
          <div class="stream-content">
            <div
              v-for="order in recentOrders"
              :key="order.id"
              class="stream-item"
              :class="{ success: order.status === 'filled', failed: order.status === 'rejected' }"
            >
              <div class="item-field">{{ formatTime(order.timestamp) }}</div>
              <div class="item-field">{{ order.strategy }}</div>
              <div class="item-field">{{ order.symbol }}</div>
              <div class="item-field">
                <el-tag :type="order.side === 'buy' ? 'danger' : 'success'" size="small">
                  {{ order.side === 'buy' ? '买' : '卖' }}
                </el-tag>
              </div>
              <div class="item-field">{{ order.quantity }}</div>
              <div class="item-field">{{ order.price.toFixed(2) }}</div>
              <div class="item-field">
                <el-tag :type="getOrderStatusType(order.status)" size="small">
                  {{ getOrderStatusText(order.status) }}
                </el-tag>
              </div>
              <div class="item-field">{{ order.latency }}ms</div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 创建策略弹窗 -->
    <el-dialog
      v-model="showCreateStrategy"
      title="创建高频策略"
      width="600px"
      :close-on-click-modal="false"
    >
      <CreateStrategyForm
        @strategy-created="handleStrategyCreated"
        @cancel="showCreateStrategy = false"
      />
    </el-dialog>

    <!-- 编辑策略弹窗 -->
    <el-dialog
      v-model="showEditStrategy"
      title="编辑策略"
      width="600px"
      :close-on-click-modal="false"
    >
      <EditStrategyForm
        v-if="editingStrategy"
        :strategy="editingStrategy"
        @strategy-updated="handleStrategyUpdated"
        @cancel="showEditStrategy = false"
      />
    </el-dialog>

    <!-- 系统设置弹窗 -->
    <el-dialog
      v-model="showSettings"
      title="系统设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <SystemSettingsForm
        :settings="systemSettings"
        @settings-updated="handleSettingsUpdated"
        @cancel="showSettings = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Lightning, VideoPlay, VideoPause, Warning, Plus, Refresh, Setting
} from '@element-plus/icons-vue'

// Props
interface Props {
  account: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'strategy-start': [strategy: any]
  'strategy-stop': [strategy: any]
  'order-placed': [order: any]
}>()

// 响应式数据
const systemStatus = ref<'running' | 'stopped'>('stopped')
const systemLoading = ref(false)
const latency = ref(2.3)
const throughput = ref(1250)
const selectedStrategy = ref(null)
const selectedChartPeriod = ref('1m')

const showCreateStrategy = ref(false)
const showEditStrategy = ref(false)
const showSettings = ref(false)
const editingStrategy = ref(null)

// 系统设置
const systemSettings = reactive({
  refreshRate: 200,
  maxConcurrency: 100,
  timeout: 1000
})

// 风险设置
const riskSettings = reactive({
  maxLoss: 10000,
  maxPosition: 100000,
  stopLossPercent: 2.0
})

// 实时指标
const metrics = reactive({
  totalOrders: 15280,
  ordersChange: 125,
  fillRate: 98.5,
  fillRateChange: 0.3,
  latencyChange: -0.5,
  todayPnl: 8520.30,
  pnlChange: 1250.80
})

// 订单统计
const orderStats = reactive({
  today: 15280,
  success: 15042,
  failed: 238,
  fillRate: 98.5
})

// 策略列表
const strategies = ref([
  {
    id: 'hft_001',
    name: '套利策略A',
    type: '统计套利',
    status: 'running',
    profitRate: 2.35,
    todayOrders: 1250,
    successRate: 98.2
  },
  {
    id: 'hft_002',
    name: '做市策略B',
    type: '做市商',
    status: 'stopped',
    profitRate: 1.88,
    todayOrders: 980,
    successRate: 97.8
  },
  {
    id: 'hft_003',
    name: '动量策略C',
    type: '动量捕捉',
    status: 'running',
    profitRate: -0.45,
    todayOrders: 720,
    successRate: 96.5
  }
])

// 最近订单
const recentOrders = ref([
  {
    id: 'ord_001',
    timestamp: Date.now() - 1000,
    strategy: '套利策略A',
    symbol: '000001',
    side: 'buy',
    quantity: 1000,
    price: 13.25,
    status: 'filled',
    latency: 2.1
  },
  {
    id: 'ord_002',
    timestamp: Date.now() - 2000,
    strategy: '做市策略B',
    symbol: '000002',
    side: 'sell',
    quantity: 500,
    price: 25.80,
    status: 'filled',
    latency: 1.8
  },
  {
    id: 'ord_003',
    timestamp: Date.now() - 3000,
    strategy: '动量策略C',
    symbol: '600036',
    side: 'buy',
    quantity: 2000,
    price: 45.60,
    status: 'rejected',
    latency: 3.2
  }
])

// 图表周期选项
const chartPeriods = [
  { label: '1分钟', value: '1m' },
  { label: '5分钟', value: '5m' },
  { label: '15分钟', value: '15m' },
  { label: '1小时', value: '1h' }
]

// 计算属性
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(amount)
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 方法
const toggleSystem = async () => {
  systemLoading.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    if (systemStatus.value === 'running') {
      systemStatus.value = 'stopped'
      ElMessage.success('高频交易系统已停止')
    } else {
      systemStatus.value = 'running'
      ElMessage.success('高频交易系统已启动')
      startRealTimeUpdate()
    }
  } catch (error) {
    ElMessage.error('系统状态切换失败')
  } finally {
    systemLoading.value = false
  }
}

const emergencyStop = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要紧急停止所有高频交易吗？这将立即停止所有策略和订单。',
      '紧急停止',
      {
        confirmButtonText: '确认停止',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )
    
    systemStatus.value = 'stopped'
    strategies.value.forEach(s => s.status = 'stopped')
    stopRealTimeUpdate()
    
    ElMessage.success('紧急停止成功，所有策略已停止')
  } catch (error) {
    // 用户取消
  }
}

const selectStrategy = (strategy: any) => {
  selectedStrategy.value = strategy
}

const toggleStrategy = async (strategy: any) => {
  const action = strategy.status === 'running' ? '停止' : '启动'
  
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (strategy.status === 'running') {
      strategy.status = 'stopped'
      emit('strategy-stop', strategy)
    } else {
      strategy.status = 'running'
      emit('strategy-start', strategy)
    }
    
    ElMessage.success(`策略 ${strategy.name} ${action}成功`)
  } catch (error) {
    ElMessage.error(`策略${action}失败`)
  }
}

const editStrategy = (strategy: any) => {
  editingStrategy.value = { ...strategy }
  showEditStrategy.value = true
}

const refreshMonitor = () => {
  // 刷新监控数据
  metrics.ordersChange = Math.floor(Math.random() * 200)
  metrics.fillRateChange = (Math.random() - 0.5) * 2
  metrics.latencyChange = (Math.random() - 0.5) * 2
  
  latency.value = Math.max(0.5, latency.value + metrics.latencyChange)
  throughput.value = Math.floor(1000 + Math.random() * 500)
  
  ElMessage.success('监控数据已刷新')
}

const pauseAllStrategies = () => {
  strategies.value.forEach(s => s.status = 'stopped')
  ElMessage.info('所有策略已暂停')
}

const resumeAllStrategies = () => {
  strategies.value.forEach(s => s.status = 'running')
  ElMessage.success('所有策略已恢复')
}

const clearAllPositions = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有持仓吗？这将发送市价单平仓所有持仓。',
      '清除持仓',
      {
        confirmButtonText: '确认清除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('持仓清除指令已发送')
  } catch (error) {
    // 用户取消
  }
}

const exportLogs = () => {
  ElMessage.info('日志导出功能开发中')
}

const handleStrategyCreated = (strategy: any) => {
  strategies.value.push(strategy)
  showCreateStrategy.value = false
  ElMessage.success('策略创建成功')
}

const handleStrategyUpdated = (strategy: any) => {
  const index = strategies.value.findIndex(s => s.id === strategy.id)
  if (index !== -1) {
    strategies.value[index] = strategy
  }
  showEditStrategy.value = false
  ElMessage.success('策略更新成功')
}

const handleSettingsUpdated = (settings: any) => {
  Object.assign(systemSettings, settings)
  showSettings.value = false
  ElMessage.success('系统设置已更新')
}

// 状态辅助方法
const getStrategyStatusType = (status: string) => {
  const types: Record<string, string> = {
    running: 'success',
    stopped: 'info',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getStrategyStatusText = (status: string) => {
  const texts: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    error: '错误'
  }
  return texts[status] || status
}

const getOrderStatusType = (status: string) => {
  const types: Record<string, string> = {
    filled: 'success',
    pending: 'warning',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const texts: Record<string, string> = {
    filled: '已成交',
    pending: '待成交',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

// 实时更新
let updateTimer: NodeJS.Timeout | null = null

const startRealTimeUpdate = () => {
  stopRealTimeUpdate()
  updateTimer = setInterval(() => {
    // 更新延迟
    latency.value = Math.max(0.5, 2 + (Math.random() - 0.5) * 2)
    
    // 更新吞吐量
    throughput.value = Math.floor(1000 + Math.random() * 500)
    
    // 模拟新订单
    if (Math.random() < 0.3) {
      const newOrder = {
        id: `ord_${Date.now()}`,
        timestamp: Date.now(),
        strategy: strategies.value[Math.floor(Math.random() * strategies.value.length)].name,
        symbol: ['000001', '000002', '600036'][Math.floor(Math.random() * 3)],
        side: Math.random() > 0.5 ? 'buy' : 'sell',
        quantity: Math.floor(Math.random() * 2000) + 100,
        price: Math.random() * 50 + 10,
        status: Math.random() > 0.02 ? 'filled' : 'rejected',
        latency: Math.random() * 5 + 1
      }
      
      recentOrders.value.unshift(newOrder)
      if (recentOrders.value.length > 50) {
        recentOrders.value.pop()
      }
      
      // 更新统计
      orderStats.today++
      if (newOrder.status === 'filled') {
        orderStats.success++
      } else {
        orderStats.failed++
      }
      orderStats.fillRate = (orderStats.success / orderStats.today) * 100
    }
  }, systemSettings.refreshRate)
}

const stopRealTimeUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
  }
}

// 生命周期
onMounted(() => {
  console.log('高频交易模块已加载')
})

onUnmounted(() => {
  stopRealTimeUpdate()
})
</script>

<style scoped lang="scss">
.high-freq-trading {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;

  .status-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .module-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }

    .system-status {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;

      .latency, .throughput {
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: auto;
}

.strategy-panel {
  height: 400px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .strategy-list {
    height: 320px;
    overflow-y: auto;
  }

  .strategy-item {
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      border-color: #409eff;
      background: #f0f9ff;
    }

    &.active {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .strategy-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .strategy-name {
        font-weight: 600;
        color: #303133;
      }
    }

    .strategy-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 13px;
      color: #606266;

      .strategy-performance {
        .profit {
          color: #67c23a;
        }
        .loss {
          color: #f56c6c;
        }
      }
    }

    .strategy-actions {
      display: flex;
      justify-content: flex-end;
    }
  }
}

.monitor-panel {
  height: 400px;

  .metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;

    .metric-card {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      text-align: center;

      .metric-label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;

        &.profit {
          color: #67c23a;
        }
        &.loss {
          color: #f56c6c;
        }
      }

      .metric-change {
        font-size: 12px;

        &.positive {
          color: #67c23a;
        }
        &.negative {
          color: #f56c6c;
        }
      }
    }
  }

  .chart-container {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }

    .mock-chart {
      height: 160px;
      background: #f8f9fa;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .chart-line {
        width: 80%;
        height: 2px;
        background: linear-gradient(90deg, #67c23a, #409eff, #f56c6c);
        border-radius: 1px;
      }

      .chart-info {
        position: absolute;
        bottom: 12px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.control-panel {
  height: 400px;

  .control-sections {
    height: 340px;
    overflow-y: auto;

    .control-section {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 6px;
      }

      .quick-actions {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }
    }
  }
}

.orders-panel {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .orders-stats {
      display: flex;
      gap: 16px;
      font-size: 14px;
      color: #606266;
    }
  }

  .orders-stream {
    .stream-header {
      display: grid;
      grid-template-columns: 80px 100px 80px 60px 80px 80px 80px 60px;
      gap: 8px;
      padding: 8px 0;
      border-bottom: 1px solid #e4e7ed;
      font-weight: 600;
      font-size: 13px;
      color: #606266;

      .header-item {
        text-align: center;
      }
    }

    .stream-content {
      max-height: 200px;
      overflow-y: auto;

      .stream-item {
        display: grid;
        grid-template-columns: 80px 100px 80px 60px 80px 80px 80px 60px;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
        font-size: 12px;
        transition: background-color 0.2s;

        &:hover {
          background: #f8f9fa;
        }

        &.success {
          background: rgba(103, 194, 58, 0.05);
        }

        &.failed {
          background: rgba(245, 108, 108, 0.05);
        }

        .item-field {
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content {
    .el-row {
      .el-col {
        margin-bottom: 16px;
      }
    }
  }
}
</style>