# Kubernetes 部署指南

本目录包含量化投资平台的 Kubernetes 部署配置文件。

## 📁 文件说明

- `namespace.yaml` - 创建专用命名空间
- `configmap.yaml` - 应用配置（非敏感）
- `secret.yaml` - 敏感配置（需要修改默认值）
- `postgres-deployment.yaml` - PostgreSQL 数据库部署
- `redis-deployment.yaml` - Redis 缓存部署
- `backend-deployment.yaml` - 后端服务部署
- `frontend-deployment.yaml` - 前端服务部署
- `celery-deployment.yaml` - Celery 异步任务部署
- `ingress.yaml` - Ingress 路由配置

## 🚀 快速部署

### 1. 前置要求

- Kubernetes 集群 (1.19+)
- kubectl 命令行工具
- Ingress Controller (如 nginx-ingress)
- 存储类 (StorageClass) 配置

### 2. 修改配置

#### 修改 secret.yaml
```bash
# 生成安全的密钥
kubectl create secret generic quant-secret \
  --from-literal=DATABASE_USER=quant_user \
  --from-literal=DATABASE_PASSWORD=$(openssl rand -hex 16) \
  --from-literal=SECRET_KEY=$(openssl rand -hex 32) \
  --from-literal=JWT_SECRET_KEY=$(openssl rand -hex 32) \
  --dry-run=client -o yaml > k8s/secret.yaml
```

#### 修改 ingress.yaml
- 将 `quant.example.com` 替换为您的域名
- 如需 HTTPS，取消注释 TLS 相关配置

### 3. 部署步骤

```bash
# 创建命名空间
kubectl apply -f k8s/namespace.yaml

# 创建配置和密钥
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secret.yaml

# 部署数据库和缓存
kubectl apply -f k8s/postgres-deployment.yaml
kubectl apply -f k8s/redis-deployment.yaml

# 等待数据库就绪
kubectl wait --for=condition=ready pod -l app=postgres -n quant-platform --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n quant-platform --timeout=300s

# 部署应用服务
kubectl apply -f k8s/backend-deployment.yaml
kubectl apply -f k8s/frontend-deployment.yaml
kubectl apply -f k8s/celery-deployment.yaml

# 配置 Ingress
kubectl apply -f k8s/ingress.yaml
```

### 4. 验证部署

```bash
# 查看所有资源
kubectl get all -n quant-platform

# 查看 Pod 状态
kubectl get pods -n quant-platform

# 查看服务
kubectl get svc -n quant-platform

# 查看 Ingress
kubectl get ingress -n quant-platform

# 查看日志
kubectl logs -f deployment/quant-backend -n quant-platform
```

## 🔧 运维管理

### 扩缩容

```bash
# 手动扩容后端服务
kubectl scale deployment quant-backend --replicas=5 -n quant-platform

# 查看 HPA 状态
kubectl get hpa -n quant-platform
```

### 更新部署

```bash
# 更新后端镜像
kubectl set image deployment/quant-backend backend=quant-platform/backend:v2.0 -n quant-platform

# 查看更新状态
kubectl rollout status deployment/quant-backend -n quant-platform

# 回滚到上一版本
kubectl rollout undo deployment/quant-backend -n quant-platform
```

### 数据库操作

```bash
# 连接到数据库
kubectl exec -it deployment/postgres -n quant-platform -- psql -U quant_user -d quant_platform

# 执行数据库迁移
kubectl exec deployment/quant-backend -n quant-platform -- alembic upgrade head
```

### 监控和日志

```bash
# 实时查看日志
kubectl logs -f deployment/quant-backend -n quant-platform

# 查看资源使用情况
kubectl top pods -n quant-platform
kubectl top nodes
```

## 📊 监控集成

### Prometheus 监控

1. 安装 Prometheus Operator
2. 创建 ServiceMonitor 资源
3. 配置 Grafana 仪表板

### 日志收集

1. 部署 Elasticsearch + Kibana
2. 安装 Fluent Bit 或 Fluentd
3. 配置日志解析规则

## 🔒 安全建议

1. **密钥管理**
   - 使用 Kubernetes Secrets 管理敏感信息
   - 考虑使用 Sealed Secrets 或 External Secrets
   - 定期轮换密钥

2. **网络策略**
   - 实施 NetworkPolicy 限制 Pod 间通信
   - 只允许必要的端口和协议

3. **RBAC**
   - 为不同角色创建适当的权限
   - 遵循最小权限原则

4. **镜像安全**
   - 使用私有镜像仓库
   - 扫描镜像漏洞
   - 使用特定版本标签，避免使用 latest

## 🚨 故障排查

### Pod 无法启动
```bash
kubectl describe pod <pod-name> -n quant-platform
kubectl logs <pod-name> -n quant-platform --previous
```

### 服务无法访问
```bash
# 检查服务端点
kubectl get endpoints -n quant-platform

# 测试服务连通性
kubectl run test-pod --image=busybox -it --rm -- /bin/sh
```

### 存储问题
```bash
# 检查 PVC 状态
kubectl get pvc -n quant-platform

# 查看存储类
kubectl get storageclass
```

## 📝 备份策略

### 数据库备份
```bash
# 创建数据库备份
kubectl exec deployment/postgres -n quant-platform -- pg_dump -U quant_user quant_platform > backup.sql

# 恢复数据库
kubectl exec -i deployment/postgres -n quant-platform -- psql -U quant_user quant_platform < backup.sql
```

### 持久卷备份
- 使用 Velero 等工具进行定期备份
- 配置跨区域复制

## 🔄 升级指南

1. 备份数据库和配置
2. 在测试环境验证新版本
3. 使用蓝绿部署或金丝雀发布
4. 监控关键指标
5. 准备回滚计划