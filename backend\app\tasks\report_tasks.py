"""
报告生成任务
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from celery import Celery
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_async_session
from app.db.models.trading import Order, Trade
from app.db.models.backtest import BacktestResult
from app.db.models.user import User
from app.services.email_service import EmailService
from app.services.report_service import ReportService
from app.tasks.celery_app import celery_app

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, max_retries=3)
def generate_daily_report(self):
    """生成日终报告"""
    try:
        logger.info("开始生成日终报告")

        # 使用asyncio运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_generate_daily_report())
            logger.info(f"日终报告生成完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"生成日终报告失败: {e}")
        raise self.retry(exc=e, countdown=60)


async def _generate_daily_report() -> Dict:
    """异步生成日终报告"""
    async with get_async_session() as session:
        today = datetime.now().date()
        yesterday = today - timedelta(days=1)

        # 获取交易统计
        trading_stats = await _get_trading_stats(session, yesterday)

        # 获取用户统计
        user_stats = await _get_user_stats(session, yesterday)

        # 获取回测统计
        backtest_stats = await _get_backtest_stats(session, yesterday)

        # 获取系统性能统计
        system_stats = await _get_system_stats(session, yesterday)

        report_data = {
            "date": yesterday.isoformat(),
            "trading_stats": trading_stats,
            "user_stats": user_stats,
            "backtest_stats": backtest_stats,
            "system_stats": system_stats,
            "generated_at": datetime.now().isoformat(),
        }

        # 保存报告
        report_service = ReportService()
        report_id = await report_service.save_daily_report(report_data)

        # 发送邮件通知
        await _send_report_notification(report_data, "daily")

        return {"report_id": report_id, "status": "success", "data": report_data}


@celery_app.task(bind=True, max_retries=3)
def generate_weekly_report(self):
    """生成周报"""
    try:
        logger.info("开始生成周报")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_generate_weekly_report())
            logger.info(f"周报生成完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"生成周报失败: {e}")
        raise self.retry(exc=e, countdown=300)


async def _generate_weekly_report() -> Dict:
    """异步生成周报"""
    async with get_async_session() as session:
        today = datetime.now().date()
        week_start = today - timedelta(days=today.weekday() + 7)  # 上周一
        week_end = week_start + timedelta(days=6)  # 上周日

        # 获取一周的交易统计
        trading_stats = await _get_trading_stats_range(session, week_start, week_end)

        # 获取用户活跃度统计
        user_activity = await _get_user_activity_stats(session, week_start, week_end)

        # 获取策略表现统计
        strategy_performance = await _get_strategy_performance(
            session, week_start, week_end
        )

        # 获取风险指标
        risk_metrics = await _get_risk_metrics(session, week_start, week_end)

        report_data = {
            "week_start": week_start.isoformat(),
            "week_end": week_end.isoformat(),
            "trading_stats": trading_stats,
            "user_activity": user_activity,
            "strategy_performance": strategy_performance,
            "risk_metrics": risk_metrics,
            "generated_at": datetime.now().isoformat(),
        }

        # 保存报告
        report_service = ReportService()
        report_id = await report_service.save_weekly_report(report_data)

        # 发送邮件通知
        await _send_report_notification(report_data, "weekly")

        return {"report_id": report_id, "status": "success", "data": report_data}


@celery_app.task(bind=True, max_retries=3)
def generate_monthly_report(self):
    """生成月报"""
    try:
        logger.info("开始生成月报")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_generate_monthly_report())
            logger.info(f"月报生成完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"生成月报失败: {e}")
        raise self.retry(exc=e, countdown=600)


async def _generate_monthly_report() -> Dict:
    """异步生成月报"""
    async with get_async_session() as session:
        today = datetime.now().date()
        # 上个月的第一天和最后一天
        if today.month == 1:
            last_month = 12
            year = today.year - 1
        else:
            last_month = today.month - 1
            year = today.year

        month_start = datetime(year, last_month, 1).date()
        if last_month == 12:
            month_end = datetime(year + 1, 1, 1).date() - timedelta(days=1)
        else:
            month_end = datetime(year, last_month + 1, 1).date() - timedelta(days=1)

        # 获取月度交易统计
        trading_stats = await _get_trading_stats_range(session, month_start, month_end)

        # 获取用户增长统计
        user_growth = await _get_user_growth_stats(session, month_start, month_end)

        # 获取收益分析
        profit_analysis = await _get_profit_analysis(session, month_start, month_end)

        # 获取市场分析
        market_analysis = await _get_market_analysis(session, month_start, month_end)

        report_data = {
            "month_start": month_start.isoformat(),
            "month_end": month_end.isoformat(),
            "trading_stats": trading_stats,
            "user_growth": user_growth,
            "profit_analysis": profit_analysis,
            "market_analysis": market_analysis,
            "generated_at": datetime.now().isoformat(),
        }

        # 保存报告
        report_service = ReportService()
        report_id = await report_service.save_monthly_report(report_data)

        # 发送邮件通知
        await _send_report_notification(report_data, "monthly")

        return {"report_id": report_id, "status": "success", "data": report_data}


@celery_app.task(bind=True)
def generate_custom_report(self, report_config: Dict):
    """生成自定义报告"""
    try:
        logger.info(f"开始生成自定义报告: {report_config}")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(_generate_custom_report(report_config))
            logger.info(f"自定义报告生成完成: {result}")
            return result
        finally:
            loop.close()

    except Exception as e:
        logger.error(f"生成自定义报告失败: {e}")
        raise


async def _generate_custom_report(report_config: Dict) -> Dict:
    """异步生成自定义报告"""
    async with get_async_session() as session:
        start_date = datetime.fromisoformat(report_config["start_date"]).date()
        end_date = datetime.fromisoformat(report_config["end_date"]).date()

        report_data = {
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "config": report_config,
            "generated_at": datetime.now().isoformat(),
        }

        # 根据配置生成不同的报告模块
        if report_config.get("include_trading", True):
            report_data["trading_stats"] = await _get_trading_stats_range(
                session, start_date, end_date
            )

        if report_config.get("include_users", True):
            report_data["user_stats"] = await _get_user_activity_stats(
                session, start_date, end_date
            )

        if report_config.get("include_backtest", True):
            report_data["backtest_stats"] = await _get_backtest_stats_range(
                session, start_date, end_date
            )

        if report_config.get("include_risk", True):
            report_data["risk_metrics"] = await _get_risk_metrics(
                session, start_date, end_date
            )

        # 保存报告
        report_service = ReportService()
        report_id = await report_service.save_custom_report(report_data)

        return {"report_id": report_id, "status": "success", "data": report_data}


# 辅助函数
async def _get_trading_stats(session: AsyncSession, date) -> Dict:
    """获取交易统计"""
    # 订单统计
    order_query = select(
        func.count(Order.id).label("total_orders"),
        func.sum(Order.volume).label("total_volume"),
        func.count(Order.id).filter(Order.status == "filled").label("filled_orders"),
        func.count(Order.id)
        .filter(Order.status == "cancelled")
        .label("cancelled_orders"),
    ).where(func.date(Order.created_at) == date)

    order_result = await session.execute(order_query)
    order_stats = order_result.first()

    # 成交统计
    trade_query = select(
        func.count(Trade.id).label("total_trades"),
        func.sum(Trade.volume).label("total_trade_volume"),
        func.sum(Trade.amount).label("total_trade_amount"),
    ).where(func.date(Trade.created_at) == date)

    trade_result = await session.execute(trade_query)
    trade_stats = trade_result.first()

    return {
        "orders": {
            "total": order_stats.total_orders or 0,
            "filled": order_stats.filled_orders or 0,
            "cancelled": order_stats.cancelled_orders or 0,
            "total_volume": float(order_stats.total_volume or 0),
        },
        "trades": {
            "total": trade_stats.total_trades or 0,
            "total_volume": float(trade_stats.total_trade_volume or 0),
            "total_amount": float(trade_stats.total_trade_amount or 0),
        },
    }


async def _get_trading_stats_range(session: AsyncSession, start_date, end_date) -> Dict:
    """获取时间范围内的交易统计"""
    # 这里实现范围查询逻辑
    # 类似于 _get_trading_stats 但使用日期范围
    pass


async def _get_user_stats(session: AsyncSession, date) -> Dict:
    """获取用户统计"""
    user_query = select(
        func.count(User.id).label("total_users"),
        func.count(User.id)
        .filter(func.date(User.last_login) == date)
        .label("active_users"),
        func.count(User.id)
        .filter(func.date(User.created_at) == date)
        .label("new_users"),
    )

    result = await session.execute(user_query)
    stats = result.first()

    return {
        "total_users": stats.total_users or 0,
        "active_users": stats.active_users or 0,
        "new_users": stats.new_users or 0,
    }


async def _get_user_activity_stats(session: AsyncSession, start_date, end_date) -> Dict:
    """获取用户活跃度统计"""
    # 实现用户活跃度统计逻辑
    pass


async def _get_backtest_stats(session: AsyncSession, date) -> Dict:
    """获取回测统计"""
    backtest_query = select(
        func.count(BacktestResult.id).label("total_backtests"),
        func.count(BacktestResult.id)
        .filter(BacktestResult.status == "completed")
        .label("completed_backtests"),
        func.avg(BacktestResult.total_return).label("avg_return"),
    ).where(func.date(BacktestResult.created_at) == date)

    result = await session.execute(backtest_query)
    stats = result.first()

    return {
        "total_backtests": stats.total_backtests or 0,
        "completed_backtests": stats.completed_backtests or 0,
        "average_return": float(stats.avg_return or 0),
    }


async def _get_backtest_stats_range(
    session: AsyncSession, start_date, end_date
) -> Dict:
    """获取时间范围内的回测统计"""
    # 实现范围查询逻辑
    pass


async def _get_system_stats(session: AsyncSession, date) -> Dict:
    """获取系统性能统计"""
    # 这里可以集成系统监控数据
    return {
        "cpu_usage": 0.0,
        "memory_usage": 0.0,
        "disk_usage": 0.0,
        "api_requests": 0,
        "response_time": 0.0,
    }


async def _get_strategy_performance(
    session: AsyncSession, start_date, end_date
) -> Dict:
    """获取策略表现统计"""
    # 实现策略表现统计逻辑
    pass


async def _get_risk_metrics(session: AsyncSession, start_date, end_date) -> Dict:
    """获取风险指标"""
    # 实现风险指标计算逻辑
    pass


async def _get_user_growth_stats(session: AsyncSession, start_date, end_date) -> Dict:
    """获取用户增长统计"""
    # 实现用户增长统计逻辑
    pass


async def _get_profit_analysis(session: AsyncSession, start_date, end_date) -> Dict:
    """获取收益分析"""
    # 实现收益分析逻辑
    pass


async def _get_market_analysis(session: AsyncSession, start_date, end_date) -> Dict:
    """获取市场分析"""
    # 实现市场分析逻辑
    pass


async def _send_report_notification(report_data: Dict, report_type: str):
    """发送报告通知"""
    try:
        email_service = EmailService()

        # 这里可以根据配置获取收件人列表
        recipients = ["<EMAIL>"]  # 从配置或数据库获取

        subject = f"量化平台{report_type}报告 - {report_data.get('date', datetime.now().date())}"

        # 生成邮件内容
        content = _generate_email_content(report_data, report_type)

        await email_service.send_email(recipients, subject, content)

    except Exception as e:
        logger.error(f"发送报告通知失败: {e}")


def _generate_email_content(report_data: Dict, report_type: str) -> str:
    """生成邮件内容"""
    # 这里可以使用模板引擎生成更美观的邮件内容
    content = f"""
    量化平台{report_type}报告
    
    生成时间: {report_data.get('generated_at')}
    
    """

    if "trading_stats" in report_data:
        trading = report_data["trading_stats"]
        content += f"""
    交易统计:
    - 总订单数: {trading['orders']['total']}
    - 成交订单数: {trading['orders']['filled']}
    - 取消订单数: {trading['orders']['cancelled']}
    - 总成交量: {trading['trades']['total_volume']}
    - 总成交金额: {trading['trades']['total_amount']}
    
    """

    if "user_stats" in report_data:
        users = report_data["user_stats"]
        content += f"""
    用户统计:
    - 总用户数: {users['total_users']}
    - 活跃用户数: {users['active_users']}
    - 新增用户数: {users['new_users']}
    
    """

    return content
