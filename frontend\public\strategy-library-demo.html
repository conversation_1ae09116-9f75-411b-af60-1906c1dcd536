<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略文库演示 - 量化投资平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #64748b;
            font-size: 0.9rem;
        }

        .content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            font-size: 1.5rem;
            color: #1e293b;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e2e8f0;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .feature-card {
            background: #f8fafc;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }

        .feature-card h3 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .feature-card p {
            color: #64748b;
            line-height: 1.6;
        }

        .demo-section {
            background: #f1f5f9;
            padding: 30px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .file-structure {
            background: #1e293b;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            overflow-x: auto;
        }

        .folder {
            color: #fbbf24;
        }

        .file {
            color: #34d399;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #e2e8f0;
            color: #475569;
        }

        .btn-secondary:hover {
            background: #cbd5e1;
        }

        .highlight {
            background: #fef3c7;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .path-display {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            border-left: 4px solid #3b82f6;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .stats {
                grid-template-columns: 1fr;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 策略文库</h1>
            <p>浏览和管理本地策略文件库，发现优质量化策略</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">7</div>
                <div class="stat-label">年份范围</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">600+</div>
                <div class="stat-label">策略文件</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">2019-2025</div>
                <div class="stat-label">时间跨度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">100%</div>
                <div class="stat-label">可导入</div>
            </div>
        </div>

        <div class="content">
            <div class="section">
                <h2>📁 数据位置</h2>
                <p>策略文件存储在以下位置：</p>
                <div class="path-display">
                    C:\Users\<USER>\Desktop\quant-platf\data\Strategy
                </div>
                <p>文件按年份组织，每年包含约100个策略文件，涵盖各种量化交易策略。</p>
            </div>

            <div class="section">
                <h2>📊 文件结构</h2>
                <div class="file-structure">
data/Strategy/
├── <span class="folder">2019/</span>
│   ├── <span class="file">2019 (1).txt</span>   # 首板低开策略
│   ├── <span class="file">2019 (2).txt</span>   # 双均线策略
│   ├── <span class="file">2019 (3).txt</span>   # 动量策略
│   └── <span class="file">...</span>            # 更多策略文件
├── <span class="folder">2020/</span>
│   ├── <span class="file">2020 (1).txt</span>
│   └── <span class="file">...</span>
├── <span class="folder">2021/</span>
├── <span class="folder">2022/</span>
├── <span class="folder">2023/</span>
├── <span class="folder">2024/</span>
└── <span class="folder">2025/</span>
                </div>
            </div>

            <div class="section">
                <h2>✨ 主要功能</h2>
                <div class="features">
                    <div class="feature-card">
                        <h3>📂 按年份浏览</h3>
                        <p>策略文件按年份组织，可以快速浏览不同年份的策略，了解策略发展趋势。</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔍 智能搜索</h3>
                        <p>支持按策略标题、作者、内容关键词搜索，快速找到感兴趣的策略。</p>
                    </div>
                    <div class="feature-card">
                        <h3>👀 详情预览</h3>
                        <p>点击策略文件可查看详细信息，包括策略代码、作者、来源等。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📥 一键导入</h3>
                        <p>喜欢的策略可以一键导入到个人策略库，进行修改和使用。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📋 列表/网格视图</h3>
                        <p>支持列表和网格两种显示模式，适应不同的浏览习惯。</p>
                    </div>
                    <div class="feature-card">
                        <h3>📄 分页浏览</h3>
                        <p>支持分页显示，可自定义每页显示数量，提升浏览体验。</p>
                    </div>
                </div>
            </div>

            <div class="demo-section">
                <h2>🎯 使用示例</h2>
                <p><strong>场景：</strong>您想找一个关于"双均线"的策略</p>
                <ol style="margin: 15px 0; padding-left: 20px; line-height: 1.8;">
                    <li>在搜索框输入 <span class="highlight">"双均线"</span></li>
                    <li>系统会搜索所有包含该关键词的策略文件</li>
                    <li>点击感兴趣的策略查看详情</li>
                    <li>确认后点击 <span class="highlight">"导入"</span> 按钮</li>
                    <li>策略自动添加到您的个人策略库</li>
                </ol>
            </div>

            <div class="section">
                <h2>🚀 快速开始</h2>
                <p>点击下面的按钮开始使用策略文库功能：</p>
                <div class="buttons">
                    <a href="/strategy/library" class="btn btn-primary">
                        📚 进入策略文库
                    </a>
                    <a href="/strategy/hub" class="btn btn-secondary">
                        🏠 返回策略中心
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 统计数字动画
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                if (!isNaN(parseInt(finalValue))) {
                    let currentValue = 0;
                    const increment = parseInt(finalValue) / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= parseInt(finalValue)) {
                            stat.textContent = finalValue;
                            clearInterval(timer);
                        } else {
                            stat.textContent = Math.floor(currentValue);
                        }
                    }, 30);
                }
            });

            // 卡片悬停效果
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.05)';
                });
            });
        });
    </script>
</body>
</html>
