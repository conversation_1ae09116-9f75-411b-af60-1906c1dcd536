global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'quant-platform'

# 告警规则文件
rule_files:
  - "/etc/prometheus/rules/*.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 后端应用监控
  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  # PostgreSQL监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # Node Exporter (系统监控)
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']

  # Celery监控
  - job_name: 'celery'
    static_configs:
      - targets: ['celery-exporter:9808']