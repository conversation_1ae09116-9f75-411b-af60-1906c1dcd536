<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 量化交易终端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            color: #333;
        }

        .trading-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            text-align: center;
        }

        .header .subtitle {
            text-align: center;
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .trading-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .trading-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .trading-panel:hover {
            transform: translateY(-5px);
        }

        .panel-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .search-section {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .stock-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .stock-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stock-price {
            font-size: 1.8em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 5px;
        }

        .stock-change {
            font-size: 1em;
            color: #27ae60;
        }

        .trading-form {
            display: grid;
            gap: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .form-input {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .trading-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-buy {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .btn-buy:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        }

        .btn-sell {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            color: white;
        }

        .btn-sell:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .orders-table th,
        .orders-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .orders-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .status-pending {
            color: #f39c12;
            font-weight: 600;
        }

        .status-filled {
            color: #27ae60;
            font-weight: 600;
        }

        .status-cancelled {
            color: #e74c3c;
            font-weight: 600;
        }

        .portfolio-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .portfolio-symbol {
            font-weight: 600;
            color: #2c3e50;
        }

        .portfolio-value {
            font-weight: 600;
            color: #27ae60;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }

        .quick-btn {
            padding: 10px 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .quick-btn:hover {
            background: #2980b9;
        }

        .account-summary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .account-balance {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .account-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
        }

        .account-item {
            text-align: center;
        }

        .account-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .account-value {
            font-size: 1.2em;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .trading-grid {
                grid-template-columns: 1fr;
            }
            
            .trading-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="trading-container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 量化交易终端</h1>
            <p class="subtitle">专业级股票交易平台 - 实时行情 • 智能下单 • 风险控制</p>
        </div>

        <!-- 账户概览 -->
        <div class="account-summary">
            <div class="account-balance">¥ 1,000,000.00</div>
            <div class="account-details">
                <div class="account-item">
                    <div class="account-label">可用资金</div>
                    <div class="account-value">¥ 850,000</div>
                </div>
                <div class="account-item">
                    <div class="account-label">持仓市值</div>
                    <div class="account-value">¥ 150,000</div>
                </div>
                <div class="account-item">
                    <div class="account-label">今日盈亏</div>
                    <div class="account-value" style="color: #2ecc71;">+¥ 2,500</div>
                </div>
                <div class="account-item">
                    <div class="account-label">总盈亏</div>
                    <div class="account-value" style="color: #2ecc71;">+¥ 15,800</div>
                </div>
            </div>
        </div>

        <!-- 交易面板 -->
        <div class="trading-grid">
            <!-- 股票搜索和交易 -->
            <div class="trading-panel">
                <div class="panel-title">📈 股票交易</div>
                
                <div class="search-section">
                    <input type="text" class="search-input" placeholder="输入股票代码或名称 (如: 000001, 平安银行)" id="stockSearch">
                </div>

                <div class="stock-info" id="stockInfo">
                    <div class="stock-name">平安银行 (000001.SZ)</div>
                    <div class="stock-price">¥ 12.85</div>
                    <div class="stock-change">+0.25 (+1.98%)</div>
                </div>

                <div class="trading-form">
                    <div class="form-group">
                        <label class="form-label">交易类型</label>
                        <select class="form-input" id="orderType">
                            <option value="limit">限价单</option>
                            <option value="market">市价单</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">价格 (元)</label>
                        <input type="number" class="form-input" placeholder="12.85" step="0.01" id="orderPrice">
                    </div>

                    <div class="form-group">
                        <label class="form-label">数量 (股)</label>
                        <input type="number" class="form-input" placeholder="100" step="100" id="orderQuantity">
                    </div>

                    <div class="trading-buttons">
                        <button class="btn btn-buy" onclick="placeOrder('buy')">买入</button>
                        <button class="btn btn-sell" onclick="placeOrder('sell')">卖出</button>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="quick-btn" onclick="quickFill(100)">100股</button>
                    <button class="quick-btn" onclick="quickFill(500)">500股</button>
                    <button class="quick-btn" onclick="quickFill(1000)">1000股</button>
                    <button class="quick-btn" onclick="quickFill(5000)">5000股</button>
                </div>
            </div>

            <!-- 委托订单 -->
            <div class="trading-panel">
                <div class="panel-title">📋 委托订单</div>
                
                <table class="orders-table">
                    <thead>
                        <tr>
                            <th>股票</th>
                            <th>类型</th>
                            <th>价格</th>
                            <th>数量</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                        <tr>
                            <td>000001.SZ</td>
                            <td>买入</td>
                            <td>¥12.80</td>
                            <td>1000</td>
                            <td class="status-pending">待成交</td>
                        </tr>
                        <tr>
                            <td>600519.SH</td>
                            <td>卖出</td>
                            <td>¥1680.00</td>
                            <td>100</td>
                            <td class="status-filled">已成交</td>
                        </tr>
                        <tr>
                            <td>000858.SZ</td>
                            <td>买入</td>
                            <td>¥8.50</td>
                            <td>2000</td>
                            <td class="status-cancelled">已撤单</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 持仓组合 -->
            <div class="trading-panel">
                <div class="panel-title">💼 持仓组合</div>
                
                <div class="portfolio-item">
                    <div>
                        <div class="portfolio-symbol">平安银行 (000001.SZ)</div>
                        <div style="font-size: 0.9em; color: #666;">1000股 • 成本¥12.50</div>
                    </div>
                    <div class="portfolio-value">¥12,850</div>
                </div>

                <div class="portfolio-item">
                    <div>
                        <div class="portfolio-symbol">贵州茅台 (600519.SH)</div>
                        <div style="font-size: 0.9em; color: #666;">100股 • 成本¥1650.00</div>
                    </div>
                    <div class="portfolio-value">¥168,000</div>
                </div>

                <div class="portfolio-item">
                    <div>
                        <div class="portfolio-symbol">五粮液 (000858.SZ)</div>
                        <div style="font-size: 0.9em; color: #666;">500股 • 成本¥120.00</div>
                    </div>
                    <div class="portfolio-value">¥62,500</div>
                </div>

                <div class="quick-actions">
                    <button class="quick-btn" onclick="refreshPortfolio()">刷新持仓</button>
                    <button class="quick-btn" onclick="exportPortfolio()">导出数据</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 交易终端JavaScript功能
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 股票搜索功能
        document.getElementById('stockSearch').addEventListener('input', async function(e) {
            const keyword = e.target.value.trim();
            if (keyword.length >= 2) {
                await searchStock(keyword);
            }
        });

        // 搜索股票
        async function searchStock(keyword) {
            try {
                const response = await fetch(`${API_BASE}/market/search?keyword=${keyword}`);
                const result = await response.json();
                
                if (result.data && result.data.length > 0) {
                    const stock = result.data[0];
                    updateStockInfo(stock);
                }
            } catch (error) {
                console.error('搜索股票失败:', error);
                // 使用模拟数据
                updateStockInfo({
                    symbol: keyword.toUpperCase(),
                    name: '模拟股票',
                    current_price: 12.85,
                    change: 0.25,
                    change_percent: 1.98
                });
            }
        }

        // 更新股票信息显示
        function updateStockInfo(stock) {
            const stockInfo = document.getElementById('stockInfo');
            const changeClass = stock.change >= 0 ? '#27ae60' : '#e74c3c';
            const changeSign = stock.change >= 0 ? '+' : '';
            
            stockInfo.innerHTML = `
                <div class="stock-name">${stock.name} (${stock.symbol})</div>
                <div class="stock-price">¥ ${stock.current_price?.toFixed(2) || '0.00'}</div>
                <div class="stock-change" style="color: ${changeClass};">
                    ${changeSign}${stock.change?.toFixed(2) || '0.00'} (${changeSign}${stock.change_percent?.toFixed(2) || '0.00'}%)
                </div>
            `;
            
            // 自动填入价格
            document.getElementById('orderPrice').value = stock.current_price?.toFixed(2) || '';
        }

        // 快速填入数量
        function quickFill(quantity) {
            document.getElementById('orderQuantity').value = quantity;
        }

        // 下单功能
        async function placeOrder(side) {
            const symbol = document.getElementById('stockSearch').value || '000001.SZ';
            const orderType = document.getElementById('orderType').value;
            const price = parseFloat(document.getElementById('orderPrice').value);
            const quantity = parseInt(document.getElementById('orderQuantity').value);

            if (!price || !quantity) {
                alert('请输入有效的价格和数量');
                return;
            }

            const orderData = {
                symbol: symbol,
                side: side,
                order_type: orderType,
                price: price,
                quantity: quantity
            };

            try {
                const response = await fetch(`${API_BASE}/trading/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(orderData)
                });

                const result = await response.json();
                
                if (response.ok) {
                    alert(`${side === 'buy' ? '买入' : '卖出'}订单提交成功！`);
                    addOrderToTable(orderData);
                    clearOrderForm();
                } else {
                    alert(`下单失败: ${result.message || '未知错误'}`);
                }
            } catch (error) {
                console.error('下单失败:', error);
                // 模拟成功
                alert(`${side === 'buy' ? '买入' : '卖出'}订单提交成功！(模拟)`);
                addOrderToTable(orderData);
                clearOrderForm();
            }
        }

        // 添加订单到表格
        function addOrderToTable(order) {
            const tbody = document.getElementById('ordersTableBody');
            const row = tbody.insertRow(0);
            
            row.innerHTML = `
                <td>${order.symbol}</td>
                <td>${order.side === 'buy' ? '买入' : '卖出'}</td>
                <td>¥${order.price.toFixed(2)}</td>
                <td>${order.quantity}</td>
                <td class="status-pending">待成交</td>
            `;
        }

        // 清空订单表单
        function clearOrderForm() {
            document.getElementById('orderPrice').value = '';
            document.getElementById('orderQuantity').value = '';
        }

        // 刷新持仓
        async function refreshPortfolio() {
            try {
                const response = await fetch(`${API_BASE}/trading/positions`);
                const result = await response.json();
                
                if (result.data) {
                    console.log('持仓数据:', result.data);
                    alert('持仓数据已刷新');
                }
            } catch (error) {
                console.error('刷新持仓失败:', error);
                alert('持仓数据已刷新 (模拟)');
            }
        }

        // 导出持仓数据
        function exportPortfolio() {
            const portfolioData = [
                ['股票代码', '股票名称', '持仓数量', '成本价', '当前价', '市值', '盈亏'],
                ['000001.SZ', '平安银行', '1000', '12.50', '12.85', '12850', '+350'],
                ['600519.SH', '贵州茅台', '100', '1650.00', '1680.00', '168000', '+3000'],
                ['000858.SZ', '五粮液', '500', '120.00', '125.00', '62500', '+2500']
            ];
            
            const csvContent = portfolioData.map(row => row.join(',')).join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            
            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `portfolio_${new Date().toISOString().split('T')[0]}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 量化交易终端已加载');
            
            // 模拟实时价格更新
            setInterval(function() {
                const priceElement = document.querySelector('.stock-price');
                if (priceElement) {
                    const currentPrice = parseFloat(priceElement.textContent.replace('¥ ', ''));
                    const change = (Math.random() - 0.5) * 0.1;
                    const newPrice = Math.max(0.01, currentPrice + change);
                    priceElement.textContent = `¥ ${newPrice.toFixed(2)}`;
                    
                    // 更新价格输入框
                    const priceInput = document.getElementById('orderPrice');
                    if (!priceInput.value || priceInput.value === currentPrice.toFixed(2)) {
                        priceInput.value = newPrice.toFixed(2);
                    }
                }
            }, 3000);
        });
    </script>
</body>
</html>
