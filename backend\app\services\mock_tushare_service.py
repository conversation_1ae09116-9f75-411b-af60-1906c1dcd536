"""
Mock Tushare数据服务
当Tushare API不可用时，提供模拟数据
"""

import asyncio
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
import pandas as pd

logger = logging.getLogger(__name__)


class MockTushareService:
    """Mock Tushare数据服务"""
    
    def __init__(self):
        self._stock_list = self._generate_mock_stocks()
        self._index_list = {
            "000001.SH": {"name": "上证指数", "base_price": 3000},
            "399001.SZ": {"name": "深证成指", "base_price": 10000},
            "399006.SZ": {"name": "创业板指", "base_price": 2000},
            "000688.SH": {"name": "科创50", "base_price": 1000}
        }
        logger.info("✅ Mock Tushare服务初始化完成")
    
    def _generate_mock_stocks(self) -> List[Dict]:
        """生成模拟股票列表"""
        stocks = []
        
        # 生成一些知名股票
        mock_stocks = [
            {"code": "000001.SZ", "name": "平安银行", "industry": "银行", "area": "深圳"},
            {"code": "000002.SZ", "name": "万科A", "industry": "房地产", "area": "深圳"},
            {"code": "600000.SH", "name": "浦发银行", "industry": "银行", "area": "上海"},
            {"code": "600036.SH", "name": "招商银行", "industry": "银行", "area": "深圳"},
            {"code": "600519.SH", "name": "贵州茅台", "industry": "食品饮料", "area": "贵州"},
            {"code": "000858.SZ", "name": "五粮液", "industry": "食品饮料", "area": "四川"},
            {"code": "600276.SH", "name": "恒瑞医药", "industry": "医药生物", "area": "江苏"},
            {"code": "300059.SZ", "name": "东方财富", "industry": "非银金融", "area": "上海"}
        ]
        
        # 为每只股票生成基础价格
        for stock in mock_stocks:
            stock["base_price"] = random.uniform(10, 200)
            stock["market"] = "主板" if stock["code"].endswith(".SH") else "深市"
            stock["list_date"] = "20100101"
            stocks.append(stock)
        
        return stocks
    
    def _generate_price_data(self, base_price: float, days: int = 250) -> List[Dict]:
        """生成价格数据"""
        data = []
        current_price = base_price
        current_date = datetime.now() - timedelta(days=days)
        
        for i in range(days):
            # 模拟价格波动
            change_percent = random.uniform(-0.08, 0.08)  # -8% 到 +8%
            change = current_price * change_percent
            
            # 计算OHLC
            open_price = current_price
            close_price = current_price + change
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
            low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)
            
            # 成交量 (万手)
            volume = random.uniform(100, 10000)
            amount = volume * close_price * 100  # 成交额
            
            data.append({
                "trade_date": current_date.strftime("%Y%m%d"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "pre_close": round(current_price, 2),
                "change": round(change, 2),
                "pct_chg": round(change_percent * 100, 2),
                "vol": round(volume, 0),
                "amount": round(amount, 0)
            })
            
            current_price = close_price
            current_date += timedelta(days=1)
            
            # 跳过周末
            if current_date.weekday() >= 5:
                current_date += timedelta(days=2)
        
        return data
    
    async def get_stock_basic(self, exchange: str = "", list_status: str = "L") -> pd.DataFrame:
        """获取股票基本信息"""
        try:
            # 模拟API延迟
            await asyncio.sleep(0.1)
            
            data = []
            for stock in self._stock_list:
                if exchange and not stock["code"].endswith(f".{exchange}"):
                    continue
                
                data.append({
                    "ts_code": stock["code"],
                    "symbol": stock["code"][:6],
                    "name": stock["name"],
                    "area": stock["area"],
                    "industry": stock["industry"],
                    "market": stock["market"],
                    "list_date": stock["list_date"]
                })
            
            df = pd.DataFrame(data)
            logger.info(f"📊 返回 {len(df)} 只模拟股票数据")
            return df
            
        except Exception as e:
            logger.error(f"获取模拟股票基本信息失败: {e}")
            return pd.DataFrame()
    
    async def get_daily_data(self, ts_code: str, start_date: str = None, end_date: str = None, limit: int = None) -> pd.DataFrame:
        """获取日线数据"""
        try:
            # 模拟API延迟
            await asyncio.sleep(0.1)
            
            # 找到对应股票
            stock = next((s for s in self._stock_list if s["code"] == ts_code), None)
            if not stock:
                logger.warning(f"未找到股票 {ts_code}")
                return pd.DataFrame()
            
            # 生成价格数据
            days = limit if limit else 250
            price_data = self._generate_price_data(stock["base_price"], days)
            
            # 转换为DataFrame
            df = pd.DataFrame(price_data)
            df["ts_code"] = ts_code
            
            # 按日期排序，最新的在前
            df = df.sort_values("trade_date", ascending=False)
            
            if limit:
                df = df.head(limit)
            
            logger.debug(f"📈 返回 {ts_code} 的 {len(df)} 条模拟日线数据")
            return df
            
        except Exception as e:
            logger.error(f"获取模拟日线数据失败 {ts_code}: {e}")
            return pd.DataFrame()
    
    async def get_index_daily(self, ts_code: str, start_date: str = None, end_date: str = None, limit: int = None) -> pd.DataFrame:
        """获取指数日线数据"""
        try:
            # 模拟API延迟
            await asyncio.sleep(0.1)
            
            # 找到对应指数
            index_info = self._index_list.get(ts_code)
            if not index_info:
                logger.warning(f"未找到指数 {ts_code}")
                return pd.DataFrame()
            
            # 生成价格数据
            days = limit if limit else 250
            price_data = self._generate_price_data(index_info["base_price"], days)
            
            # 转换为DataFrame
            df = pd.DataFrame(price_data)
            df["ts_code"] = ts_code
            
            # 按日期排序，最新的在前
            df = df.sort_values("trade_date", ascending=False)
            
            if limit:
                df = df.head(limit)
            
            logger.debug(f"📊 返回 {ts_code} 的 {len(df)} 条模拟指数数据")
            return df
            
        except Exception as e:
            logger.error(f"获取模拟指数数据失败 {ts_code}: {e}")
            return pd.DataFrame()
    
    async def get_realtime_quotes(self, symbols: List[str]) -> Dict[str, Dict]:
        """获取实时行情（模拟）"""
        quotes = {}
        
        for symbol in symbols:
            try:
                # 获取最新的日线数据作为基础
                df = await self.get_daily_data(symbol, limit=1)
                
                if not df.empty:
                    row = df.iloc[0]
                    
                    # 在收盘价基础上添加实时波动
                    base_price = float(row["close"])
                    real_fluctuation = random.uniform(-0.02, 0.02)  # -2% 到 +2%
                    current_price = base_price * (1 + real_fluctuation)
                    
                    quotes[symbol] = {
                        "symbol": symbol,
                        "price": round(current_price, 2),
                        "prev_close": float(row["close"]),
                        "open": float(row["open"]),
                        "high": max(float(row["high"]), current_price),
                        "low": min(float(row["low"]), current_price),
                        "volume": float(row["vol"]) * 100,
                        "amount": float(row["amount"]) * 1000,
                        "change": round(current_price - base_price, 2),
                        "change_percent": round(real_fluctuation * 100, 2),
                        "time": datetime.now().strftime("%Y%m%d %H:%M:%S"),
                        "type": "stock"
                    }
                else:
                    # 默认数据
                    quotes[symbol] = {
                        "symbol": symbol,
                        "price": 0,
                        "prev_close": 0,
                        "open": 0,
                        "high": 0,
                        "low": 0,
                        "volume": 0,
                        "amount": 0,
                        "change": 0,
                        "change_percent": 0,
                        "time": datetime.now().strftime("%Y%m%d %H:%M:%S"),
                        "type": "stock"
                    }
                    
            except Exception as e:
                logger.warning(f"获取 {symbol} 模拟行情失败: {e}")
        
        logger.info(f"⚡ 返回 {len(quotes)} 个模拟实时行情")
        return quotes
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return {
            "service": "mock_tushare",
            "status": "healthy",
            "message": "模拟数据服务正常",
            "timestamp": time.time(),
            "token_configured": True,
            "api_initialized": True,
            "mock_stocks": len(self._stock_list),
            "mock_indices": len(self._index_list)
        }


# 全局实例
_mock_service = None


def get_mock_tushare_service() -> MockTushareService:
    """获取Mock Tushare服务单例"""
    global _mock_service
    if _mock_service is None:
        _mock_service = MockTushareService()
    return _mock_service