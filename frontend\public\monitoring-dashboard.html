<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📡 系统监控仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: #ecf0f1;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(52, 73, 94, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            text-align: center;
        }

        .header h1 {
            color: #ecf0f1;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #bdc3c7;
            font-size: 1.1em;
        }

        .status-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .status-card {
            background: rgba(52, 73, 94, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-value {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .status-label {
            color: #bdc3c7;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-healthy { color: #2ecc71; }
        .status-warning { color: #f39c12; }
        .status-critical { color: #e74c3c; }

        .monitoring-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .monitor-panel {
            background: rgba(52, 73, 94, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .panel-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #ecf0f1;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .chart-container {
            width: 100%;
            height: 300px;
        }

        .alerts-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .alert-item {
            background: rgba(44, 62, 80, 0.8);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid;
        }

        .alert-critical { border-left-color: #e74c3c; }
        .alert-warning { border-left-color: #f39c12; }
        .alert-info { border-left-color: #3498db; }

        .alert-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .alert-message {
            color: #bdc3c7;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .alert-time {
            color: #95a5a6;
            font-size: 0.8em;
        }

        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .metrics-table th,
        .metrics-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #34495e;
        }

        .metrics-table th {
            background: rgba(44, 62, 80, 0.8);
            font-weight: 600;
            color: #ecf0f1;
        }

        .metrics-table td {
            color: #bdc3c7;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .control-btn:hover {
            background: #2980b9;
        }

        .control-btn.active {
            background: #e74c3c;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #bdc3c7;
            font-size: 1.1em;
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #34495e;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .monitoring-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 头部 -->
        <div class="header">
            <h1>📡 系统监控仪表板</h1>
            <p class="subtitle">实时系统监控 • 性能分析 • 智能告警 • 资源管理</p>
        </div>

        <!-- 状态概览 -->
        <div class="status-bar">
            <div class="status-card">
                <div class="status-value status-healthy" id="systemStatus">正常</div>
                <div class="status-label">系统状态</div>
            </div>
            <div class="status-card">
                <div class="status-value" id="cpuUsage">25.6%</div>
                <div class="status-label">CPU使用率</div>
            </div>
            <div class="status-card">
                <div class="status-value" id="memoryUsage">45.2%</div>
                <div class="status-label">内存使用率</div>
            </div>
            <div class="status-card">
                <div class="status-value" id="diskUsage">68.9%</div>
                <div class="status-label">磁盘使用率</div>
            </div>
            <div class="status-card">
                <div class="status-value status-healthy" id="activeAlerts">0</div>
                <div class="status-label">活跃告警</div>
            </div>
            <div class="status-card">
                <div class="status-value" id="apiRequests">1,247</div>
                <div class="status-label">API请求/分钟</div>
            </div>
        </div>

        <!-- 监控图表 -->
        <div class="monitoring-grid">
            <!-- 系统性能图表 -->
            <div class="monitor-panel">
                <div class="panel-title">📊 系统性能监控</div>
                <div class="controls">
                    <button class="control-btn active" onclick="changeMetric('cpu')">CPU</button>
                    <button class="control-btn" onclick="changeMetric('memory')">内存</button>
                    <button class="control-btn" onclick="changeMetric('disk')">磁盘</button>
                    <button class="control-btn" onclick="refreshMetrics()">刷新</button>
                </div>
                <div class="chart-container" id="performanceChart"></div>
            </div>

            <!-- API监控图表 -->
            <div class="monitor-panel">
                <div class="panel-title">🌐 API监控</div>
                <div class="controls">
                    <button class="control-btn active" onclick="changeApiMetric('requests')">请求量</button>
                    <button class="control-btn" onclick="changeApiMetric('response_time')">响应时间</button>
                    <button class="control-btn" onclick="changeApiMetric('error_rate')">错误率</button>
                </div>
                <div class="chart-container" id="apiChart"></div>
            </div>
        </div>

        <!-- 第二行监控面板 -->
        <div class="monitoring-grid">
            <!-- 告警列表 -->
            <div class="monitor-panel">
                <div class="panel-title">🚨 系统告警</div>
                <div class="controls">
                    <button class="control-btn" onclick="refreshAlerts()">刷新告警</button>
                    <button class="control-btn" onclick="clearResolvedAlerts()">清理已解决</button>
                </div>
                <div class="alerts-list" id="alertsList">
                    <div class="alert-item alert-warning">
                        <div class="alert-title">内存使用率偏高</div>
                        <div class="alert-message">当前内存使用率 45.2%，建议关注</div>
                        <div class="alert-time">2025-08-05 12:15:30</div>
                    </div>
                    <div class="alert-item alert-info">
                        <div class="alert-title">API响应时间正常</div>
                        <div class="alert-message">平均响应时间 150ms，系统运行良好</div>
                        <div class="alert-time">2025-08-05 12:10:15</div>
                    </div>
                </div>
            </div>

            <!-- 系统指标详情 -->
            <div class="monitor-panel">
                <div class="panel-title">📈 系统指标详情</div>
                <table class="metrics-table">
                    <thead>
                        <tr>
                            <th>指标名称</th>
                            <th>当前值</th>
                            <th>状态</th>
                            <th>更新时间</th>
                        </tr>
                    </thead>
                    <tbody id="metricsTableBody">
                        <tr>
                            <td>CPU使用率</td>
                            <td>25.6%</td>
                            <td><span class="status-healthy">正常</span></td>
                            <td>12:15:45</td>
                        </tr>
                        <tr>
                            <td>内存使用率</td>
                            <td>45.2%</td>
                            <td><span class="status-warning">注意</span></td>
                            <td>12:15:45</td>
                        </tr>
                        <tr>
                            <td>磁盘使用率</td>
                            <td>68.9%</td>
                            <td><span class="status-healthy">正常</span></td>
                            <td>12:15:45</td>
                        </tr>
                        <tr>
                            <td>API响应时间</td>
                            <td>150ms</td>
                            <td><span class="status-healthy">正常</span></td>
                            <td>12:15:45</td>
                        </tr>
                        <tr>
                            <td>数据库连接</td>
                            <td>活跃</td>
                            <td><span class="status-healthy">正常</span></td>
                            <td>12:15:45</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE = 'http://localhost:8000/api/v1';
        let performanceChart, apiChart;
        let currentMetric = 'cpu';
        let currentApiMetric = 'requests';

        // 初始化图表
        function initCharts() {
            performanceChart = echarts.init(document.getElementById('performanceChart'));
            apiChart = echarts.init(document.getElementById('apiChart'));

            loadPerformanceData();
            loadApiData();
        }

        // 生成模拟时间序列数据
        function generateTimeSeriesData(points = 30, baseValue = 50, variance = 20) {
            const data = [];
            const now = new Date();
            
            for (let i = points - 1; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60000); // 每分钟一个点
                const value = baseValue + (Math.random() - 0.5) * variance;
                data.push([
                    time.toLocaleTimeString(),
                    Math.max(0, Math.min(100, value)).toFixed(1)
                ]);
            }
            
            return data;
        }

        // 加载性能数据
        async function loadPerformanceData() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/metrics`);
                let data;
                
                if (response.ok) {
                    const result = await response.json();
                    // 使用真实数据或生成模拟数据
                    data = generateTimeSeriesData(30, currentMetric === 'cpu' ? 25 : currentMetric === 'memory' ? 45 : 68, 15);
                } else {
                    data = generateTimeSeriesData(30, currentMetric === 'cpu' ? 25 : currentMetric === 'memory' ? 45 : 68, 15);
                }

                const option = {
                    title: {
                        text: `${currentMetric.toUpperCase()}使用率趋势`,
                        textStyle: { color: '#ecf0f1' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        backgroundColor: 'rgba(44, 62, 80, 0.9)',
                        textStyle: { color: '#ecf0f1' }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: data.map(item => item[0]),
                        axisLine: { lineStyle: { color: '#34495e' } },
                        axisLabel: { color: '#bdc3c7' }
                    },
                    yAxis: {
                        type: 'value',
                        max: 100,
                        axisLine: { lineStyle: { color: '#34495e' } },
                        axisLabel: { color: '#bdc3c7' },
                        splitLine: { lineStyle: { color: '#34495e' } }
                    },
                    series: [
                        {
                            name: `${currentMetric.toUpperCase()}使用率`,
                            type: 'line',
                            data: data.map(item => item[1]),
                            smooth: true,
                            itemStyle: { color: '#3498db' },
                            areaStyle: {
                                color: {
                                    type: 'linear',
                                    x: 0, y: 0, x2: 0, y2: 1,
                                    colorStops: [
                                        { offset: 0, color: 'rgba(52, 152, 219, 0.8)' },
                                        { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
                                    ]
                                }
                            }
                        }
                    ]
                };

                performanceChart.setOption(option);
            } catch (error) {
                console.error('加载性能数据失败:', error);
            }
        }

        // 加载API数据
        async function loadApiData() {
            const data = generateTimeSeriesData(30, 
                currentApiMetric === 'requests' ? 1200 : 
                currentApiMetric === 'response_time' ? 150 : 2, 
                currentApiMetric === 'requests' ? 300 : 
                currentApiMetric === 'response_time' ? 50 : 1);

            const option = {
                title: {
                    text: `API${currentApiMetric === 'requests' ? '请求量' : currentApiMetric === 'response_time' ? '响应时间' : '错误率'}趋势`,
                    textStyle: { color: '#ecf0f1' }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(44, 62, 80, 0.9)',
                    textStyle: { color: '#ecf0f1' }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: data.map(item => item[0]),
                    axisLine: { lineStyle: { color: '#34495e' } },
                    axisLabel: { color: '#bdc3c7' }
                },
                yAxis: {
                    type: 'value',
                    axisLine: { lineStyle: { color: '#34495e' } },
                    axisLabel: { color: '#bdc3c7' },
                    splitLine: { lineStyle: { color: '#34495e' } }
                },
                series: [
                    {
                        name: currentApiMetric,
                        type: 'bar',
                        data: data.map(item => item[1]),
                        itemStyle: { 
                            color: currentApiMetric === 'error_rate' ? '#e74c3c' : '#2ecc71'
                        }
                    }
                ]
            };

            apiChart.setOption(option);
        }

        // 切换性能指标
        function changeMetric(metric) {
            currentMetric = metric;
            
            // 更新按钮状态
            const perfBtns = document.querySelectorAll('.monitor-panel')[0].querySelectorAll('.control-btn');
            perfBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            loadPerformanceData();
        }

        // 切换API指标
        function changeApiMetric(metric) {
            currentApiMetric = metric;
            
            // 更新按钮状态
            const apiBtns = document.querySelectorAll('.monitor-panel')[1].querySelectorAll('.control-btn');
            apiBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            loadApiData();
        }

        // 刷新指标
        function refreshMetrics() {
            loadPerformanceData();
            updateStatusCards();
        }

        // 刷新告警
        async function refreshAlerts() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/alerts`);
                if (response.ok) {
                    const result = await response.json();
                    updateAlertsList(result.data || []);
                }
            } catch (error) {
                console.error('刷新告警失败:', error);
            }
        }

        // 更新告警列表
        function updateAlertsList(alerts) {
            const alertsList = document.getElementById('alertsList');
            
            if (alerts.length === 0) {
                alertsList.innerHTML = '<div style="text-align: center; color: #bdc3c7; padding: 20px;">暂无活跃告警</div>';
                return;
            }
            
            alertsList.innerHTML = alerts.map(alert => `
                <div class="alert-item alert-${alert.level}">
                    <div class="alert-title">${alert.title}</div>
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-time">${alert.timestamp}</div>
                </div>
            `).join('');
        }

        // 清理已解决的告警
        function clearResolvedAlerts() {
            alert('已清理所有已解决的告警');
        }

        // 更新状态卡片
        function updateStatusCards() {
            // 模拟实时数据更新
            const cpuUsage = (Math.random() * 30 + 15).toFixed(1);
            const memoryUsage = (Math.random() * 20 + 35).toFixed(1);
            const diskUsage = (Math.random() * 10 + 65).toFixed(1);
            const apiRequests = Math.floor(Math.random() * 500 + 1000);

            document.getElementById('cpuUsage').textContent = cpuUsage + '%';
            document.getElementById('memoryUsage').textContent = memoryUsage + '%';
            document.getElementById('diskUsage').textContent = diskUsage + '%';
            document.getElementById('apiRequests').textContent = apiRequests.toLocaleString();

            // 更新状态颜色
            updateStatusColor('cpuUsage', parseFloat(cpuUsage));
            updateStatusColor('memoryUsage', parseFloat(memoryUsage));
            updateStatusColor('diskUsage', parseFloat(diskUsage));
        }

        // 更新状态颜色
        function updateStatusColor(elementId, value) {
            const element = document.getElementById(elementId);
            element.className = '';
            
            if (value < 50) {
                element.classList.add('status-healthy');
            } else if (value < 80) {
                element.classList.add('status-warning');
            } else {
                element.classList.add('status-critical');
            }
        }

        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', function() {
            if (performanceChart) performanceChart.resize();
            if (apiChart) apiChart.resize();
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📡 系统监控仪表板已加载');
            initCharts();
            
            // 定时更新数据
            setInterval(function() {
                updateStatusCards();
                loadPerformanceData();
                loadApiData();
            }, 30000); // 每30秒更新一次
            
            // 定时刷新告警
            setInterval(refreshAlerts, 60000); // 每分钟刷新告警
        });
    </script>
</body>
</html>
