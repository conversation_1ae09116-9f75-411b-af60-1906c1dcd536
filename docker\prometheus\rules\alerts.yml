groups:
  - name: quant_platform_alerts
    interval: 30s
    rules:
      # API响应时间过高
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, http_request_duration_seconds_bucket) > 0.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "API响应时间过高"
          description: "95%的请求响应时间超过500ms，当前值: {{ $value }}s"

      # 错误率过高
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.05
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "错误率过高"
          description: "5分钟内错误率超过5%，当前值: {{ $value }}"

      # 数据库连接数过高
      - alert: HighDatabaseConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "数据库连接数超过最大连接数的80%"

      # Redis内存使用率过高
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis内存使用率超过80%"

      # Celery任务积压
      - alert: CeleryTaskBacklog
        expr: celery_tasks_pending > 1000
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Celery任务积压"
          description: "待处理的Celery任务超过1000个"

      # 服务不可用
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务不可用"
          description: "{{ $labels.job }} 服务已下线"