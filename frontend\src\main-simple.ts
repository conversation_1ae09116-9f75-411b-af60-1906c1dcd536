/**
 * 简化版主入口文件 - 用于调试
 */
import { createApp } from 'vue'

// 创建一个简单的测试组件
const TestApp = {
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1 style="color: #409EFF;">🎉 Vue应用正常运行！</h1>
      <p>如果你看到这个页面，说明Vue应用基础功能正常。</p>
      <div style="margin: 20px 0;">
        <h3>基础测试：</h3>
        <p>✅ Vue 3 已加载</p>
        <p>✅ 组件渲染正常</p>
        <p>✅ 响应式数据: {{ count }}</p>
        <button @click="count++" style="padding: 8px 16px; margin: 5px; background: #409EFF; color: white; border: none; border-radius: 4px; cursor: pointer;">
          点击测试 ({{ count }})
        </button>
      </div>
      <div style="margin: 20px 0;">
        <h3>环境信息：</h3>
        <p>🌍 环境: {{ isDev ? '开发环境' : '生产环境' }}</p>
        <p>🔗 API地址: {{ apiUrl }}</p>
        <p>⏰ 当前时间: {{ currentTime }}</p>
      </div>
      <div style="margin: 20px 0;">
        <h3>下一步：</h3>
        <p>如果这个页面正常显示，问题可能在于：</p>
        <ul>
          <li>某个特定的组件或模块导入失败</li>
          <li>路由配置问题</li>
          <li>样式文件加载问题</li>
          <li>第三方库兼容性问题</li>
        </ul>
        <button @click="goToMain" style="padding: 8px 16px; margin: 5px; background: #67C23A; color: white; border: none; border-radius: 4px; cursor: pointer;">
          尝试加载完整应用
        </button>
      </div>
    </div>
  `,
  data() {
    return {
      count: 0,
      currentTime: new Date().toLocaleString('zh-CN'),
      isDev: import.meta.env.DEV,
      apiUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
    }
  },
  mounted() {
    // 每秒更新时间
    setInterval(() => {
      this.currentTime = new Date().toLocaleString('zh-CN')
    }, 1000)
    
    console.log('🎉 简化版应用启动成功！')
    console.log('📊 环境变量:', import.meta.env)
  },
  methods: {
    goToMain() {
      // 尝试加载完整应用
      window.location.href = '/'
    }
  }
}

// 创建应用
const app = createApp(TestApp)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('🚨 Vue错误:', err)
  console.error('📍 错误信息:', info)
  console.error('🔍 组件实例:', instance)
}

// 挂载应用
try {
  app.mount('#app')
  console.log('✅ 应用挂载成功')
} catch (error) {
  console.error('❌ 应用挂载失败:', error)
  
  // 在页面上显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif; background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px;">
        <h2>❌ 应用启动失败</h2>
        <p><strong>错误信息:</strong> ${error.message}</p>
        <p><strong>错误堆栈:</strong></p>
        <pre style="background: #fff; padding: 10px; border-radius: 4px; overflow-x: auto;">${error.stack}</pre>
        <button onclick="window.location.reload()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
          重新加载页面
        </button>
      </div>
    `
  }
}

// 导出应用实例
export default app
