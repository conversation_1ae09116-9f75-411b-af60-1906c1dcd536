import type { RouteRecordRaw } from 'vue-router'

const marketRoutes: RouteRecordRaw[] = [
  {
    path: '/market',
    name: 'market',
    component: () => import('@/views/Market/MarketView.vue'),
    meta: {
      title: '行情中心'
    }
  },
  {
    path: '/market/realtime',
    name: 'realtime-market',
    component: () => import('@/views/Market/MarketView.vue'),
    meta: {
      title: '实时行情'
    }
  },
  {
    path: '/market/historical',
    name: 'historical-data',
    component: () => import('@/views/Market/HistoricalData.vue'),
    meta: {
      title: '历史数据中心'
    }
  },
  {
    path: '/market/:symbol',
    name: 'stock-detail',
    component: () => import('@/views/Market/StockDetail.vue'),
    meta: {
      title: '股票详情'
    },
    props: true
  }
]

export default marketRoutes
