<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端空白页面调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端空白页面调试工具</h1>
        <p>这个页面用于诊断前端应用空白的原因</p>

        <div class="test-section">
            <h3>📋 基础检查</h3>
            <div id="basic-checks"></div>
        </div>

        <div class="test-section">
            <h3>🌐 网络连接测试</h3>
            <button onclick="testAPI()">测试API连接</button>
            <button onclick="testWebSocket()">测试WebSocket连接</button>
            <div id="network-results"></div>
        </div>

        <div class="test-section">
            <h3>🚨 控制台错误</h3>
            <div id="console-errors"></div>
            <button onclick="captureErrors()">捕获错误</button>
        </div>

        <div class="test-section">
            <h3>📊 性能信息</h3>
            <div id="performance-info"></div>
            <button onclick="showPerformance()">显示性能数据</button>
        </div>

        <div class="test-section">
            <h3>🔧 快速修复</h3>
            <button onclick="clearCache()">清除缓存</button>
            <button onclick="reloadPage()">强制刷新</button>
            <button onclick="openMainApp()">打开主应用</button>
        </div>

        <div class="test-section">
            <h3>📝 诊断日志</h3>
            <pre id="debug-log"></pre>
        </div>
    </div>

    <script>
        let debugLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}`;
            debugLog.push(logEntry);
            
            const logElement = document.getElementById('debug-log');
            logElement.textContent = debugLog.join('\n');
            
            console.log(logEntry);
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            element.appendChild(div);
        }

        // 基础检查
        function runBasicChecks() {
            const checks = [
                { name: 'JavaScript支持', test: () => true },
                { name: 'localStorage支持', test: () => typeof Storage !== 'undefined' },
                { name: 'fetch API支持', test: () => typeof fetch !== 'undefined' },
                { name: 'WebSocket支持', test: () => typeof WebSocket !== 'undefined' },
                { name: 'ES6支持', test: () => {
                    try {
                        eval('const test = () => {}');
                        return true;
                    } catch {
                        return false;
                    }
                }}
            ];

            checks.forEach(check => {
                try {
                    const result = check.test();
                    showStatus('basic-checks', 
                        `${check.name}: ${result ? '✅ 支持' : '❌ 不支持'}`, 
                        result ? 'success' : 'error'
                    );
                    log(`${check.name}: ${result ? '支持' : '不支持'}`);
                } catch (error) {
                    showStatus('basic-checks', 
                        `${check.name}: ❌ 检查失败 - ${error.message}`, 
                        'error'
                    );
                    log(`${check.name}: 检查失败 - ${error.message}`);
                }
            });
        }

        // 测试API连接
        async function testAPI() {
            const apiUrl = 'http://localhost:8000/api/v1/health';
            
            try {
                log('开始测试API连接...');
                const response = await fetch(apiUrl);
                
                if (response.ok) {
                    const data = await response.json();
                    showStatus('network-results', 
                        `✅ API连接成功: ${JSON.stringify(data)}`, 
                        'success'
                    );
                    log(`API连接成功: ${JSON.stringify(data)}`);
                } else {
                    showStatus('network-results', 
                        `❌ API响应错误: ${response.status} ${response.statusText}`, 
                        'error'
                    );
                    log(`API响应错误: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                showStatus('network-results', 
                    `❌ API连接失败: ${error.message}`, 
                    'error'
                );
                log(`API连接失败: ${error.message}`);
            }
        }

        // 测试WebSocket连接
        function testWebSocket() {
            const wsUrl = 'ws://localhost:8000/api/v1/ws';
            
            try {
                log('开始测试WebSocket连接...');
                const ws = new WebSocket(wsUrl);
                
                ws.onopen = () => {
                    showStatus('network-results', 
                        '✅ WebSocket连接成功', 
                        'success'
                    );
                    log('WebSocket连接成功');
                    ws.close();
                };
                
                ws.onerror = (error) => {
                    showStatus('network-results', 
                        `❌ WebSocket连接失败: ${error}`, 
                        'error'
                    );
                    log(`WebSocket连接失败: ${error}`);
                };
                
                ws.onclose = () => {
                    log('WebSocket连接已关闭');
                };
                
                // 5秒后超时
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        ws.close();
                        showStatus('network-results', 
                            '❌ WebSocket连接超时', 
                            'error'
                        );
                        log('WebSocket连接超时');
                    }
                }, 5000);
                
            } catch (error) {
                showStatus('network-results', 
                    `❌ WebSocket测试失败: ${error.message}`, 
                    'error'
                );
                log(`WebSocket测试失败: ${error.message}`);
            }
        }

        // 捕获错误
        function captureErrors() {
            const errors = [];
            
            // 捕获已有的控制台错误
            const originalError = console.error;
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            // 显示错误
            setTimeout(() => {
                if (errors.length > 0) {
                    showStatus('console-errors', 
                        `发现 ${errors.length} 个错误`, 
                        'error'
                    );
                    errors.forEach(error => {
                        showStatus('console-errors', error, 'error');
                        log(`控制台错误: ${error}`);
                    });
                } else {
                    showStatus('console-errors', 
                        '✅ 暂无控制台错误', 
                        'success'
                    );
                    log('暂无控制台错误');
                }
            }, 1000);
        }

        // 显示性能数据
        function showPerformance() {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    const loadTime = perfData.loadEventEnd - perfData.navigationStart;
                    const domTime = perfData.domContentLoadedEventEnd - perfData.navigationStart;
                    
                    showStatus('performance-info', 
                        `页面加载时间: ${loadTime}ms`, 
                        loadTime > 3000 ? 'warning' : 'success'
                    );
                    showStatus('performance-info', 
                        `DOM加载时间: ${domTime}ms`, 
                        'info'
                    );
                    
                    log(`页面加载时间: ${loadTime}ms`);
                    log(`DOM加载时间: ${domTime}ms`);
                } else {
                    showStatus('performance-info', 
                        '⚠️ 无法获取性能数据', 
                        'warning'
                    );
                    log('无法获取性能数据');
                }
            } else {
                showStatus('performance-info', 
                    '❌ 浏览器不支持性能API', 
                    'error'
                );
                log('浏览器不支持性能API');
            }
        }

        // 清除缓存
        function clearCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                alert('缓存已清除，建议刷新页面');
                log('缓存已清除');
            } catch (error) {
                alert('清除缓存失败: ' + error.message);
                log('清除缓存失败: ' + error.message);
            }
        }

        // 强制刷新
        function reloadPage() {
            window.location.reload(true);
        }

        // 打开主应用
        function openMainApp() {
            window.open('http://localhost:5173', '_blank');
        }

        // 页面加载完成后运行基础检查
        window.addEventListener('load', () => {
            log('调试页面加载完成');
            runBasicChecks();
        });

        // 捕获全局错误
        window.addEventListener('error', (event) => {
            log(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`);
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`未处理的Promise错误: ${event.reason}`);
        });
    </script>
</body>
</html>
