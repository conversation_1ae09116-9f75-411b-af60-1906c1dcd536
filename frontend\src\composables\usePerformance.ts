/**
 * 性能优化组合式函数
 * 提供常用的性能优化功能
 */

import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { debounce, throttle, PerformanceMonitor, requestIdleCallback } from '@/utils/performance'

/**
 * 使用防抖
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay = 300
) {
  return debounce(fn, delay)
}

/**
 * 使用节流
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  limit = 100
) {
  return throttle(fn, limit)
}

/**
 * 使用性能监控
 */
export function usePerformanceMonitor() {
  const mark = (name: string) => {
    PerformanceMonitor.mark(name)
  }

  const measure = (name: string, startMark: string) => {
    return PerformanceMonitor.measure(name, startMark)
  }

  const getMemoryUsage = () => {
    return PerformanceMonitor.getMemoryUsage()
  }

  const getFID = () => {
    return PerformanceMonitor.getFID()
  }

  return {
    mark,
    measure,
    getMemoryUsage,
    getFID
  }
}

/**
 * 使用懒加载
 */
export function useLazyLoad() {
  const isVisible = ref(false)
  const targetRef = ref<HTMLElement>()

  onMounted(() => {
    if (!targetRef.value) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            isVisible.value = true
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )

    observer.observe(targetRef.value)

    onUnmounted(() => {
      observer.disconnect()
    })
  })

  return {
    isVisible,
    targetRef
  }
}

/**
 * 使用空闲时执行
 */
export function useIdleCallback(callback: () => void, timeout = 5000) {
  onMounted(() => {
    requestIdleCallback(callback, timeout)
  })
}

/**
 * 使用虚拟滚动
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number
) {
  const scrollTop = ref(0)
  const visibleCount = Math.ceil(containerHeight / itemHeight) + 2

  const visibleItems = computed(() => {
    const startIndex = Math.floor(scrollTop.value / itemHeight)
    const endIndex = Math.min(startIndex + visibleCount, items.length)
    
    return {
      items: items.slice(startIndex, endIndex),
      startIndex,
      offsetY: startIndex * itemHeight,
      totalHeight: items.length * itemHeight
    }
  })

  const handleScroll = throttle((event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }, 16)

  return {
    visibleItems,
    handleScroll,
    scrollTop
  }
}

/**
 * 使用响应式优化
 */
export function useResponsiveOptimization() {
  const isLargeScreen = ref(window.innerWidth >= 1200)
  const isMediumScreen = ref(window.innerWidth >= 768)
  const isSmallScreen = ref(window.innerWidth < 768)

  const updateScreenSize = throttle(() => {
    isLargeScreen.value = window.innerWidth >= 1200
    isMediumScreen.value = window.innerWidth >= 768
    isSmallScreen.value = window.innerWidth < 768
  }, 100)

  onMounted(() => {
    window.addEventListener('resize', updateScreenSize, { passive: true })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize)
  })

  return {
    isLargeScreen,
    isMediumScreen,
    isSmallScreen
  }
}

/**
 * 使用图片懒加载
 */
export function useImageLazyLoad() {
  const imageRef = ref<HTMLImageElement>()
  const isLoaded = ref(false)
  const isError = ref(false)

  const loadImage = (src: string) => {
    if (!imageRef.value) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement
            img.src = src
            
            img.onload = () => {
              isLoaded.value = true
              img.classList.add('loaded')
            }
            
            img.onerror = () => {
              isError.value = true
              img.classList.add('error')
            }
            
            observer.unobserve(img)
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )

    observer.observe(imageRef.value)

    onUnmounted(() => {
      observer.disconnect()
    })
  }

  return {
    imageRef,
    isLoaded,
    isError,
    loadImage
  }
}

/**
 * 使用组件缓存
 */
export function useComponentCache<T>(
  key: string,
  factory: () => T,
  ttl = 5 * 60 * 1000 // 5分钟
) {
  const cache = new Map<string, { data: T; timestamp: number }>()

  const get = (): T | null => {
    const cached = cache.get(key)
    if (!cached) return null

    const now = Date.now()
    if (now - cached.timestamp > ttl) {
      cache.delete(key)
      return null
    }

    return cached.data
  }

  const set = (data: T) => {
    cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }

  const getOrCreate = (): T => {
    const cached = get()
    if (cached) return cached

    const data = factory()
    set(data)
    return data
  }

  const clear = () => {
    cache.delete(key)
  }

  return {
    get,
    set,
    getOrCreate,
    clear
  }
}

/**
 * 使用批量更新
 */
export function useBatchUpdate() {
  const updates = ref<(() => void)[]>([])
  const isScheduled = ref(false)

  const scheduleUpdate = (updateFn: () => void) => {
    updates.value.push(updateFn)

    if (!isScheduled.value) {
      isScheduled.value = true
      nextTick(() => {
        const currentUpdates = [...updates.value]
        updates.value = []
        isScheduled.value = false

        currentUpdates.forEach(update => update())
      })
    }
  }

  return {
    scheduleUpdate
  }
}

/**
 * 使用Web Worker
 */
export function useWebWorker(workerScript: string) {
  const worker = ref<Worker>()
  const isSupported = ref(typeof Worker !== 'undefined')

  const createWorker = () => {
    if (!isSupported.value) return

    worker.value = new Worker(workerScript)
  }

  const postMessage = (data: any) => {
    if (worker.value) {
      worker.value.postMessage(data)
    }
  }

  const onMessage = (callback: (event: MessageEvent) => void) => {
    if (worker.value) {
      worker.value.onmessage = callback
    }
  }

  const terminate = () => {
    if (worker.value) {
      worker.value.terminate()
      worker.value = undefined
    }
  }

  onMounted(() => {
    createWorker()
  })

  onUnmounted(() => {
    terminate()
  })

  return {
    isSupported,
    postMessage,
    onMessage,
    terminate
  }
}
