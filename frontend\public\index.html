<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 量化投资平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden; /* 防止横向滚动 */
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            width: 100%; /* 确保容器不超出屏幕 */
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: clamp(2rem, 5vw, 3rem); /* 响应式字体大小 */
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: clamp(1rem, 3vw, 1.2rem);
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
            text-align: center;
        }

        .feature-description {
            color: #666;
            line-height: 1.6;
            text-align: center;
            margin-bottom: 20px;
        }

        .feature-button {
            display: block;
            width: 100%;
            padding: 12px 20px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .feature-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .stats-title {
            text-align: center;
            font-size: 1.8rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .footer {
            background: rgba(44, 62, 80, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px 20px;
            text-align: center;
            color: #ecf0f1;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .footer-link {
            color: #bdc3c7;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: #3498db;
        }

        /* 响应式设计优化 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header {
                padding: 20px 15px;
                margin-bottom: 20px;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .feature-card {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .footer-links {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }

            .header {
                padding: 15px 10px;
            }

            .feature-card {
                padding: 15px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .stat-item {
                padding: 15px;
            }
        }

        /* 防止内容溢出 */
        .feature-card,
        .stats-section,
        .header,
        .footer {
            max-width: 100%;
            word-wrap: break-word;
        }

        /* 确保图片和媒体元素响应式 */
        img, video, iframe {
            max-width: 100%;
            height: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🚀 量化投资平台</h1>
            <p class="subtitle">
                专业级量化交易系统 • 实时市场数据 • 智能策略回测 • 风险管理<br>
                现代化架构 • 企业级安全 • 高性能计算 • 全面监控
            </p>
        </div>

        <!-- 功能模块 -->
        <div class="features-grid">
            <div class="feature-card" onclick="window.open('trading-terminal.html', '_blank')">
                <div class="feature-icon">💹</div>
                <div class="feature-title">交易终端</div>
                <div class="feature-description">
                    专业级股票交易界面，支持实时行情、智能下单、风险控制，提供完整的交易体验
                </div>
                <a href="trading-terminal.html" class="feature-button">进入交易</a>
            </div>

            <div class="feature-card" onclick="window.open('market-charts.html', '_blank')">
                <div class="feature-icon">📊</div>
                <div class="feature-title">市场数据</div>
                <div class="feature-description">
                    实时K线图、深度图、技术指标分析，基于ECharts的专业级数据可视化
                </div>
                <a href="market-charts.html" class="feature-button">查看图表</a>
            </div>

            <div class="feature-card" onclick="window.open('monitoring-dashboard.html', '_blank')">
                <div class="feature-icon">📡</div>
                <div class="feature-title">系统监控</div>
                <div class="feature-description">
                    实时系统性能监控、API调用分析、智能告警机制，确保系统稳定运行
                </div>
                <a href="monitoring-dashboard.html" class="feature-button">监控面板</a>
            </div>

            <div class="feature-card" onclick="window.open('optimization-test.html', '_blank')">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">系统优化</div>
                <div class="feature-description">
                    数据源集成、压缩算法优化、存储管理，展示最新的系统优化功能
                </div>
                <a href="optimization-test.html" class="feature-button">优化测试</a>
            </div>

            <div class="feature-card" onclick="window.open('strategy-library-demo.html', '_blank')">
                <div class="feature-icon">🧠</div>
                <div class="feature-title">策略开发</div>
                <div class="feature-description">
                    量化策略开发平台，支持策略编写、回测分析、参数优化
                </div>
                <a href="strategy-library-demo.html" class="feature-button">策略中心</a>
            </div>

            <div class="feature-card" onclick="window.open('api-test.html', '_blank')">
                <div class="feature-icon">🔧</div>
                <div class="feature-title">API测试</div>
                <div class="feature-description">
                    完整的API接口测试工具，支持市场数据、交易接口、系统管理等功能测试
                </div>
                <a href="api-test.html" class="feature-button">API测试</a>
            </div>
        </div>

        <!-- 系统统计 -->
        <div class="stats-section">
            <div class="stats-title">📈 系统状态概览</div>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="systemHealth">98.5%</div>
                    <div class="stat-label">系统健康度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="apiCalls">15,247</div>
                    <div class="stat-label">今日API调用</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="activeUsers">1,234</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="dataVolume">2.8TB</div>
                    <div class="stat-label">数据存储</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="responseTime">150ms</div>
                    <div class="stat-label">平均响应时间</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="uptime">99.9%</div>
                    <div class="stat-label">系统可用性</div>
                </div>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <div class="footer-links">
                <a href="api-test.html" class="footer-link">API文档</a>
                <a href="monitoring-dashboard.html" class="footer-link">系统监控</a>
                <a href="optimization-test.html" class="footer-link">性能优化</a>
                <a href="market-charts.html" class="footer-link">数据可视化</a>
                <a href="trading-terminal.html" class="footer-link">交易终端</a>
            </div>
            <p>&copy; 2025 量化投资平台. 基于现代化技术栈构建的专业级量化交易系统.</p>
        </div>
    </div>

    <script>
        // 动态更新统计数据
        function updateStats() {
            const stats = {
                systemHealth: () => (98 + Math.random() * 2).toFixed(1) + '%',
                apiCalls: () => (15000 + Math.floor(Math.random() * 1000)).toLocaleString(),
                activeUsers: () => (1200 + Math.floor(Math.random() * 100)).toLocaleString(),
                dataVolume: () => (2.5 + Math.random() * 0.5).toFixed(1) + 'TB',
                responseTime: () => Math.floor(120 + Math.random() * 60) + 'ms',
                uptime: () => (99.8 + Math.random() * 0.2).toFixed(1) + '%'
            };

            Object.keys(stats).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = stats[key]();
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 量化投资平台主页已加载');
            
            // 定时更新统计数据
            setInterval(updateStats, 5000);
            
            // 添加卡片点击效果
            document.querySelectorAll('.feature-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'A') {
                        const link = this.querySelector('.feature-button');
                        if (link) {
                            window.open(link.href, '_blank');
                        }
                    }
                });
            });
        });

        // 检测移动设备并优化体验
        function isMobile() {
            return window.innerWidth <= 768;
        }

        // 响应式调整
        window.addEventListener('resize', function() {
            if (isMobile()) {
                // 移动端优化
                document.body.style.fontSize = '14px';
            } else {
                // 桌面端
                document.body.style.fontSize = '16px';
            }
        });

        // 防止横向滚动
        window.addEventListener('load', function() {
            document.body.style.overflowX = 'hidden';
        });
    </script>
</body>
</html>
