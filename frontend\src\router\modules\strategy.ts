import type { RouteRecordRaw } from 'vue-router'

const strategyRoutes: RouteRecordRaw[] = [
  {
    path: '/strategy',
    redirect: '/strategy/center'
  },
  {
    path: '/strategy/center',
    name: 'strategy-center',
    component: () => import('@/views/Strategy/StrategyHub.vue'),
    meta: {
      title: '策略中心'
    }
  },
  {
    path: '/strategy/development',
    name: 'strategy-development',
    component: () => import('@/views/Strategy/StrategyDevelop.vue'),
    meta: {
      title: '策略开发'
    }
  },
  {
    path: '/strategy/monitor',
    name: 'strategy-monitor',
    component: () => import('@/views/Strategy/StrategyMonitor.vue'),
    meta: {
      title: '策略监控'
    }
  },
  {
    path: '/strategy/library',
    name: 'strategy-library',
    component: () => import('@/views/Strategy/StrategyLibrary.vue'),
    meta: {
      title: '策略文库'
    }
  },
  {
    path: '/strategy/detail/:id',
    name: 'strategy-detail',
    component: () => import('@/views/Strategy/StrategyDetail.vue'),
    meta: {
      title: '策略详情',
      requiresAuth: false
    }
  }
]

export default strategyRoutes
