"""
增强的生产环境监控和告警服务
"""
import asyncio
import psutil
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import aiohttp
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

from app.core.config import get_settings
from app.core.logger import logger

settings = get_settings()


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class AlertType(Enum):
    """告警类型"""
    SYSTEM = "system"
    APPLICATION = "application"
    SECURITY = "security"
    BUSINESS = "business"
    PERFORMANCE = "performance"


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    network_io: Dict[str, int]
    disk_io: Dict[str, int]
    process_count: int
    load_average: List[float]
    uptime: float


@dataclass
class ApplicationMetrics:
    """应用指标"""
    timestamp: str
    active_users: int
    active_sessions: int
    api_requests_per_minute: int
    response_time_avg: float
    error_rate: float
    database_connections: int
    cache_hit_rate: float
    queue_size: int


@dataclass
class Alert:
    """告警"""
    id: str
    level: AlertLevel
    type: AlertType
    title: str
    message: str
    source: str
    timestamp: str
    resolved: bool = False
    resolved_at: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class EnhancedMonitoringService:
    """增强的监控服务"""
    
    def __init__(self):
        self.alerts = {}  # 存储告警
        self.metrics_history = []  # 指标历史
        self.alert_rules = {}  # 告警规则
        self.notification_channels = {}  # 通知渠道
        self.monitoring_enabled = True
        self.collection_interval = 60  # 采集间隔（秒）
        
        # 健康检查端点
        self.health_checks = {}
        
        # 日志监控
        self.log_patterns = {}
        
        # 性能基准
        self.performance_baselines = {}
        
        # 初始化配置
        self._init_alert_rules()
        self._init_notification_channels()
        self._init_health_checks()
        self._init_log_patterns()
        self._init_performance_baselines()
        
        # 启动监控任务
        self.monitoring_task = None
    
    def _init_alert_rules(self):
        """初始化告警规则"""
        self.alert_rules = {
            "cpu_high": {
                "name": "CPU使用率过高",
                "condition": "cpu_percent > 80",
                "level": AlertLevel.WARNING,
                "type": AlertType.SYSTEM,
                "threshold": 80,
                "duration": 300,  # 持续5分钟
                "enabled": True,
                "description": "CPU使用率持续超过80%"
            },
            "cpu_critical": {
                "name": "CPU使用率严重过高",
                "condition": "cpu_percent > 95",
                "level": AlertLevel.CRITICAL,
                "type": AlertType.SYSTEM,
                "threshold": 95,
                "duration": 60,
                "enabled": True,
                "description": "CPU使用率超过95%，系统可能无响应"
            },
            "memory_high": {
                "name": "内存使用率过高",
                "condition": "memory_percent > 85",
                "level": AlertLevel.WARNING,
                "type": AlertType.SYSTEM,
                "threshold": 85,
                "duration": 300,
                "enabled": True,
                "description": "内存使用率持续超过85%"
            },
            "memory_critical": {
                "name": "内存使用率严重过高",
                "condition": "memory_percent > 95",
                "level": AlertLevel.CRITICAL,
                "type": AlertType.SYSTEM,
                "threshold": 95,
                "duration": 60,
                "enabled": True,
                "description": "内存使用率超过95%，可能导致OOM"
            },
            "disk_high": {
                "name": "磁盘使用率过高",
                "condition": "disk_percent > 90",
                "level": AlertLevel.WARNING,
                "type": AlertType.SYSTEM,
                "threshold": 90,
                "duration": 600,
                "enabled": True,
                "description": "磁盘使用率超过90%"
            },
            "disk_critical": {
                "name": "磁盘空间不足",
                "condition": "disk_percent > 98",
                "level": AlertLevel.CRITICAL,
                "type": AlertType.SYSTEM,
                "threshold": 98,
                "duration": 60,
                "enabled": True,
                "description": "磁盘使用率超过98%，空间严重不足"
            },
            "response_time_high": {
                "name": "API响应时间过长",
                "condition": "response_time_avg > 2000",
                "level": AlertLevel.WARNING,
                "type": AlertType.PERFORMANCE,
                "threshold": 2000,  # 2秒
                "duration": 300,
                "enabled": True,
                "description": "API平均响应时间超过2秒"
            },
            "error_rate_high": {
                "name": "错误率过高",
                "condition": "error_rate > 5",
                "level": AlertLevel.ERROR,
                "type": AlertType.APPLICATION,
                "threshold": 5,  # 5%
                "duration": 180,
                "enabled": True,
                "description": "应用错误率超过5%"
            },
            "database_connections_high": {
                "name": "数据库连接数过高",
                "condition": "database_connections > 80",
                "level": AlertLevel.WARNING,
                "type": AlertType.APPLICATION,
                "threshold": 80,
                "duration": 300,
                "enabled": True,
                "description": "数据库连接数超过80"
            },
            "queue_size_high": {
                "name": "队列积压严重",
                "condition": "queue_size > 1000",
                "level": AlertLevel.WARNING,
                "type": AlertType.APPLICATION,
                "threshold": 1000,
                "duration": 300,
                "enabled": True,
                "description": "消息队列积压超过1000条"
            },
            "cache_hit_rate_low": {
                "name": "缓存命中率过低",
                "condition": "cache_hit_rate < 70",
                "level": AlertLevel.WARNING,
                "type": AlertType.PERFORMANCE,
                "threshold": 70,
                "duration": 600,
                "enabled": True,
                "description": "缓存命中率低于70%"
            }
        }
    
    def _init_notification_channels(self):
        """初始化通知渠道"""
        self.notification_channels = {
            "email": {
                "enabled": True,
                "smtp_server": getattr(settings, 'SMTP_SERVER', 'smtp.gmail.com'),
                "smtp_port": getattr(settings, 'SMTP_PORT', 587),
                "username": getattr(settings, 'SMTP_USERNAME', ''),
                "password": getattr(settings, 'SMTP_PASSWORD', ''),
                "recipients": ["<EMAIL>", "<EMAIL>"]
            },
            "webhook": {
                "enabled": True,
                "url": getattr(settings, 'ALERT_WEBHOOK_URL', 'http://localhost:8080/alerts'),
                "headers": {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {getattr(settings, 'ALERT_WEBHOOK_TOKEN', 'demo-token')}"
                }
            },
            "slack": {
                "enabled": True,
                "webhook_url": getattr(settings, 'SLACK_WEBHOOK_URL', ''),
                "channel": "#alerts"
            },
            "dingtalk": {
                "enabled": True,
                "webhook_url": getattr(settings, 'DINGTALK_WEBHOOK_URL', ''),
                "secret": getattr(settings, 'DINGTALK_SECRET', '')
            },
            "sms": {
                "enabled": False,
                "provider": "aliyun",
                "access_key": getattr(settings, 'SMS_ACCESS_KEY', ''),
                "secret_key": getattr(settings, 'SMS_SECRET_KEY', ''),
                "template_id": "SMS_123456789",
                "phone_numbers": ["+86138****8888"]
            }
        }
    
    def _init_health_checks(self):
        """初始化健康检查"""
        self.health_checks = {
            "database": {
                "name": "数据库连接",
                "url": "postgresql://localhost:5432/quantdb",
                "timeout": 5,
                "interval": 60,
                "enabled": True,
                "last_check": None,
                "status": "unknown"
            },
            "redis": {
                "name": "Redis缓存",
                "url": "redis://localhost:6379",
                "timeout": 3,
                "interval": 60,
                "enabled": True,
                "last_check": None,
                "status": "unknown"
            },
            "external_api": {
                "name": "外部API",
                "url": "https://api.example.com/health",
                "timeout": 10,
                "interval": 300,
                "enabled": True,
                "last_check": None,
                "status": "unknown"
            },
            "message_queue": {
                "name": "消息队列",
                "url": "amqp://localhost:5672",
                "timeout": 5,
                "interval": 120,
                "enabled": True,
                "last_check": None,
                "status": "unknown"
            }
        }
    
    def _init_log_patterns(self):
        """初始化日志监控模式"""
        self.log_patterns = {
            "error_pattern": {
                "name": "错误日志监控",
                "pattern": r"ERROR|CRITICAL|FATAL",
                "level": AlertLevel.ERROR,
                "threshold": 10,  # 10分钟内超过阈值
                "window": 600,
                "enabled": True
            },
            "security_pattern": {
                "name": "安全事件监控",
                "pattern": r"SECURITY|BREACH|UNAUTHORIZED|FAILED_LOGIN",
                "level": AlertLevel.WARNING,
                "threshold": 5,
                "window": 300,
                "enabled": True
            },
            "performance_pattern": {
                "name": "性能问题监控",
                "pattern": r"SLOW_QUERY|TIMEOUT|PERFORMANCE",
                "level": AlertLevel.WARNING,
                "threshold": 20,
                "window": 600,
                "enabled": True
            }
        }
    
    def _init_performance_baselines(self):
        """初始化性能基准"""
        self.performance_baselines = {
            "api_response_time": {
                "baseline": 500,  # 毫秒
                "tolerance": 0.5,  # 50%偏差
                "measurement_window": 3600  # 1小时
            },
            "throughput": {
                "baseline": 1000,  # 每分钟请求数
                "tolerance": 0.3,
                "measurement_window": 1800
            },
            "error_rate": {
                "baseline": 1.0,  # 1%
                "tolerance": 2.0,  # 允许2倍偏差
                "measurement_window": 1800
            },
            "cpu_usage": {
                "baseline": 50.0,  # 50%
                "tolerance": 0.6,
                "measurement_window": 3600
            },
            "memory_usage": {
                "baseline": 60.0,  # 60%
                "tolerance": 0.4,
                "measurement_window": 3600
            }
        }
    
    async def start_monitoring(self):
        """启动监控"""
        if self.monitoring_task is None or self.monitoring_task.done():
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            logger.info("🚀 增强监控服务已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        if self.monitoring_task and not self.monitoring_task.done():
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
            logger.info("⏹️ 增强监控服务已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_enabled:
            try:
                # 收集系统指标
                system_metrics = await self._collect_system_metrics()
                
                # 收集应用指标
                app_metrics = await self._collect_application_metrics()
                
                # 存储指标
                self._store_metrics(system_metrics, app_metrics)
                
                # 检查告警规则
                await self._check_alert_rules(system_metrics, app_metrics)
                
                # 执行健康检查
                await self._perform_health_checks()
                
                # 检查性能基准
                await self._check_performance_baselines(system_metrics, app_metrics)
                
                # 清理过期数据
                self._cleanup_old_data()
                
                await asyncio.sleep(self.collection_interval)
                
            except Exception as e:
                logger.error(f"❌ 监控循环错误: {e}")
                await asyncio.sleep(30)  # 错误时短暂等待
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """收集系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 网络IO
            network_io = psutil.net_io_counters()._asdict()
            
            # 磁盘IO
            disk_io = psutil.disk_io_counters()._asdict()
            
            # 进程数
            process_count = len(psutil.pids())
            
            # 负载平均值
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows系统没有getloadavg
                load_average = [0, 0, 0]
            
            # 系统运行时间
            uptime = time.time() - psutil.boot_time()
            
            return SystemMetrics(
                timestamp=datetime.utcnow().isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                disk_percent=disk_percent,
                network_io=network_io,
                disk_io=disk_io,
                process_count=process_count,
                load_average=load_average,
                uptime=uptime
            )
            
        except Exception as e:
            logger.error(f"❌ 收集系统指标失败: {e}")
            return SystemMetrics(
                timestamp=datetime.utcnow().isoformat(),
                cpu_percent=0,
                memory_percent=0,
                disk_percent=0,
                network_io={},
                disk_io={},
                process_count=0,
                load_average=[0, 0, 0],
                uptime=0
            )
    
    async def _collect_application_metrics(self) -> ApplicationMetrics:
        """收集应用指标"""
        try:
            # 在实际环境中，这些指标应该从应用程序中收集
            # 这里使用模拟数据进行演示
            import random
            
            return ApplicationMetrics(
                timestamp=datetime.utcnow().isoformat(),
                active_users=random.randint(50, 200),
                active_sessions=random.randint(30, 150),
                api_requests_per_minute=random.randint(100, 1000),
                response_time_avg=random.uniform(100, 800),
                error_rate=random.uniform(0, 3),
                database_connections=random.randint(10, 50),
                cache_hit_rate=random.uniform(85, 99),
                queue_size=random.randint(0, 500)
            )
            
        except Exception as e:
            logger.error(f"❌ 收集应用指标失败: {e}")
            return ApplicationMetrics(
                timestamp=datetime.utcnow().isoformat(),
                active_users=0,
                active_sessions=0,
                api_requests_per_minute=0,
                response_time_avg=0,
                error_rate=0,
                database_connections=0,
                cache_hit_rate=0,
                queue_size=0
            )
    
    def _store_metrics(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """存储指标"""
        metrics_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "system": asdict(system_metrics),
            "application": asdict(app_metrics)
        }
        
        self.metrics_history.append(metrics_data)
        
        # 只保留最近24小时的数据
        cutoff_time = datetime.utcnow() - timedelta(hours=24)
        self.metrics_history = [
            m for m in self.metrics_history
            if datetime.fromisoformat(m["timestamp"]) > cutoff_time
        ]
    
    async def _check_alert_rules(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """检查告警规则"""
        current_time = datetime.utcnow()
        
        # 合并指标数据
        all_metrics = {
            **asdict(system_metrics),
            **asdict(app_metrics)
        }
        
        for rule_id, rule in self.alert_rules.items():
            if not rule["enabled"]:
                continue
            
            try:
                # 检查条件
                condition_met = self._evaluate_condition(rule["condition"], all_metrics)
                
                if condition_met:
                    # 检查是否已经有活跃的告警
                    existing_alert = self._get_active_alert(rule_id)
                    
                    if existing_alert:
                        # 更新现有告警的持续时间
                        duration = (current_time - datetime.fromisoformat(existing_alert.timestamp)).total_seconds()
                        if duration >= rule["duration"]:
                            # 达到持续时间阈值，发送告警
                            await self._send_alert(existing_alert)
                    else:
                        # 创建新告警
                        alert = Alert(
                            id=f"{rule_id}_{int(time.time())}",
                            level=rule["level"],
                            type=rule["type"],
                            title=rule["name"],
                            message=f"{rule['name']}: {rule['description']}",
                            source=rule_id,
                            timestamp=current_time.isoformat(),
                            metadata={
                                "rule_id": rule_id,
                                "threshold": rule["threshold"],
                                "current_value": self._get_metric_value(rule["condition"], all_metrics),
                                "condition": rule["condition"],
                                "description": rule["description"],
                                "metrics_snapshot": all_metrics
                            }
                        )
                        
                        self.alerts[alert.id] = alert
                        logger.warning(f"⚠️ 新告警: {alert.title}")
                else:
                    # 条件不满足，解决相关告警
                    resolved_count = self._resolve_alerts_by_source(rule_id)
                    if resolved_count > 0:
                        logger.info(f"✅ 已解决 {resolved_count} 个告警: {rule['name']}")
                    
            except Exception as e:
                logger.error(f"❌ 检查告警规则 {rule_id} 失败: {e}")
    
    async def _perform_health_checks(self):
        """执行健康检查"""
        for check_id, check_config in self.health_checks.items():
            if not check_config["enabled"]:
                continue
            
            # 检查是否到了检查时间
            last_check = check_config.get("last_check")
            if last_check:
                time_since_last = (datetime.utcnow() - datetime.fromisoformat(last_check)).total_seconds()
                if time_since_last < check_config["interval"]:
                    continue
            
            try:
                # 执行健康检查
                status = await self._execute_health_check(check_config)
                check_config["status"] = status
                check_config["last_check"] = datetime.utcnow().isoformat()
                
                # 如果健康检查失败，创建告警
                if status != "healthy":
                    await self._create_health_check_alert(check_id, check_config, status)
                
            except Exception as e:
                logger.error(f"❌ 健康检查 {check_id} 失败: {e}")
                check_config["status"] = "error"
                check_config["last_check"] = datetime.utcnow().isoformat()
    
    async def _execute_health_check(self, check_config: Dict[str, Any]) -> str:
        """执行单个健康检查"""
        url = check_config["url"]
        timeout = check_config["timeout"]
        
        try:
            if url.startswith("http"):
                # HTTP健康检查
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, timeout=timeout) as response:
                        if response.status == 200:
                            return "healthy"
                        else:
                            return "unhealthy"
            elif url.startswith("postgresql://"):
                # 数据库健康检查（模拟）
                await asyncio.sleep(0.1)  # 模拟检查延迟
                return "healthy"
            elif url.startswith("redis://"):
                # Redis健康检查（模拟）
                await asyncio.sleep(0.05)
                return "healthy"
            elif url.startswith("amqp://"):
                # 消息队列健康检查（模拟）
                await asyncio.sleep(0.1)
                return "healthy"
            else:
                return "unknown"
                
        except asyncio.TimeoutError:
            return "timeout"
        except Exception:
            return "error"
    
    async def _create_health_check_alert(self, check_id: str, check_config: Dict[str, Any], status: str):
        """创建健康检查告警"""
        alert_id = f"health_check_{check_id}_{int(time.time())}"
        
        # 检查是否已有相同的活跃告警
        existing_alert = self._get_active_alert(f"health_check_{check_id}")
        if existing_alert:
            return
        
        level = AlertLevel.CRITICAL if status == "error" else AlertLevel.WARNING
        
        alert = Alert(
            id=alert_id,
            level=level,
            type=AlertType.SYSTEM,
            title=f"健康检查失败: {check_config['name']}",
            message=f"{check_config['name']} 健康检查失败，状态: {status}",
            source=f"health_check_{check_id}",
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "check_id": check_id,
                "check_name": check_config["name"],
                "check_url": check_config["url"],
                "status": status,
                "timeout": check_config["timeout"]
            }
        )
        
        self.alerts[alert.id] = alert
        await self._send_alert(alert)
    
    async def _check_performance_baselines(self, system_metrics: SystemMetrics, app_metrics: ApplicationMetrics):
        """检查性能基准"""
        current_metrics = {
            "api_response_time": app_metrics.response_time_avg,
            "throughput": app_metrics.api_requests_per_minute,
            "error_rate": app_metrics.error_rate,
            "cpu_usage": system_metrics.cpu_percent,
            "memory_usage": system_metrics.memory_percent
        }
        
        for metric_name, current_value in current_metrics.items():
            baseline_config = self.performance_baselines.get(metric_name)
            if not baseline_config:
                continue
            
            baseline = baseline_config["baseline"]
            tolerance = baseline_config["tolerance"]
            
            # 计算偏差
            if baseline > 0:
                deviation = abs(current_value - baseline) / baseline
                
                if deviation > tolerance:
                    # 性能偏离基准，创建告警
                    await self._create_performance_baseline_alert(metric_name, current_value, baseline, deviation)
    
    async def _create_performance_baseline_alert(self, metric_name: str, current_value: float, baseline: float, deviation: float):
        """创建性能基准告警"""
        alert_id = f"baseline_{metric_name}_{int(time.time())}"
        
        # 检查是否已有相同的活跃告警
        existing_alert = self._get_active_alert(f"baseline_{metric_name}")
        if existing_alert:
            return
        
        level = AlertLevel.WARNING if deviation < 1.0 else AlertLevel.ERROR
        
        alert = Alert(
            id=alert_id,
            level=level,
            type=AlertType.PERFORMANCE,
            title=f"性能基准偏离: {metric_name}",
            message=f"{metric_name} 当前值 {current_value:.2f} 偏离基准 {baseline:.2f}，偏差 {deviation:.1%}",
            source=f"baseline_{metric_name}",
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "metric_name": metric_name,
                "current_value": current_value,
                "baseline": baseline,
                "deviation": deviation,
                "tolerance": self.performance_baselines[metric_name]["tolerance"]
            }
        )
        
        self.alerts[alert.id] = alert
        await self._send_alert(alert)
    
    def _evaluate_condition(self, condition: str, metrics: Dict[str, Any]) -> bool:
        """评估告警条件"""
        try:
            # 简单的条件评估，生产环境中应该使用更安全的方法
            for key, value in metrics.items():
                if isinstance(value, (int, float)):
                    locals()[key] = value
            
            return eval(condition)
        except Exception as e:
            logger.error(f"❌ 评估条件失败: {condition}, {e}")
            return False
    
    def _get_metric_value(self, condition: str, metrics: Dict[str, Any]) -> Any:
        """获取指标值"""
        try:
            # 从条件中提取指标名称
            metric_name = condition.split()[0]
            return metrics.get(metric_name, 0)
        except:
            return 0
    
    def _get_active_alert(self, source: str) -> Optional[Alert]:
        """获取活跃的告警"""
        for alert in self.alerts.values():
            if alert.source == source and not alert.resolved:
                return alert
        return None
    
    def _resolve_alerts_by_source(self, source: str) -> int:
        """根据来源解决告警"""
        current_time = datetime.utcnow().isoformat()
        resolved_count = 0
        
        for alert in self.alerts.values():
            if alert.source == source and not alert.resolved:
                alert.resolved = True
                alert.resolved_at = current_time
                resolved_count += 1
        
        return resolved_count
    
    async def _send_alert(self, alert: Alert):
        """发送告警"""
        try:
            # 发送到各个通知渠道
            tasks = []
            
            if self.notification_channels["email"]["enabled"]:
                tasks.append(self._send_email_alert(alert))
            
            if self.notification_channels["webhook"]["enabled"]:
                tasks.append(self._send_webhook_alert(alert))
            
            if self.notification_channels["slack"]["enabled"]:
                tasks.append(self._send_slack_alert(alert))
            
            if self.notification_channels["dingtalk"]["enabled"]:
                tasks.append(self._send_dingtalk_alert(alert))
            
            if self.notification_channels["sms"]["enabled"] and alert.level in [AlertLevel.CRITICAL, AlertLevel.ERROR]:
                tasks.append(self._send_sms_alert(alert))
            
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)
                success_count = sum(1 for r in results if not isinstance(r, Exception))
                logger.info(f"📢 告警已发送到 {success_count}/{len(tasks)} 个渠道: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ 发送告警失败: {e}")
    
    async def _send_webhook_alert(self, alert: Alert):
        """发送Webhook告警"""
        try:
            webhook_config = self.notification_channels["webhook"]
            
            if not webhook_config["url"]:
                return
            
            payload = {
                "alert": asdict(alert),
                "timestamp": datetime.utcnow().isoformat(),
                "platform": "QuantTradingPlatform"
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_config["url"],
                    json=payload,
                    headers=webhook_config["headers"],
                    timeout=10
                ) as response:
                    if response.status == 200:
                        logger.debug(f"✅ Webhook告警发送成功: {alert.title}")
                    else:
                        logger.warning(f"⚠️ Webhook告警发送失败: {response.status}")
                        
        except Exception as e:
            logger.error(f"❌ 发送Webhook告警失败: {e}")
    
    async def _send_email_alert(self, alert: Alert):
        """发送邮件告警"""
        try:
            email_config = self.notification_channels["email"]
            
            if not email_config["recipients"] or not email_config["username"]:
                return
            
            # 创建邮件内容
            msg = MimeMultipart()
            msg['From'] = email_config["username"]
            msg['To'] = ", ".join(email_config["recipients"])
            msg['Subject'] = f"[{alert.level.value.upper()}] {alert.title}"
            
            # 邮件正文
            body = f"""
<html>
<body>
<h2>🚨 系统告警通知</h2>

<table border="1" cellpadding="5" cellspacing="0">
<tr><td><strong>告警级别</strong></td><td style="color: {'red' if alert.level in [AlertLevel.CRITICAL, AlertLevel.ERROR] else 'orange'}">{alert.level.value.upper()}</td></tr>
<tr><td><strong>告警类型</strong></td><td>{alert.type.value}</td></tr>
<tr><td><strong>告警时间</strong></td><td>{alert.timestamp}</td></tr>
<tr><td><strong>告警标题</strong></td><td>{alert.title}</td></tr>
<tr><td><strong>告警消息</strong></td><td>{alert.message}</td></tr>
<tr><td><strong>告警来源</strong></td><td>{alert.source}</td></tr>
</table>

<h3>📊 详细信息</h3>
<pre>{json.dumps(alert.metadata, indent=2, ensure_ascii=False) if alert.metadata else '无'}</pre>

<hr>
<p><small>此邮件由量化交易平台监控系统自动发送</small></p>
</body>
</html>
            """
            
            msg.attach(MimeText(body, 'html', 'utf-8'))
            
            # 发送邮件（模拟）
            logger.debug(f"📧 邮件告警已准备发送: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ 发送邮件告警失败: {e}")
    
    async def _send_slack_alert(self, alert: Alert):
        """发送Slack告警"""
        try:
            slack_config = self.notification_channels["slack"]
            
            if not slack_config["webhook_url"]:
                return
            
            color_map = {
                AlertLevel.INFO: "good",
                AlertLevel.WARNING: "warning",
                AlertLevel.ERROR: "danger",
                AlertLevel.CRITICAL: "danger"
            }
            
            emoji_map = {
                AlertLevel.INFO: ":information_source:",
                AlertLevel.WARNING: ":warning:",
                AlertLevel.ERROR: ":exclamation:",
                AlertLevel.CRITICAL: ":rotating_light:"
            }
            
            payload = {
                "channel": slack_config["channel"],
                "username": "监控系统",
                "icon_emoji": emoji_map.get(alert.level, ":warning:"),
                "attachments": [{
                    "color": color_map.get(alert.level, "warning"),
                    "title": alert.title,
                    "text": alert.message,
                    "fields": [
                        {"title": "级别", "value": alert.level.value.upper(), "short": True},
                        {"title": "类型", "value": alert.type.value, "short": True},
                        {"title": "来源", "value": alert.source, "short": True},
                        {"title": "时间", "value": alert.timestamp, "short": True}
                    ],
                    "timestamp": int(datetime.fromisoformat(alert.timestamp).timestamp())
                }]
            }
            
            # 模拟发送
            logger.debug(f"💬 Slack告警已准备发送: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ 发送Slack告警失败: {e}")
    
    async def _send_dingtalk_alert(self, alert: Alert):
        """发送钉钉告警"""
        try:
            dingtalk_config = self.notification_channels["dingtalk"]
            
            if not dingtalk_config["webhook_url"]:
                return
            
            emoji_map = {
                AlertLevel.INFO: "ℹ️",
                AlertLevel.WARNING: "⚠️",
                AlertLevel.ERROR: "❌",
                AlertLevel.CRITICAL: "🚨"
            }
            
            text = f"""
{emoji_map.get(alert.level, "⚠️")} **{alert.level.value.upper()}告警**

**标题**: {alert.title}
**消息**: {alert.message}
**时间**: {alert.timestamp}
**来源**: {alert.source}
**类型**: {alert.type.value}

---
量化交易平台监控系统
            """
            
            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "title": f"{alert.level.value.upper()}告警",
                    "text": text
                }
            }
            
            # 模拟发送
            logger.debug(f"📱 钉钉告警已准备发送: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ 发送钉钉告警失败: {e}")
    
    async def _send_sms_alert(self, alert: Alert):
        """发送短信告警"""
        try:
            sms_config = self.notification_channels["sms"]
            
            if not sms_config["phone_numbers"]:
                return
            
            message = f"【告警】{alert.title}: {alert.message[:50]}... 时间:{alert.timestamp[:16]}"
            
            # 模拟发送短信
            logger.debug(f"📱 短信告警已准备发送: {alert.title}")
            
        except Exception as e:
            logger.error(f"❌ 发送短信告警失败: {e}")
    
    def _cleanup_old_data(self):
        """清理过期数据"""
        # 清理过期告警（保留7天）
        cutoff_time = datetime.utcnow() - timedelta(days=7)
        expired_alerts = [
            alert_id for alert_id, alert in self.alerts.items()
            if datetime.fromisoformat(alert.timestamp) < cutoff_time
        ]
        
        for alert_id in expired_alerts:
            del self.alerts[alert_id]
        
        if expired_alerts:
            logger.info(f"🧹 已清理 {len(expired_alerts)} 个过期告警")
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        if not self.metrics_history:
            return {
                "status": "unknown",
                "health_score": 0,
                "message": "暂无监控数据",
                "components": {}
            }
        
        latest_metrics = self.metrics_history[-1]
        system_metrics = latest_metrics["system"]
        app_metrics = latest_metrics["application"]
        
        # 计算健康分数
        health_score = 100
        issues = []
        component_status = {}
        
        # 系统组件检查
        if system_metrics["cpu_percent"] > 80:
            health_score -= 20
            issues.append("CPU使用率过高")
            component_status["cpu"] = "warning"
        else:
            component_status["cpu"] = "healthy"
        
        if system_metrics["memory_percent"] > 85:
            health_score -= 20
            issues.append("内存使用率过高")
            component_status["memory"] = "warning"
        else:
            component_status["memory"] = "healthy"
        
        if system_metrics["disk_percent"] > 90:
            health_score -= 15
            issues.append("磁盘使用率过高")
            component_status["disk"] = "warning"
        else:
            component_status["disk"] = "healthy"
        
        # 应用组件检查
        if app_metrics["error_rate"] > 5:
            health_score -= 25
            issues.append("错误率过高")
            component_status["application"] = "error"
        elif app_metrics["error_rate"] > 2:
            component_status["application"] = "warning"
        else:
            component_status["application"] = "healthy"
        
        if app_metrics["response_time_avg"] > 2000:
            health_score -= 10
            issues.append("响应时间过长")
            component_status["performance"] = "warning"
        else:
            component_status["performance"] = "healthy"
        
        # 健康检查状态
        unhealthy_checks = [
            check_id for check_id, check in self.health_checks.items()
            if check["enabled"] and check["status"] not in ["healthy", "unknown"]
        ]
        
        if unhealthy_checks:
            health_score -= len(unhealthy_checks) * 10
            issues.extend([f"{check_id}健康检查失败" for check_id in unhealthy_checks])
        
        # 活跃告警影响
        active_alerts = [a for a in self.alerts.values() if not a.resolved]
        critical_alerts = [a for a in active_alerts if a.level == AlertLevel.CRITICAL]
        error_alerts = [a for a in active_alerts if a.level == AlertLevel.ERROR]
        
        health_score -= len(critical_alerts) * 15
        health_score -= len(error_alerts) * 10
        
        health_score = max(0, health_score)
        
        # 确定状态
        if health_score >= 90:
            status = "healthy"
            status_text = "系统运行正常"
        elif health_score >= 70:
            status = "warning"
            status_text = "系统存在一些问题"
        elif health_score >= 50:
            status = "degraded"
            status_text = "系统性能下降"
        else:
            status = "critical"
            status_text = "系统存在严重问题"
        
        return {
            "status": status,
            "health_score": health_score,
            "message": status_text,
            "issues": issues,
            "components": component_status,
            "active_alerts": len(active_alerts),
            "critical_alerts": len(critical_alerts),
            "last_check": latest_metrics["timestamp"],
            "uptime": system_metrics["uptime"],
            "health_checks": {
                check_id: check["status"]
                for check_id, check in self.health_checks.items()
                if check["enabled"]
            }
        }
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """获取监控仪表板数据"""
        health = self.get_system_health()
        active_alerts = self.get_active_alerts()
        metrics_summary = self.get_metrics_summary(1)
        
        return {
            "overview": {
                "health_status": health["status"],
                "health_score": health["health_score"],
                "active_alerts": len(active_alerts),
                "critical_alerts": len([a for a in active_alerts if a["level"] == "critical"]),
                "monitoring_enabled": self.monitoring_enabled,
                "last_update": datetime.utcnow().isoformat()
            },
            "system_health": health,
            "active_alerts": active_alerts[:10],  # 最近10个告警
            "metrics_summary": metrics_summary,
            "alert_rules": {
                "total": len(self.alert_rules),
                "enabled": len([r for r in self.alert_rules.values() if r["enabled"]]),
                "by_type": {
                    alert_type.value: len([
                        r for r in self.alert_rules.values()
                        if r["type"] == alert_type and r["enabled"]
                    ])
                    for alert_type in AlertType
                }
            },
            "notification_channels": {
                name: config["enabled"]
                for name, config in self.notification_channels.items()
            },
            "health_checks": {
                check_id: {
                    "name": check["name"],
                    "status": check["status"],
                    "last_check": check["last_check"]
                }
                for check_id, check in self.health_checks.items()
                if check["enabled"]
            }
        }
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        recent_metrics = [
            m for m in self.metrics_history
            if datetime.fromisoformat(m["timestamp"]) > cutoff_time
        ]
        
        if not recent_metrics:
            return {"message": "暂无数据"}
        
        # 计算平均值、最大值、最小值
        cpu_values = [m["system"]["cpu_percent"] for m in recent_metrics]
        memory_values = [m["system"]["memory_percent"] for m in recent_metrics]
        response_times = [m["application"]["response_time_avg"] for m in recent_metrics]
        error_rates = [m["application"]["error_rate"] for m in recent_metrics]
        
        return {
            "period_hours": hours,
            "data_points": len(recent_metrics),
            "cpu": {
                "avg": round(sum(cpu_values) / len(cpu_values), 2),
                "max": round(max(cpu_values), 2),
                "min": round(min(cpu_values), 2),
                "current": round(cpu_values[-1], 2)
            },
            "memory": {
                "avg": round(sum(memory_values) / len(memory_values), 2),
                "max": round(max(memory_values), 2),
                "min": round(min(memory_values), 2),
                "current": round(memory_values[-1], 2)
            },
            "response_time": {
                "avg": round(sum(response_times) / len(response_times), 2),
                "max": round(max(response_times), 2),
                "min": round(min(response_times), 2),
                "current": round(response_times[-1], 2)
            },
            "error_rate": {
                "avg": round(sum(error_rates) / len(error_rates), 2),
                "max": round(max(error_rates), 2),
                "min": round(min(error_rates), 2),
                "current": round(error_rates[-1], 2)
            }
        }
    
    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """获取活跃告警"""
        active_alerts = [
            asdict(alert) for alert in self.alerts.values()
            if not alert.resolved
        ]
        
        # 按级别和时间排序
        level_priority = {
            AlertLevel.CRITICAL: 0,
            AlertLevel.ERROR: 1,
            AlertLevel.WARNING: 2,
            AlertLevel.INFO: 3
        }
        
        active_alerts.sort(key=lambda x: (
            level_priority.get(AlertLevel(x["level"]), 4),
            x["timestamp"]
        ))
        
        return active_alerts
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        return {
            "monitoring_enabled": self.monitoring_enabled,
            "collection_interval": self.collection_interval,
            "total_alerts": len(self.alerts),
            "active_alerts": len([a for a in self.alerts.values() if not a.resolved]),
            "resolved_alerts": len([a for a in self.alerts.values() if a.resolved]),
            "metrics_data_points": len(self.metrics_history),
            "alert_rules": {
                "total": len(self.alert_rules),
                "enabled": len([r for r in self.alert_rules.values() if r["enabled"]]),
                "disabled": len([r for r in self.alert_rules.values() if not r["enabled"]])
            },
            "health_checks": {
                "total": len(self.health_checks),
                "enabled": len([c for c in self.health_checks.values() if c["enabled"]]),
                "healthy": len([c for c in self.health_checks.values() if c["status"] == "healthy"]),
                "unhealthy": len([c for c in self.health_checks.values() if c["status"] not in ["healthy", "unknown"]])
            },
            "notification_channels": {
                name: config["enabled"]
                for name, config in self.notification_channels.items()
            },
            "performance_baselines": len(self.performance_baselines),
            "log_patterns": len(self.log_patterns),
            "last_collection": self.metrics_history[-1]["timestamp"] if self.metrics_history else None,
            "uptime": time.time() - (time.time() - 3600)  # 模拟1小时运行时间
        }


# 全局实例
enhanced_monitoring_service = EnhancedMonitoringService()


async def get_enhanced_monitoring_service() -> EnhancedMonitoringService:
    """获取增强监控服务实例"""
    return enhanced_monitoring_service
