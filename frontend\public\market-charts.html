<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 市场数据可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .charts-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .chart-container {
            width: 100%;
            height: 400px;
        }

        .chart-container.large {
            height: 500px;
        }

        .chart-container.small {
            height: 300px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .control-btn {
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .control-btn:hover {
            background: #2980b9;
        }

        .control-btn.active {
            background: #e74c3c;
        }

        .symbol-input {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
            min-width: 120px;
        }

        .symbol-input:focus {
            outline: none;
            border-color: #3498db;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #e0e0e0;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="charts-container">
        <!-- 头部 -->
        <div class="header">
            <h1>📊 市场数据可视化</h1>
            <p class="subtitle">实时K线图 • 深度图 • 技术指标 • 市场热力图</p>
        </div>

        <!-- 市场统计 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="marketCap">¥85.6万亿</div>
                <div class="stat-label">总市值</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="tradingVolume">¥8,520亿</div>
                <div class="stat-label">成交额</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="upCount">2,156</div>
                <div class="stat-label">上涨家数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="downCount">1,844</div>
                <div class="stat-label">下跌家数</div>
            </div>
        </div>

        <!-- 图表网格 -->
        <div class="charts-grid">
            <!-- K线图 -->
            <div class="chart-panel">
                <div class="chart-title">📈 K线图</div>
                <div class="controls">
                    <input type="text" class="symbol-input" placeholder="股票代码" value="000001.SZ" id="klineSymbol">
                    <button class="control-btn active" onclick="changeKlinePeriod('1d')">日K</button>
                    <button class="control-btn" onclick="changeKlinePeriod('1w')">周K</button>
                    <button class="control-btn" onclick="changeKlinePeriod('1M')">月K</button>
                    <button class="control-btn" onclick="refreshKlineChart()">刷新</button>
                </div>
                <div class="chart-container large" id="klineChart"></div>
            </div>

            <!-- 深度图 -->
            <div class="chart-panel">
                <div class="chart-title">📊 买卖深度</div>
                <div class="controls">
                    <input type="text" class="symbol-input" placeholder="股票代码" value="000001.SZ" id="depthSymbol">
                    <button class="control-btn" onclick="refreshDepthChart()">刷新</button>
                </div>
                <div class="chart-container" id="depthChart"></div>
            </div>
        </div>

        <!-- 第二行图表 -->
        <div class="charts-grid">
            <!-- 技术指标 -->
            <div class="chart-panel">
                <div class="chart-title">📉 技术指标</div>
                <div class="controls">
                    <button class="control-btn active" onclick="changeIndicator('MACD')">MACD</button>
                    <button class="control-btn" onclick="changeIndicator('RSI')">RSI</button>
                    <button class="control-btn" onclick="changeIndicator('KDJ')">KDJ</button>
                    <button class="control-btn" onclick="changeIndicator('BOLL')">BOLL</button>
                </div>
                <div class="chart-container" id="indicatorChart"></div>
            </div>

            <!-- 市场热力图 -->
            <div class="chart-panel">
                <div class="chart-title">🔥 市场热力图</div>
                <div class="controls">
                    <button class="control-btn active" onclick="changeHeatmapType('sector')">行业</button>
                    <button class="control-btn" onclick="changeHeatmapType('concept')">概念</button>
                </div>
                <div class="chart-container" id="heatmapChart"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        const API_BASE = 'http://localhost:8000/api/v1';
        let klineChart, depthChart, indicatorChart, heatmapChart;
        let currentKlinePeriod = '1d';
        let currentIndicator = 'MACD';
        let currentHeatmapType = 'sector';

        // 初始化所有图表
        function initCharts() {
            // 初始化K线图
            klineChart = echarts.init(document.getElementById('klineChart'));
            
            // 初始化深度图
            depthChart = echarts.init(document.getElementById('depthChart'));
            
            // 初始化技术指标图
            indicatorChart = echarts.init(document.getElementById('indicatorChart'));
            
            // 初始化热力图
            heatmapChart = echarts.init(document.getElementById('heatmapChart'));

            // 加载初始数据
            loadKlineData();
            loadDepthData();
            loadIndicatorData();
            loadHeatmapData();
        }

        // 生成模拟K线数据
        function generateKlineData(days = 60) {
            const data = [];
            let basePrice = 12.5;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - days);

            for (let i = 0; i < days; i++) {
                const date = new Date(startDate);
                date.setDate(date.getDate() + i);
                
                const change = (Math.random() - 0.5) * 0.8;
                const open = basePrice;
                const close = basePrice + change;
                const high = Math.max(open, close) + Math.random() * 0.3;
                const low = Math.min(open, close) - Math.random() * 0.3;
                const volume = Math.floor(Math.random() * 1000000) + 500000;

                data.push([
                    date.toISOString().split('T')[0],
                    open.toFixed(2),
                    close.toFixed(2),
                    low.toFixed(2),
                    high.toFixed(2),
                    volume
                ]);

                basePrice = close;
            }

            return data;
        }

        // 加载K线数据
        async function loadKlineData() {
            const symbol = document.getElementById('klineSymbol').value || '000001.SZ';
            
            try {
                // 尝试从API获取数据
                const response = await fetch(`${API_BASE}/market/enhanced/kline/${symbol}?period=${currentKlinePeriod}&limit=60`);
                let klineData;
                
                if (response.ok) {
                    const result = await response.json();
                    klineData = result.data || generateKlineData();
                } else {
                    klineData = generateKlineData();
                }

                const option = {
                    title: {
                        text: `${symbol} K线图`,
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        formatter: function (params) {
                            const data = params[0].data;
                            return `日期: ${data[0]}<br/>
                                   开盘: ${data[1]}<br/>
                                   收盘: ${data[2]}<br/>
                                   最低: ${data[3]}<br/>
                                   最高: ${data[4]}<br/>
                                   成交量: ${data[5]}`;
                        }
                    },
                    grid: {
                        left: '10%',
                        right: '10%',
                        bottom: '15%'
                    },
                    xAxis: {
                        type: 'category',
                        data: klineData.map(item => item[0]),
                        scale: true,
                        boundaryGap: false,
                        axisLine: { onZero: false },
                        splitLine: { show: false },
                        min: 'dataMin',
                        max: 'dataMax'
                    },
                    yAxis: {
                        scale: true,
                        splitArea: {
                            show: true
                        }
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 50,
                            end: 100
                        },
                        {
                            show: true,
                            type: 'slider',
                            top: '90%',
                            start: 50,
                            end: 100
                        }
                    ],
                    series: [
                        {
                            name: 'K线',
                            type: 'candlestick',
                            data: klineData.map(item => [item[1], item[2], item[3], item[4]]),
                            itemStyle: {
                                color: '#ef232a',
                                color0: '#14b143',
                                borderColor: '#ef232a',
                                borderColor0: '#14b143'
                            }
                        }
                    ]
                };

                klineChart.setOption(option);
            } catch (error) {
                console.error('加载K线数据失败:', error);
            }
        }

        // 加载深度数据
        async function loadDepthData() {
            const symbol = document.getElementById('depthSymbol').value || '000001.SZ';
            
            // 生成模拟深度数据
            const buyDepth = [];
            const sellDepth = [];
            const basePrice = 12.5;

            for (let i = 1; i <= 10; i++) {
                buyDepth.push([
                    (basePrice - i * 0.01).toFixed(2),
                    Math.floor(Math.random() * 10000) + 1000
                ]);
                sellDepth.push([
                    (basePrice + i * 0.01).toFixed(2),
                    Math.floor(Math.random() * 10000) + 1000
                ]);
            }

            const option = {
                title: {
                    text: `${symbol} 买卖深度`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['买盘', '卖盘'],
                    bottom: 10
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: [...buyDepth.map(item => item[0]).reverse(), ...sellDepth.map(item => item[0])]
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '买盘',
                        type: 'bar',
                        data: [...buyDepth.map(item => item[1]).reverse(), ...new Array(10).fill(0)],
                        itemStyle: {
                            color: '#14b143'
                        }
                    },
                    {
                        name: '卖盘',
                        type: 'bar',
                        data: [...new Array(10).fill(0), ...sellDepth.map(item => item[1])],
                        itemStyle: {
                            color: '#ef232a'
                        }
                    }
                ]
            };

            depthChart.setOption(option);
        }

        // 加载技术指标数据
        async function loadIndicatorData() {
            const data = [];
            const dates = [];
            
            for (let i = 0; i < 30; i++) {
                const date = new Date();
                date.setDate(date.getDate() - (29 - i));
                dates.push(date.toISOString().split('T')[0]);
                
                if (currentIndicator === 'MACD') {
                    data.push([
                        (Math.random() - 0.5) * 0.5,  // MACD
                        (Math.random() - 0.5) * 0.3,  // Signal
                        (Math.random() - 0.5) * 0.2   // Histogram
                    ]);
                } else if (currentIndicator === 'RSI') {
                    data.push(Math.random() * 100);
                } else if (currentIndicator === 'KDJ') {
                    data.push([
                        Math.random() * 100,  // K
                        Math.random() * 100,  // D
                        Math.random() * 100   // J
                    ]);
                }
            }

            let option = {};

            if (currentIndicator === 'MACD') {
                option = {
                    title: {
                        text: 'MACD指标',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    legend: {
                        data: ['MACD', 'Signal', 'Histogram'],
                        bottom: 10
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [
                        {
                            name: 'MACD',
                            type: 'line',
                            data: data.map(item => item[0]),
                            itemStyle: { color: '#3498db' }
                        },
                        {
                            name: 'Signal',
                            type: 'line',
                            data: data.map(item => item[1]),
                            itemStyle: { color: '#e74c3c' }
                        },
                        {
                            name: 'Histogram',
                            type: 'bar',
                            data: data.map(item => item[2]),
                            itemStyle: { color: '#95a5a6' }
                        }
                    ]
                };
            } else if (currentIndicator === 'RSI') {
                option = {
                    title: {
                        text: 'RSI指标',
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: dates
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 100
                    },
                    series: [
                        {
                            name: 'RSI',
                            type: 'line',
                            data: data,
                            itemStyle: { color: '#9b59b6' },
                            markLine: {
                                data: [
                                    { yAxis: 70, lineStyle: { color: '#e74c3c' } },
                                    { yAxis: 30, lineStyle: { color: '#27ae60' } }
                                ]
                            }
                        }
                    ]
                };
            }

            indicatorChart.setOption(option);
        }

        // 加载热力图数据
        async function loadHeatmapData() {
            const sectorData = [
                { name: '银行', value: 2.5, children: [
                    { name: '平安银行', value: 1.8 },
                    { name: '招商银行', value: 2.1 },
                    { name: '工商银行', value: 0.9 }
                ]},
                { name: '白酒', value: -1.2, children: [
                    { name: '贵州茅台', value: -0.8 },
                    { name: '五粮液', value: -1.5 },
                    { name: '泸州老窖', value: -2.1 }
                ]},
                { name: '科技', value: 3.2, children: [
                    { name: '腾讯控股', value: 4.1 },
                    { name: '阿里巴巴', value: 2.8 },
                    { name: '美团', value: 2.7 }
                ]},
                { name: '医药', value: 1.8, children: [
                    { name: '恒瑞医药', value: 2.2 },
                    { name: '药明康德', value: 1.1 },
                    { name: '迈瑞医疗', value: 2.1 }
                ]}
            ];

            const option = {
                title: {
                    text: currentHeatmapType === 'sector' ? '行业热力图' : '概念热力图',
                    left: 'center'
                },
                tooltip: {
                    formatter: function (info) {
                        const value = info.value;
                        return `${info.name}: ${value > 0 ? '+' : ''}${value.toFixed(2)}%`;
                    }
                },
                series: [
                    {
                        type: 'treemap',
                        data: sectorData,
                        leafDepth: 1,
                        label: {
                            show: true,
                            formatter: '{b}\n{c}%'
                        },
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        levels: [
                            {
                                itemStyle: {
                                    borderColor: '#777',
                                    borderWidth: 0,
                                    gapWidth: 1
                                }
                            },
                            {
                                colorSaturation: [0.35, 0.5],
                                itemStyle: {
                                    gapWidth: 1,
                                    borderColorSaturation: 0.6
                                }
                            }
                        ],
                        visualMap: {
                            type: 'continuous',
                            dimension: 'value',
                            min: -3,
                            max: 5,
                            inRange: {
                                color: ['#d73027', '#f46d43', '#fdae61', '#fee08b', '#e6f598', '#abdda4', '#66c2a5', '#3288bd']
                            }
                        }
                    }
                ]
            };

            heatmapChart.setOption(option);
        }

        // 切换K线周期
        function changeKlinePeriod(period) {
            currentKlinePeriod = period;
            
            // 更新按钮状态
            document.querySelectorAll('.control-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            loadKlineData();
        }

        // 切换技术指标
        function changeIndicator(indicator) {
            currentIndicator = indicator;
            
            // 更新按钮状态
            const indicatorBtns = document.querySelectorAll('.chart-panel')[2].querySelectorAll('.control-btn');
            indicatorBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            loadIndicatorData();
        }

        // 切换热力图类型
        function changeHeatmapType(type) {
            currentHeatmapType = type;
            
            // 更新按钮状态
            const heatmapBtns = document.querySelectorAll('.chart-panel')[3].querySelectorAll('.control-btn');
            heatmapBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            loadHeatmapData();
        }

        // 刷新图表
        function refreshKlineChart() {
            loadKlineData();
        }

        function refreshDepthChart() {
            loadDepthData();
        }

        // 窗口大小改变时重新调整图表
        window.addEventListener('resize', function() {
            if (klineChart) klineChart.resize();
            if (depthChart) depthChart.resize();
            if (indicatorChart) indicatorChart.resize();
            if (heatmapChart) heatmapChart.resize();
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 市场数据可视化页面已加载');
            initCharts();
            
            // 定时更新市场统计数据
            setInterval(function() {
                document.getElementById('tradingVolume').textContent = 
                    '¥' + (Math.random() * 2000 + 7000).toFixed(0) + '亿';
                document.getElementById('upCount').textContent = 
                    Math.floor(Math.random() * 500 + 2000);
                document.getElementById('downCount').textContent = 
                    Math.floor(Math.random() * 500 + 1500);
            }, 5000);
        });
    </script>
</body>
</html>
