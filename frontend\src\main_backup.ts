/**
 * 应用主入口文件 - 简化版
 * 移除可能导致问题的复杂导入
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'

// 基础导入
import App from './App.vue'

// 尝试安全导入其他模块
let router, config, i18n, http

try {
  router = (await import('./router')).default
} catch (error) {
  console.error('❌ 路由模块加载失败:', error)
  // 创建一个简单的路由
  router = { push: () => {}, replace: () => {} }
}

try {
  config = (await import('@/config')).config
} catch (error) {
  console.error('❌ 配置模块加载失败:', error)
  // 使用默认配置
  config = {
    app: { name: '量化投资平台', isDev: true, isProd: false },
    api: { baseURL: 'http://localhost:8000/api/v1' },
    auth: { tokenKey: 'auth_token' }
  }
}

try {
  i18n = (await import('@/locales')).default
} catch (error) {
  console.error('❌ 国际化模块加载失败:', error)
  // 创建一个简单的i18n
  i18n = { install: () => {} }
}

try {
  http = (await import('@/api/http')).default
  if (http && http.defaults) {
    http.defaults.baseURL = config.api.baseURL
  }
} catch (error) {
  console.error('❌ HTTP模块加载失败:', error)
}

// 尝试导入样式
try {
  await import('element-plus/theme-chalk/index.css')
} catch (error) {
  console.warn('⚠️ Element Plus样式加载失败:', error)
}

// 尝试导入Element Plus组件
let ElButton, ElMessage, ElMessageBox, ElLoading
try {
  const elementPlus = await import('element-plus')
  ElButton = elementPlus.ElButton
  ElMessage = elementPlus.ElMessage
  ElMessageBox = elementPlus.ElMessageBox
  ElLoading = elementPlus.ElLoading
} catch (error) {
  console.warn('⚠️ Element Plus组件加载失败:', error)
}

// 创建Vue应用实例
const app = createApp(App)

// =======================
// Pinia状态管理配置
// =======================
const pinia = createPinia()
app.use(pinia)

// =======================
// 路由配置
// =======================
app.use(router)

// =======================
// Element Plus配置 (按需注册核心组件)
// =======================
app.use(ElButton)
app.use(ElLoading)
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$messageBox = ElMessageBox

// =======================
// 国际化配置
// =======================
app.use(i18n)

// =======================
// 异步加载Element Plus图标 (性能优化)
// =======================
let iconsLoaded = false
const loadIcons = async () => {
  if (iconsLoaded) return
  try {
    const ElementPlusIconsVue = await loadElementPlusIcons()
    for (const [key, component] of Object.entries(ElementPlusIconsVue.default || ElementPlusIconsVue)) {
      app.component(key, component)
    }
    iconsLoaded = true
    console.log('✅ Element Plus图标加载完成')
  } catch (error) {
    console.error('❌ Element Plus图标加载失败:', error)
  }
}

// =======================
// 异步加载全局组件 (性能优化)
// =======================
let componentsLoaded = false
const loadComponents = async () => {
  if (componentsLoaded) return
  try {
    const GlobalComponents = await loadGlobalComponents()
    app.use(GlobalComponents.default || GlobalComponents)
    componentsLoaded = true
    console.log('✅ 全局组件加载完成')
  } catch (error) {
    console.error('❌ 全局组件加载失败:', error)
  }
}

// =======================
// 安全防护初始化
// =======================
try {
  const { initSecurity } = await import('@/utils/security')
  initSecurity()
  console.log('🔒 安全防护已初始化')
} catch (error) {
  console.warn('⚠️ 安全防护初始化失败:', error)
}

// =======================
// 性能监控初始化
// =======================
// 性能监控服务会自动初始化，这里只是确保导入
console.log('📊 性能监控服务已启动')

// =======================
// ResizeObserver 错误修复
// =======================
setupResizeObserverFix()

// =======================
// 错误处理系统初始化
// =======================
setupErrorHandler(app)

// =======================
// 全局属性配置
// =======================
app.config.globalProperties.$config = config

// =======================
// 异步加载ECharts (性能优化)
// =======================
let echartsLoaded = false
const loadEChartsLib = async () => {
  if (echartsLoaded) return
  try {
    const echarts = await loadECharts()
    app.config.globalProperties.$echarts = echarts.default || echarts

    // 将ECharts添加到window对象，供调试和第三方库使用
    if (typeof window !== 'undefined') {
      (window as any).echarts = echarts.default || echarts
    }
    echartsLoaded = true
    console.log('✅ ECharts加载完成')
    return echarts.default || echarts
  } catch (error) {
    console.error('❌ ECharts加载失败:', error)
    return null
  }
}

// =======================
// 全局错误处理
// =======================
app.config.errorHandler = (err: unknown, instance, info) => {
  console.error('🚨 全局错误捕获:', {
    error: err,
    errorInfo: info,
    instance: instance?.$options?.name || 'Unknown Component',
    timestamp: new Date().toISOString()
  })

  // 开发环境显示详细错误
  if (config.app.isDev) {
    console.error('错误堆栈:', (err as Error).stack)
  }

  // 生产环境发送错误到监控服务
  if (config.app.isProd) {
    // 集成Sentry或其他错误监控
    reportError(err as Error, {
      component: instance?.$options?.name,
      errorInfo: info,
      userAgent: navigator.userAgent,
      url: window.location.href
    })
  }
}

// =======================
// 未捕获的Promise异常处理
// =======================
window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 未处理的Promise异常:', event.reason)

  if (config.app.isProd) {
    reportError(new Error(event.reason), {
      type: 'unhandledrejection',
      url: window.location.href
    })
  }

  // 阻止默认的控制台错误输出
  event.preventDefault()
})

// =======================
// 全局异常处理
// =======================
window.addEventListener('error', (event) => {
  console.error('🚨 全局异常:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error
  })

  if (config.app.isProd) {
    reportError(event.error || new Error(event.message), {
      type: 'javascript',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  }
})

// =======================
// 资源加载错误处理
// =======================
window.addEventListener('error', (event: ErrorEvent) => {
  const target = event.target as HTMLElement
  if (target && target !== window) {
    console.error('🚨 资源加载失败:', {
      tagName: target.tagName,
      source: (target as HTMLImageElement | HTMLScriptElement | HTMLLinkElement).src || (target as HTMLLinkElement).href,
      message: event.message
    })

    if (config.app.isProd) {
      reportError(new Error(`资源加载失败: ${target.tagName}`), {
        type: 'resource',
        source: (target as HTMLImageElement | HTMLScriptElement | HTMLLinkElement).src || (target as HTMLLinkElement).href,
        tagName: target.tagName
      })
    }
  }
}, true)

// =======================
// 网络状态监听
// =======================
window.addEventListener('online', () => {
  console.log('🌐 网络连接已恢复')
  // 可以在这里重新初始化WebSocket连接等
})

window.addEventListener('offline', () => {
  console.log('🚫 网络连接已断开')
  // 可以在这里显示离线提示
})

// =======================
// 页面可见性监听
// =======================
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    console.log('📱 页面已隐藏')
    // 可以在这里暂停不必要的请求和动画
  } else {
    console.log('👀 页面已显示')
    // 可以在这里恢复数据更新
  }
})

// =======================
// 图表错误监听
// =======================
window.addEventListener('chart-init-error', (event: CustomEvent) => {
  console.error('📊 图表初始化错误:', event.detail)

  // 在生产环境中可以显示用户友好的错误提示
  if (config.app.isProd) {
    // 可以显示一个全局的错误提示
    // ElMessage.error('图表加载失败，请刷新页面重试')
  }
})

// =======================
// 应用生命周期钩子
// =======================
app.config.warnHandler = (msg, instance, trace) => {
  // 自定义警告处理
  if (config.app.isDev) {
    console.warn('⚠️ Vue警告:', msg)
    console.warn('组件追踪:', trace)
  }
}

// =======================
// 性能监控
// =======================
if (config.app.isProd && 'performance' in window) {
  window.addEventListener('load', () => {
    // 页面加载性能监控
    setTimeout(() => {
      const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming

      if (perfData) {
        const loadTime = perfData.loadEventEnd - perfData.navigationStart
        const domContentLoadedTime = perfData.domContentLoadedEventEnd - perfData.navigationStart
        const firstPaintTime = performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime || 0

        console.log('📊 页面性能指标:', {
          页面加载时间: `${loadTime}ms`,
          DOM加载时间: `${domContentLoadedTime}ms`,
          首次绘制时间: `${firstPaintTime}ms`
        })

        // 性能数据上报
        reportPerformance({
          loadTime,
          domContentLoadedTime,
          firstPaintTime,
          url: window.location.href
        })
      }
    }, 0)
  })
}

// =======================
// 辅助函数
// =======================

/**
 * 错误上报函数
 */
function reportError(error: Error, context: Record<string, unknown> = {}) {
  // 这里可以集成Sentry、LogRocket等错误监控服务
  try {
    // 模拟发送错误报告
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: localStorage.getItem(config.auth.tokenKey) ? 'logged_in' : 'anonymous',
      ...context
    }

    // 发送到错误监控服务
    // Sentry.captureException(error, { contexts: { custom: context } })

    console.log('📤 错误报告已发送:', errorReport)
  } catch (reportingError) {
    console.error('❌ 错误报告发送失败:', reportingError)
  }
}

/**
 * 性能数据上报函数
 */
function reportPerformance(perfData: Record<string, unknown>) {
  try {
    // 发送性能数据到分析服务
    console.log('📊 性能数据已记录:', perfData)

    // 实际项目中可以发送到Analytics服务
    // gtag('event', 'timing_complete', perfData)
  } catch (error) {
    console.error('❌ 性能数据上报失败:', error)
  }
}

// =======================
// 开发环境调试工具
// =======================
if (config.app.isDev) {
  // 暴露一些调试工具到全局
  (window as Record<string, unknown>).__APP_DEBUG__ = {
    app,
    router,
    pinia,
    config,
    version: config.app.version
  }

  console.log('🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问')
  console.log(`📱 应用版本: ${config.app.version}`)
  console.log(`🌐 API地址: ${config.api.baseURL}`)
  console.log(`🔌 WebSocket地址: ${config.api.websocket.url}`)
}

// =======================
// PWA支持
// =======================
if (config.app.enablePWA && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ Service Worker 注册成功:', registration.scope)
      })
      .catch((error) => {
        console.error('❌ Service Worker 注册失败:', error)
      })
  })
}

// =======================
// 应用启动 (优化启动流程)
// =======================

// 立即挂载应用，提升首屏渲染速度
app.mount('#app')

// 异步加载非关键资源
Promise.all([
  loadIcons(),
  loadComponents(),
  loadEChartsLib()
]).then(() => {
  console.log('🎉 所有资源加载完成')
}).catch(error => {
  console.error('❌ 资源加载过程中出现错误:', error)
})

console.log(`🚀 ${config.app.name} v${config.app.version} 启动成功!`)
console.log(`🌍 运行环境: ${config.app.isDev ? '开发' : '生产'}`)
console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)

export default app
