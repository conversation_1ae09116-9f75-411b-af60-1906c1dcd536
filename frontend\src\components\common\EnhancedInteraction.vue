<template>
  <div class="enhanced-interaction">
    <!-- 快捷操作面板 -->
    <div class="quick-actions-panel" v-if="showQuickActions">
      <div class="panel-header">
        <h4>快捷操作</h4>
        <el-button @click="showQuickActions = false" type="text" size="small">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="actions-grid">
        <el-button 
          v-for="action in quickActions" 
          :key="action.key"
          @click="handleQuickAction(action)"
          :type="action.type"
          :icon="action.icon"
          size="small"
          class="action-btn"
        >
          {{ action.label }}
        </el-button>
      </div>
    </div>

    <!-- 智能搜索框 -->
    <div class="smart-search" v-if="showSearch">
      <el-autocomplete
        v-model="searchQuery"
        :fetch-suggestions="querySearch"
        placeholder="搜索股票、策略、订单..."
        @select="handleSelect"
        @keyup.enter="handleSearch"
        clearable
        style="width: 100%"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        <template #default="{ item }">
          <div class="search-item">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-type">{{ item.type }}</div>
          </div>
        </template>
      </el-autocomplete>
    </div>

    <!-- 上下文菜单 -->
    <el-dropdown
      v-if="showContextMenu"
      trigger="contextmenu"
      @command="handleContextCommand"
    >
      <span class="context-trigger">右键菜单</span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="copy">复制</el-dropdown-item>
          <el-dropdown-item command="paste">粘贴</el-dropdown-item>
          <el-dropdown-item command="refresh">刷新</el-dropdown-item>
          <el-dropdown-item command="export" divided>导出数据</el-dropdown-item>
          <el-dropdown-item command="settings">设置</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 拖拽区域 -->
    <div 
      v-if="showDragDrop"
      class="drag-drop-area"
      @dragover.prevent
      @drop="handleDrop"
      :class="{ 'drag-over': isDragOver }"
      @dragenter="isDragOver = true"
      @dragleave="isDragOver = false"
    >
      <el-icon><Upload /></el-icon>
      <p>拖拽文件到此处上传</p>
    </div>

    <!-- 快捷键提示 -->
    <div v-if="showShortcuts" class="shortcuts-panel">
      <div class="panel-header">
        <h4>快捷键</h4>
        <el-button @click="showShortcuts = false" type="text" size="small">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="shortcuts-list">
        <div v-for="shortcut in shortcuts" :key="shortcut.key" class="shortcut-item">
          <kbd>{{ shortcut.key }}</kbd>
          <span>{{ shortcut.description }}</span>
        </div>
      </div>
    </div>

    <!-- 浮动操作按钮 -->
    <div class="floating-actions">
      <el-button 
        circle 
        type="primary" 
        @click="toggleQuickActions"
        class="fab-main"
        :class="{ 'fab-active': showQuickActions }"
      >
        <el-icon><Plus /></el-icon>
      </el-button>
      
      <transition-group name="fab" tag="div" class="fab-group">
        <el-button 
          v-show="showQuickActions"
          v-for="(fab, index) in fabActions" 
          :key="fab.key"
          circle 
          :type="fab.type"
          @click="handleFabAction(fab)"
          class="fab-item"
          :style="{ '--index': index }"
        >
          <el-icon :class="fab.icon"></el-icon>
        </el-button>
      </transition-group>
    </div>

    <!-- 通知中心 -->
    <div v-if="showNotifications" class="notifications-panel">
      <div class="panel-header">
        <h4>通知中心</h4>
        <el-badge :value="unreadCount" class="notification-badge">
          <el-button @click="showNotifications = false" type="text" size="small">
            <el-icon><Bell /></el-icon>
          </el-button>
        </el-badge>
      </div>
      
      <div class="notifications-list">
        <div 
          v-for="notification in notifications" 
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.read }"
          @click="markAsRead(notification)"
        >
          <div class="notification-content">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Close, Search, Plus, Upload, Bell,
  Refresh, Download, Setting, Copy, Share
} from '@element-plus/icons-vue'

// Props
interface Props {
  enableQuickActions?: boolean
  enableSearch?: boolean
  enableContextMenu?: boolean
  enableDragDrop?: boolean
  enableShortcuts?: boolean
  enableNotifications?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enableQuickActions: true,
  enableSearch: true,
  enableContextMenu: true,
  enableDragDrop: false,
  enableShortcuts: true,
  enableNotifications: true
})

// Emits
const emit = defineEmits<{
  quickAction: [action: any]
  search: [query: string]
  contextAction: [command: string]
  fileUpload: [files: FileList]
  shortcut: [key: string]
  notification: [notification: any]
}>()

// 响应式数据
const showQuickActions = ref(false)
const showSearch = ref(props.enableSearch)
const showContextMenu = ref(props.enableContextMenu)
const showDragDrop = ref(props.enableDragDrop)
const showShortcuts = ref(false)
const showNotifications = ref(false)
const isDragOver = ref(false)
const searchQuery = ref('')

// 快捷操作配置
const quickActions = reactive([
  { key: 'buy', label: '快速买入', type: 'success', icon: 'Plus' },
  { key: 'sell', label: '快速卖出', type: 'danger', icon: 'Minus' },
  { key: 'refresh', label: '刷新数据', type: 'info', icon: 'Refresh' },
  { key: 'export', label: '导出数据', type: 'warning', icon: 'Download' },
  { key: 'settings', label: '设置', type: 'default', icon: 'Setting' }
])

// 浮动按钮配置
const fabActions = reactive([
  { key: 'search', type: 'info', icon: 'Search' },
  { key: 'shortcuts', type: 'warning', icon: 'Keyboard' },
  { key: 'notifications', type: 'success', icon: 'Bell' }
])

// 快捷键配置
const shortcuts = reactive([
  { key: 'Ctrl + B', description: '快速买入' },
  { key: 'Ctrl + S', description: '快速卖出' },
  { key: 'Ctrl + R', description: '刷新数据' },
  { key: 'Ctrl + F', description: '搜索' },
  { key: 'Ctrl + H', description: '显示快捷键' },
  { key: 'Esc', description: '关闭面板' }
])

// 通知数据
const notifications = reactive([
  {
    id: 1,
    title: '订单成交',
    message: '您的买入订单已成交',
    time: new Date(),
    read: false
  },
  {
    id: 2,
    title: '价格提醒',
    message: '平安银行达到目标价格',
    time: new Date(Date.now() - 300000),
    read: false
  },
  {
    id: 3,
    title: '系统通知',
    message: '系统将在30分钟后维护',
    time: new Date(Date.now() - 600000),
    read: true
  }
])

// 计算属性
const unreadCount = computed(() => 
  notifications.filter(n => !n.read).length
)

// 方法
const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value
}

const handleQuickAction = (action: any) => {
  emit('quickAction', action)
  ElMessage.success(`执行操作: ${action.label}`)
}

const handleFabAction = (fab: any) => {
  switch (fab.key) {
    case 'search':
      showSearch.value = !showSearch.value
      break
    case 'shortcuts':
      showShortcuts.value = !showShortcuts.value
      break
    case 'notifications':
      showNotifications.value = !showNotifications.value
      break
  }
}

const querySearch = (queryString: string, cb: Function) => {
  const results = [
    { title: '平安银行', type: '股票', value: '000001' },
    { title: '招商银行', type: '股票', value: '600036' },
    { title: '均线策略', type: '策略', value: 'ma_strategy' },
    { title: '最新订单', type: '订单', value: 'recent_orders' }
  ].filter(item => 
    item.title.toLowerCase().includes(queryString.toLowerCase())
  )
  cb(results)
}

const handleSelect = (item: any) => {
  ElMessage.success(`选择了: ${item.title}`)
  emit('search', item.value)
}

const handleSearch = () => {
  if (searchQuery.value) {
    emit('search', searchQuery.value)
    ElMessage.info(`搜索: ${searchQuery.value}`)
  }
}

const handleContextCommand = (command: string) => {
  emit('contextAction', command)
  ElMessage.info(`执行: ${command}`)
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    emit('fileUpload', files)
    ElMessage.success(`上传 ${files.length} 个文件`)
  }
}

const markAsRead = (notification: any) => {
  notification.read = true
  emit('notification', notification)
}

const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / 60000)
  
  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`
  return time.toLocaleDateString()
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.ctrlKey) {
    switch (event.key) {
      case 'b':
        event.preventDefault()
        emit('shortcut', 'buy')
        break
      case 's':
        event.preventDefault()
        emit('shortcut', 'sell')
        break
      case 'r':
        event.preventDefault()
        emit('shortcut', 'refresh')
        break
      case 'f':
        event.preventDefault()
        showSearch.value = true
        break
      case 'h':
        event.preventDefault()
        showShortcuts.value = !showShortcuts.value
        break
    }
  } else if (event.key === 'Escape') {
    showQuickActions.value = false
    showShortcuts.value = false
    showNotifications.value = false
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.enhanced-interaction {
  position: relative;
}

.quick-actions-panel,
.shortcuts-panel,
.notifications-panel {
  position: fixed;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  padding: 20px;
  width: 300px;
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-btn {
  width: 100%;
}

.smart-search {
  margin-bottom: 20px;
}

.search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-type {
  font-size: 12px;
  color: #999;
}

.drag-drop-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
}

.drag-drop-area.drag-over {
  border-color: #409eff;
  background: #f0f9ff;
}

.shortcuts-list {
  max-height: 300px;
  overflow-y: auto;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.shortcut-item:last-child {
  border-bottom: none;
}

kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 12px;
}

.floating-actions {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.fab-main {
  width: 56px;
  height: 56px;
  transition: all 0.3s ease;
}

.fab-main.fab-active {
  transform: rotate(45deg);
}

.fab-group {
  position: absolute;
  bottom: 70px;
  right: 0;
}

.fab-item {
  display: block;
  margin-bottom: 10px;
  width: 40px;
  height: 40px;
  transform: translateY(calc(var(--index) * 50px));
}

.fab-enter-active,
.fab-leave-active {
  transition: all 0.3s ease;
}

.fab-enter-from,
.fab-leave-to {
  opacity: 0;
  transform: translateY(20px) scale(0.8);
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
}

.notification-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background 0.2s ease;
}

.notification-item:hover {
  background: #f9f9f9;
}

.notification-item.unread {
  background: #f0f9ff;
  border-left: 3px solid #409eff;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
}

.notification-message {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.notification-time {
  color: #999;
  font-size: 12px;
}

.notification-badge {
  margin-left: 10px;
}
</style>
