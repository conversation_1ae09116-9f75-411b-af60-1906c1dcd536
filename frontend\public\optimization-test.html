<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 短期优化功能测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .optimization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .optimization-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .optimization-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .optimization-card.real-data {
            border-left-color: #e74c3c;
        }

        .optimization-card.monitoring {
            border-left-color: #f39c12;
        }

        .optimization-card.compression {
            border-left-color: #27ae60;
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 2em;
            margin-right: 15px;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }
        .status-unknown { background-color: #95a5a6; }

        .result-box {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .stat-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: scale(1.05);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-text { color: #27ae60; font-weight: bold; }
        .error-text { color: #e74c3c; font-weight: bold; }
        .warning-text { color: #f39c12; font-weight: bold; }
        .info-text { color: #3498db; font-weight: bold; }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 短期优化功能测试</h1>
            <p>真实数据源集成 • 监控告警机制 • 压缩算法优化</p>
        </div>

        <div class="content">
            <!-- 优化功能测试 -->
            <div class="optimization-grid">
                <!-- 真实数据源集成 -->
                <div class="optimization-card real-data">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-title">真实数据源集成</div>
                    </div>
                    <div class="card-description">
                        集成tushare、akshare等真实市场数据源，实现多数据源切换和故障转移机制
                    </div>
                    <div class="button-group">
                        <button class="btn" onclick="testDataSources()">🔍 检查数据源状态</button>
                        <button class="btn success" onclick="testRealDataAPI()">📈 测试真实数据API</button>
                        <button class="btn warning" onclick="testFailover()">🔄 测试故障转移</button>
                    </div>
                    <div class="stats-grid" id="dataSourceStats">
                        <div class="stat-item">
                            <div class="stat-value" id="totalSources">--</div>
                            <div class="stat-label">数据源总数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="healthySources">--</div>
                            <div class="stat-label">健康数据源</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="primarySource">--</div>
                            <div class="stat-label">主要数据源</div>
                        </div>
                    </div>
                    <div class="result-box" id="dataSourceResults">
                        <div class="info-text">点击按钮开始测试数据源...</div>
                    </div>
                </div>

                <!-- 监控告警机制 -->
                <div class="optimization-card monitoring">
                    <div class="card-header">
                        <div class="card-icon">📡</div>
                        <div class="card-title">监控告警机制</div>
                    </div>
                    <div class="card-description">
                        实时监控系统性能、API调用、存储空间等关键指标，提供智能告警功能
                    </div>
                    <div class="button-group">
                        <button class="btn" onclick="testMonitoring()">📊 查看监控指标</button>
                        <button class="btn warning" onclick="testAlerts()">🚨 查看告警信息</button>
                        <button class="btn success" onclick="testStorageMonitor()">💾 存储监控</button>
                    </div>
                    <div class="stats-grid" id="monitoringStats">
                        <div class="stat-item">
                            <div class="stat-value" id="cpuUsage">--</div>
                            <div class="stat-label">CPU使用率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="memoryUsage">--</div>
                            <div class="stat-label">内存使用率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="activeAlerts">--</div>
                            <div class="stat-label">活跃告警</div>
                        </div>
                    </div>
                    <div class="result-box" id="monitoringResults">
                        <div class="info-text">点击按钮开始监控测试...</div>
                    </div>
                </div>

                <!-- 压缩算法优化 -->
                <div class="optimization-card compression">
                    <div class="card-header">
                        <div class="card-icon">🗜️</div>
                        <div class="card-title">压缩算法优化</div>
                    </div>
                    <div class="card-description">
                        测试不同压缩级别，优化gzip压缩性能，实现自适应压缩策略
                    </div>
                    <div class="button-group">
                        <button class="btn" onclick="testCompressionStats()">📈 压缩统计</button>
                        <button class="btn success" onclick="optimizeCompression()">⚡ 优化压缩算法</button>
                        <button class="btn warning" onclick="benchmarkCompression()">🏃 压缩基准测试</button>
                    </div>
                    <div class="stats-grid" id="compressionStats">
                        <div class="stat-item">
                            <div class="stat-value" id="compressionRatio">--</div>
                            <div class="stat-label">平均压缩比</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="compressionTime">--</div>
                            <div class="stat-label">平均压缩时间</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value" id="optimalAlgorithm">--</div>
                            <div class="stat-label">最优算法</div>
                        </div>
                    </div>
                    <div class="result-box" id="compressionResults">
                        <div class="info-text">点击按钮开始压缩测试...</div>
                    </div>
                </div>
            </div>

            <!-- 综合优化状态 -->
            <div class="optimization-card">
                <div class="card-header">
                    <div class="card-icon">🎯</div>
                    <div class="card-title">综合优化状态</div>
                </div>
                <div class="card-description">
                    显示所有短期优化功能的综合状态和性能指标
                </div>
                <div class="button-group">
                    <button class="btn success" onclick="runAllTests()">🚀 运行全部测试</button>
                    <button class="btn" onclick="refreshAllStats()">🔄 刷新所有状态</button>
                    <button class="btn danger" onclick="clearAllResults()">🧹 清空结果</button>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="overallProgress" style="width: 0%"></div>
                </div>
                <div class="result-box" id="overallResults">
                    <div class="info-text">准备运行综合测试...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let testProgress = 0;
        
        // 显示加载状态
        function showLoading(elementId, message = '加载中...') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<span class="loading"></span>${message}`;
        }

        // 显示结果
        function showResult(elementId, content, className = 'info-text') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${className}">${content}</div>`;
        }

        // 格式化JSON显示
        function formatJSON(obj) {
            return JSON.stringify(obj, null, 2);
        }

        // 更新进度条
        function updateProgress(percent) {
            document.getElementById('overallProgress').style.width = percent + '%';
        }

        // 测试数据源状态
        async function testDataSources() {
            try {
                showLoading('dataSourceResults', '检查数据源状态...');
                
                const response = await fetch(`${API_BASE}/datasources/status`);
                const result = await response.json();
                
                if (result.data && result.data.sources) {
                    const sources = result.data.sources;
                    const summary = result.data.summary;
                    
                    // 更新统计
                    document.getElementById('totalSources').textContent = summary.total_sources || 0;
                    document.getElementById('healthySources').textContent = summary.healthy_sources || 0;
                    
                    // 找到主要数据源
                    let primarySource = '--';
                    for (const [name, info] of Object.entries(sources)) {
                        if (info.priority === 1 && info.status === 'healthy') {
                            primarySource = name;
                            break;
                        }
                    }
                    document.getElementById('primarySource').textContent = primarySource;
                    
                    // 显示详细结果
                    let resultHtml = '<div class="success-text">✅ 数据源状态检查完成</div><br>';
                    for (const [name, info] of Object.entries(sources)) {
                        const statusClass = info.status === 'healthy' ? 'success-text' : 
                                          info.status === 'degraded' ? 'warning-text' : 'error-text';
                        resultHtml += `<div class="${statusClass}">${name}: ${info.status} (优先级: ${info.priority})</div>`;
                    }
                    resultHtml += `<pre>${formatJSON(result.data)}</pre>`;
                    
                    showResult('dataSourceResults', resultHtml);
                } else {
                    showResult('dataSourceResults', '❌ 获取数据源状态失败', 'error-text');
                }
                
            } catch (error) {
                showResult('dataSourceResults', `❌ 数据源测试失败: ${error.message}`, 'error-text');
            }
        }

        // 测试真实数据API
        async function testRealDataAPI() {
            try {
                showLoading('dataSourceResults', '测试真实数据API...');
                
                // 测试增强行情API
                const response = await fetch(`${API_BASE}/market/enhanced/quote/000001.SZ`);
                const result = await response.json();
                
                if (result.data) {
                    showResult('dataSourceResults', 
                        `<div class="success-text">✅ 真实数据API测试成功</div>
                         <div>数据源: ${result.data.data_source || '未知'}</div>
                         <div>股票: ${result.data.name || result.data.symbol}</div>
                         <div>价格: ${result.data.current_price || result.data.price}</div>
                         <pre>${formatJSON(result)}</pre>`
                    );
                } else {
                    showResult('dataSourceResults', '⚠️ API返回空数据', 'warning-text');
                }
                
            } catch (error) {
                showResult('dataSourceResults', `❌ 真实数据API测试失败: ${error.message}`, 'error-text');
            }
        }

        // 测试监控指标
        async function testMonitoring() {
            try {
                showLoading('monitoringResults', '获取监控指标...');
                
                const response = await fetch(`${API_BASE}/monitoring/metrics`);
                const result = await response.json();
                
                if (result.data && result.data.metrics) {
                    const metrics = result.data.metrics;
                    
                    // 更新统计
                    const cpuMetric = metrics['system.cpu.usage_percent'];
                    const memoryMetric = metrics['system.memory.usage_percent'];
                    
                    if (cpuMetric) {
                        document.getElementById('cpuUsage').textContent = `${cpuMetric.current_value?.toFixed(1) || '--'}%`;
                    }
                    if (memoryMetric) {
                        document.getElementById('memoryUsage').textContent = `${memoryMetric.current_value?.toFixed(1) || '--'}%`;
                    }
                    
                    showResult('monitoringResults', 
                        `<div class="success-text">✅ 监控指标获取成功</div>
                         <div>监控状态: ${result.data.monitoring_enabled ? '启用' : '禁用'}</div>
                         <div>指标数量: ${Object.keys(metrics).length}</div>
                         <pre>${formatJSON(result.data)}</pre>`
                    );
                } else {
                    showResult('monitoringResults', '⚠️ 监控数据为空', 'warning-text');
                }
                
            } catch (error) {
                showResult('monitoringResults', `❌ 监控测试失败: ${error.message}`, 'error-text');
            }
        }

        // 测试告警信息
        async function testAlerts() {
            try {
                showLoading('monitoringResults', '获取告警信息...');
                
                const response = await fetch(`${API_BASE}/monitoring/alerts`);
                const result = await response.json();
                
                if (result.data) {
                    document.getElementById('activeAlerts').textContent = result.count || 0;
                    
                    let alertsHtml = `<div class="success-text">✅ 告警信息获取成功</div>`;
                    alertsHtml += `<div>告警总数: ${result.count}</div>`;
                    
                    if (result.count > 0) {
                        alertsHtml += '<div>最近告警:</div>';
                        result.data.slice(0, 3).forEach(alert => {
                            const levelClass = alert.level === 'critical' ? 'error-text' : 
                                             alert.level === 'error' ? 'error-text' :
                                             alert.level === 'warning' ? 'warning-text' : 'info-text';
                            alertsHtml += `<div class="${levelClass}">${alert.title}: ${alert.message}</div>`;
                        });
                    }
                    
                    alertsHtml += `<pre>${formatJSON(result)}</pre>`;
                    showResult('monitoringResults', alertsHtml);
                } else {
                    showResult('monitoringResults', '⚠️ 告警数据为空', 'warning-text');
                }
                
            } catch (error) {
                showResult('monitoringResults', `❌ 告警测试失败: ${error.message}`, 'error-text');
            }
        }

        // 测试存储监控
        async function testStorageMonitor() {
            try {
                showLoading('monitoringResults', '获取存储监控信息...');
                
                const response = await fetch(`${API_BASE}/storage/monitor`);
                const result = await response.json();
                
                if (result.data) {
                    const data = result.data;
                    
                    showResult('monitoringResults', 
                        `<div class="success-text">✅ 存储监控获取成功</div>
                         <div>磁盘使用率: ${data.disk_usage?.usage_percent?.toFixed(1) || '--'}%</div>
                         <div>可用空间: ${data.disk_usage?.free_gb?.toFixed(2) || '--'}GB</div>
                         <div>监控状态: ${data.monitoring_enabled ? '启用' : '禁用'}</div>
                         <pre>${formatJSON(result.data)}</pre>`
                    );
                } else {
                    showResult('monitoringResults', '⚠️ 存储监控数据为空', 'warning-text');
                }
                
            } catch (error) {
                showResult('monitoringResults', `❌ 存储监控测试失败: ${error.message}`, 'error-text');
            }
        }

        // 测试压缩统计
        async function testCompressionStats() {
            try {
                showLoading('compressionResults', '获取压缩统计...');
                
                const response = await fetch(`${API_BASE}/compression/stats`);
                const result = await response.json();
                
                if (result.data) {
                    const data = result.data;
                    
                    // 更新统计
                    if (data.algorithm_stats && Object.keys(data.algorithm_stats).length > 0) {
                        const firstAlgo = Object.values(data.algorithm_stats)[0];
                        document.getElementById('compressionRatio').textContent = 
                            firstAlgo.avg_compression_ratio?.toFixed(3) || '--';
                        document.getElementById('compressionTime').textContent = 
                            `${(firstAlgo.avg_total_time * 1000)?.toFixed(1) || '--'}ms`;
                        document.getElementById('optimalAlgorithm').textContent = 
                            Object.keys(data.algorithm_stats)[0] || '--';
                    }
                    
                    showResult('compressionResults', 
                        `<div class="success-text">✅ 压缩统计获取成功</div>
                         <div>测试总数: ${data.total_tests}</div>
                         <div>配置数量: ${Object.keys(data.current_profiles || {}).length}</div>
                         <pre>${formatJSON(result.data)}</pre>`
                    );
                } else {
                    showResult('compressionResults', '⚠️ 压缩统计数据为空', 'warning-text');
                }
                
            } catch (error) {
                showResult('compressionResults', `❌ 压缩统计测试失败: ${error.message}`, 'error-text');
            }
        }

        // 优化压缩算法
        async function optimizeCompression() {
            try {
                showLoading('compressionResults', '正在优化压缩算法...');
                
                const response = await fetch(`${API_BASE}/compression/optimize`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                showResult('compressionResults', 
                    `<div class="success-text">✅ ${result.message}</div>
                     <div>优化时间: ${result.timestamp}</div>
                     <div>建议刷新压缩统计查看优化结果</div>`
                );
                
                // 自动刷新统计
                setTimeout(testCompressionStats, 2000);
                
            } catch (error) {
                showResult('compressionResults', `❌ 压缩优化失败: ${error.message}`, 'error-text');
            }
        }

        // 运行全部测试
        async function runAllTests() {
            showResult('overallResults', '<div class="info-text">🚀 开始运行全部测试...</div>');
            updateProgress(0);
            
            const tests = [
                { name: '数据源状态', func: testDataSources },
                { name: '监控指标', func: testMonitoring },
                { name: '告警信息', func: testAlerts },
                { name: '压缩统计', func: testCompressionStats }
            ];
            
            let completed = 0;
            let results = [];
            
            for (const test of tests) {
                try {
                    showResult('overallResults', `<div class="info-text">正在执行: ${test.name}...</div>`);
                    await test.func();
                    results.push(`✅ ${test.name}: 成功`);
                } catch (error) {
                    results.push(`❌ ${test.name}: 失败 - ${error.message}`);
                }
                
                completed++;
                updateProgress((completed / tests.length) * 100);
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            const successCount = results.filter(r => r.includes('✅')).length;
            const totalCount = results.length;
            const successRate = (successCount / totalCount * 100).toFixed(1);
            
            let finalResult = `<div class="success-text">🎉 全部测试完成!</div>`;
            finalResult += `<div>成功率: ${successRate}% (${successCount}/${totalCount})</div><br>`;
            finalResult += results.join('<br>');
            
            showResult('overallResults', finalResult);
        }

        // 刷新所有状态
        async function refreshAllStats() {
            await testDataSources();
            await testMonitoring();
            await testCompressionStats();
        }

        // 清空所有结果
        function clearAllResults() {
            const resultBoxes = ['dataSourceResults', 'monitoringResults', 'compressionResults', 'overallResults'];
            resultBoxes.forEach(id => {
                showResult(id, '<div class="info-text">结果已清空，点击按钮开始测试...</div>');
            });
            
            // 重置统计
            ['totalSources', 'healthySources', 'primarySource', 'cpuUsage', 'memoryUsage', 'activeAlerts', 
             'compressionRatio', 'compressionTime', 'optimalAlgorithm'].forEach(id => {
                document.getElementById(id).textContent = '--';
            });
            
            updateProgress(0);
        }

        // 页面加载时自动刷新状态
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(refreshAllStats, 1000);
        });
    </script>
</body>
</html>
