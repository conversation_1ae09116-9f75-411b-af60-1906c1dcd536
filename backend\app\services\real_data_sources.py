"""
真实数据源集成模块
集成tushare、akshare等真实市场数据源，实现多数据源切换和故障转移
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod

import pandas as pd
from loguru import logger

try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False
    logger.warning("Tushare未安装，将跳过tushare数据源")

try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    logger.warning("AKShare未安装，将跳过akshare数据源")

from app.core.config import settings
from app.core.data_storage import api_rate_limiter


class DataSourceInterface(ABC):
    """数据源接口"""
    
    @abstractmethod
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时行情"""
        pass
    
    @abstractmethod
    async def get_kline_data(self, symbol: str, start_date: str, end_date: str, period: str = 'daily') -> Optional[pd.DataFrame]:
        """获取K线数据"""
        pass
    
    @abstractmethod
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        pass
    
    @abstractmethod
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        pass


class TushareDataSource(DataSourceInterface):
    """Tushare数据源"""
    
    def __init__(self):
        self.name = "tushare"
        self.pro = None
        self.initialized = False
        self.last_error = None
        
        if TUSHARE_AVAILABLE and settings.TUSHARE_API_TOKEN:
            try:
                ts.set_token(settings.TUSHARE_API_TOKEN)
                self.pro = ts.pro_api()
                self.initialized = True
                logger.info("Tushare数据源初始化成功")
            except Exception as e:
                logger.error(f"Tushare初始化失败: {e}")
                self.last_error = str(e)
    
    def is_available(self) -> bool:
        return TUSHARE_AVAILABLE and self.initialized and self.pro is not None
    
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时行情"""
        if not self.is_available():
            return None
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('tushare')
            
            # 转换股票代码格式 (000001.SZ -> 000001.SZ)
            ts_symbol = self._convert_symbol_to_tushare(symbol)
            
            # 获取实时行情
            df = self.pro.daily(ts_code=ts_symbol, trade_date=datetime.now().strftime('%Y%m%d'))
            
            if df.empty:
                # 如果当天没有数据，获取最近一个交易日的数据
                df = self.pro.daily(ts_code=ts_symbol, start_date=(datetime.now() - timedelta(days=7)).strftime('%Y%m%d'))
                if df.empty:
                    return None
                df = df.iloc[0:1]  # 取最新一条
            
            row = df.iloc[0]
            
            # 转换为标准格式
            quote_data = {
                'symbol': symbol,
                'code': symbol.split('.')[0],
                'name': self._get_stock_name(ts_symbol),
                'market': 'SZ' if symbol.endswith('.SZ') else 'SH',
                'sector': '未知',
                'price': float(row['close']),
                'current_price': float(row['close']),
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'previous_close': float(row['pre_close']),
                'change': float(row['close'] - row['pre_close']),
                'change_percent': float((row['close'] - row['pre_close']) / row['pre_close'] * 100),
                'volume': int(row['vol'] * 100),  # 转换为股
                'turnover': float(row['amount'] * 1000),  # 转换为元
                'timestamp': datetime.now().isoformat(),
                'data_source': 'tushare'
            }
            
            logger.debug(f"Tushare获取实时行情成功: {symbol}")
            return quote_data
            
        except Exception as e:
            logger.error(f"Tushare获取实时行情失败 {symbol}: {e}")
            self.last_error = str(e)
            return None
    
    async def get_kline_data(self, symbol: str, start_date: str, end_date: str, period: str = 'daily') -> Optional[pd.DataFrame]:
        """获取K线数据"""
        if not self.is_available():
            return None
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('tushare')
            
            ts_symbol = self._convert_symbol_to_tushare(symbol)
            
            # 获取日线数据
            df = self.pro.daily(
                ts_code=ts_symbol,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                return pd.DataFrame()
            
            # 转换为标准格式
            df['trade_date'] = pd.to_datetime(df['trade_date'])
            df = df.set_index('trade_date')
            df = df.sort_index()
            
            # 重命名列
            df = df.rename(columns={
                'open': 'open',
                'high': 'high', 
                'low': 'low',
                'close': 'close',
                'vol': 'volume',
                'amount': 'turnover'
            })
            
            # 选择需要的列
            df = df[['open', 'high', 'low', 'close', 'volume', 'turnover']]
            
            logger.debug(f"Tushare获取K线数据成功: {symbol} ({len(df)} 条)")
            return df
            
        except Exception as e:
            logger.error(f"Tushare获取K线数据失败 {symbol}: {e}")
            self.last_error = str(e)
            return pd.DataFrame()
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        if not self.is_available():
            return []
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('tushare')
            
            # 获取股票基本信息
            df = self.pro.stock_basic(exchange='', list_status='L')
            
            # 搜索匹配的股票
            mask = (df['ts_code'].str.contains(keyword.upper(), na=False) | 
                   df['name'].str.contains(keyword, na=False) |
                   df['symbol'].str.contains(keyword, na=False))
            
            results = df[mask].head(limit)
            
            stock_list = []
            for _, row in results.iterrows():
                stock_list.append({
                    'symbol': row['ts_code'],
                    'code': row['symbol'],
                    'name': row['name'],
                    'market': row['market'],
                    'industry': row['industry'],
                    'area': row['area'],
                    'data_source': 'tushare'
                })
            
            logger.debug(f"Tushare搜索股票成功: {keyword} ({len(stock_list)} 条)")
            return stock_list
            
        except Exception as e:
            logger.error(f"Tushare搜索股票失败 {keyword}: {e}")
            self.last_error = str(e)
            return []
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        if not self.is_available():
            return []
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('tushare')
            
            # 获取股票基本信息
            df = self.pro.stock_basic(exchange='', list_status='L')
            
            stock_list = []
            for _, row in df.iterrows():
                stock_list.append({
                    'symbol': row['ts_code'],
                    'code': row['symbol'],
                    'name': row['name'],
                    'market': row['market'],
                    'industry': row['industry'],
                    'area': row['area'],
                    'list_date': row['list_date'],
                    'data_source': 'tushare'
                })
            
            logger.info(f"Tushare获取股票列表成功: {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"Tushare获取股票列表失败: {e}")
            self.last_error = str(e)
            return []
    
    def _convert_symbol_to_tushare(self, symbol: str) -> str:
        """转换股票代码为tushare格式"""
        if '.' in symbol:
            return symbol  # 已经是tushare格式
        
        # 根据代码前缀判断市场
        if symbol.startswith('6'):
            return f"{symbol}.SH"
        elif symbol.startswith(('0', '3')):
            return f"{symbol}.SZ"
        else:
            return symbol
    
    def _get_stock_name(self, ts_symbol: str) -> str:
        """获取股票名称"""
        try:
            df = self.pro.stock_basic(ts_code=ts_symbol)
            if not df.empty:
                return df.iloc[0]['name']
        except:
            pass
        return ts_symbol


class AKShareDataSource(DataSourceInterface):
    """AKShare数据源"""
    
    def __init__(self):
        self.name = "akshare"
        self.initialized = AKSHARE_AVAILABLE
        self.last_error = None
        
        if self.initialized:
            logger.info("AKShare数据源初始化成功")
        else:
            logger.warning("AKShare不可用")
    
    def is_available(self) -> bool:
        return AKSHARE_AVAILABLE and self.initialized
    
    async def get_realtime_quote(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取实时行情"""
        if not self.is_available():
            return None
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('akshare')
            
            # 转换股票代码格式
            ak_symbol = self._convert_symbol_to_akshare(symbol)
            
            # 获取实时行情
            df = ak.stock_zh_a_spot_em()
            
            # 查找对应股票
            stock_data = df[df['代码'] == ak_symbol]
            
            if stock_data.empty:
                return None
            
            row = stock_data.iloc[0]
            
            # 转换为标准格式
            quote_data = {
                'symbol': symbol,
                'code': ak_symbol,
                'name': row['名称'],
                'market': 'SZ' if symbol.endswith('.SZ') else 'SH',
                'sector': '未知',
                'price': float(row['最新价']),
                'current_price': float(row['最新价']),
                'open': float(row['今开']),
                'high': float(row['最高']),
                'low': float(row['最低']),
                'previous_close': float(row['昨收']),
                'change': float(row['涨跌额']),
                'change_percent': float(row['涨跌幅']),
                'volume': int(row['成交量']),
                'turnover': float(row['成交额']),
                'timestamp': datetime.now().isoformat(),
                'data_source': 'akshare'
            }
            
            logger.debug(f"AKShare获取实时行情成功: {symbol}")
            return quote_data
            
        except Exception as e:
            logger.error(f"AKShare获取实时行情失败 {symbol}: {e}")
            self.last_error = str(e)
            return None
    
    async def get_kline_data(self, symbol: str, start_date: str, end_date: str, period: str = 'daily') -> Optional[pd.DataFrame]:
        """获取K线数据"""
        if not self.is_available():
            return None
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('akshare')
            
            ak_symbol = self._convert_symbol_to_akshare(symbol)
            
            # 获取历史数据
            df = ak.stock_zh_a_hist(
                symbol=ak_symbol,
                period="daily",
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', ''),
                adjust=""
            )
            
            if df.empty:
                return pd.DataFrame()
            
            # 转换为标准格式
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.set_index('日期')
            df = df.sort_index()
            
            # 重命名列
            df = df.rename(columns={
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'turnover'
            })
            
            # 选择需要的列
            df = df[['open', 'high', 'low', 'close', 'volume', 'turnover']]
            
            logger.debug(f"AKShare获取K线数据成功: {symbol} ({len(df)} 条)")
            return df
            
        except Exception as e:
            logger.error(f"AKShare获取K线数据失败 {symbol}: {e}")
            self.last_error = str(e)
            return pd.DataFrame()
    
    async def search_stocks(self, keyword: str, limit: int = 20) -> List[Dict[str, Any]]:
        """搜索股票"""
        if not self.is_available():
            return []
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('akshare')
            
            # 获取股票列表
            df = ak.stock_zh_a_spot_em()
            
            # 搜索匹配的股票
            mask = (df['代码'].str.contains(keyword, na=False) | 
                   df['名称'].str.contains(keyword, na=False))
            
            results = df[mask].head(limit)
            
            stock_list = []
            for _, row in results.iterrows():
                symbol = self._convert_akshare_to_symbol(row['代码'])
                stock_list.append({
                    'symbol': symbol,
                    'code': row['代码'],
                    'name': row['名称'],
                    'market': 'SZ' if symbol.endswith('.SZ') else 'SH',
                    'current_price': float(row['最新价']),
                    'change_percent': float(row['涨跌幅']),
                    'data_source': 'akshare'
                })
            
            logger.debug(f"AKShare搜索股票成功: {keyword} ({len(stock_list)} 条)")
            return stock_list
            
        except Exception as e:
            logger.error(f"AKShare搜索股票失败 {keyword}: {e}")
            self.last_error = str(e)
            return []
    
    async def get_stock_list(self) -> List[Dict[str, Any]]:
        """获取股票列表"""
        if not self.is_available():
            return []
        
        try:
            # 检查API调用限制
            await api_rate_limiter.wait_for_rate_limit('akshare')
            
            # 获取股票列表
            df = ak.stock_zh_a_spot_em()
            
            stock_list = []
            for _, row in df.iterrows():
                symbol = self._convert_akshare_to_symbol(row['代码'])
                stock_list.append({
                    'symbol': symbol,
                    'code': row['代码'],
                    'name': row['名称'],
                    'market': 'SZ' if symbol.endswith('.SZ') else 'SH',
                    'current_price': float(row['最新价']),
                    'change_percent': float(row['涨跌幅']),
                    'volume': int(row['成交量']),
                    'turnover': float(row['成交额']),
                    'data_source': 'akshare'
                })
            
            logger.info(f"AKShare获取股票列表成功: {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"AKShare获取股票列表失败: {e}")
            self.last_error = str(e)
            return []
    
    def _convert_symbol_to_akshare(self, symbol: str) -> str:
        """转换股票代码为akshare格式"""
        if '.' in symbol:
            return symbol.split('.')[0]  # 去掉后缀
        return symbol
    
    def _convert_akshare_to_symbol(self, code: str) -> str:
        """转换akshare代码为标准格式"""
        if code.startswith('6'):
            return f"{code}.SH"
        elif code.startswith(('0', '3')):
            return f"{code}.SZ"
        else:
            return code


# 导出主要组件
__all__ = [
    'DataSourceInterface',
    'TushareDataSource',
    'AKShareDataSource'
]
