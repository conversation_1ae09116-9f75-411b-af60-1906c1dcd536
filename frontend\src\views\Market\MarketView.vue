<template>
  <div class="market-view-optimized">
    <!-- Loading状态 -->
    <div v-if="loading.initial" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-result icon="error" :title="error">
        <template #extra>
          <el-button type="primary" @click="retryLoad">重试</el-button>
        </template>
      </el-result>
    </div>

    <!-- 主内容 -->
    <template v-else>
      <!-- 页面头部 -->
      <div class="market-header">
        <div class="header-content">
          <h1 class="page-title">行情分析</h1>
          <div class="header-actions">
            <el-input
              id="stock-search-input"
              v-model="searchQuery"
              placeholder="搜索股票代码或名称"
              style="width: 200px"
              clearable
              aria-label="搜索股票代码或名称"
              @input="handleSearchDebounced"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>

            <el-button type="primary" @click="refreshData" :loading="loading.refresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </div>

      <!-- 市场指数 -->
      <div class="market-indices" v-loading="loading.indices">
        <el-row :gutter="20">
          <el-col :xs="12" :sm="6" v-for="index in marketIndices" :key="index.symbol">
            <el-card class="index-card">
              <div class="index-content">
                <div class="index-name">{{ index.name }}</div>
                <div class="index-value">{{ formatNumber(index.price) }}</div>
                <div class="index-change" :class="getChangeClass(index.change)">
                  {{ formatChange(index.change) }} ({{ formatPercent(index.changePercent) }})
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 市场趋势图表 -->
      <div class="market-chart-section">
        <el-card v-loading="loading.chart" element-loading-text="正在加载图表...">
          <template #header>
            <div class="chart-header">
              <span>市场趋势</span>
              <div class="chart-status">
                <el-tag v-if="chartStatus === 'loading'" type="info" size="small">
                  <el-icon class="is-loading"><Loading /></el-icon>
                  初始化中
                </el-tag>
                <el-tag v-else-if="chartStatus === 'ready'" type="success" size="small">
                  <el-icon><Check /></el-icon>
                  已就绪
                </el-tag>
                <el-tag v-else-if="chartStatus === 'error'" type="danger" size="small">
                  <el-icon><Close /></el-icon>
                  加载失败
                </el-tag>
              </div>
              <el-button-group>
                <el-button size="small" :type="chartTimeRange === '1d' ? 'primary' : ''" @click="changeChartTimeRange('1d')">日线</el-button>
                <el-button size="small" :type="chartTimeRange === '1w' ? 'primary' : ''" @click="changeChartTimeRange('1w')">周线</el-button>
                <el-button size="small" :type="chartTimeRange === '1m' ? 'primary' : ''" @click="changeChartTimeRange('1m')">月线</el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container">
            <!-- 图表加载状态 -->
            <div v-if="chartStatus === 'loading'" class="chart-loading">
              <div class="loading-content">
                <el-icon class="loading-icon is-loading"><Loading /></el-icon>
                <div class="loading-text">正在初始化图表...</div>
                <div class="loading-progress">
                  <el-progress :percentage="chartLoadingProgress" :show-text="false" />
                </div>
              </div>
            </div>

            <!-- 图表错误状态 -->
            <div v-else-if="chartStatus === 'error'" class="chart-error">
              <div class="error-content">
                <el-icon class="error-icon"><Warning /></el-icon>
                <div class="error-text">图表加载失败</div>
                <el-button size="small" type="primary" @click="retryInitChart">重试</el-button>
              </div>
            </div>

            <!-- 图表容器 -->
            <div
              ref="marketChartRef"
              class="market-chart"
              style="height: 300px; width: 100%; visibility: visible; position: relative; z-index: 1;"
            ></div>
          </div>
        </el-card>
      </div>

      <!-- 筛选工具栏 -->
      <div class="filter-toolbar">
        <el-row :gutter="20" align="middle">
          <el-col :xs="24" :sm="10" :md="8">
            <el-button-group class="market-buttons">
              <el-button
                :type="selectedMarket === 'all' ? 'primary' : ''"
                @click="selectMarket('all')"
              >
                全部
              </el-button>
              <el-button
                :type="selectedMarket === 'sh' ? 'primary' : ''"
                @click="selectMarket('sh')"
              >
                沪A
              </el-button>
              <el-button
                :type="selectedMarket === 'sz' ? 'primary' : ''"
                @click="selectMarket('sz')"
              >
                深A
              </el-button>
              <el-button
                :type="selectedMarket === 'cyb' ? 'primary' : ''"
                @click="selectMarket('cyb')"
              >
                创业板
              </el-button>
              <el-button
                :type="selectedMarket === 'kcb' ? 'primary' : ''"
                @click="selectMarket('kcb')"
              >
                科创板
              </el-button>
            </el-button-group>
          </el-col>

          <el-col :xs="24" :sm="6" :md="4">
            <el-select
              id="industry-select"
              v-model="selectedIndustry"
              placeholder="选择行业"
              clearable
              aria-label="选择行业筛选"
              @change="handleIndustryChange"
            >
              <el-option
                v-for="industry in industries"
                :key="industry.code"
                :label="industry.name"
                :value="industry.code"
              />
            </el-select>
          </el-col>

          <el-col :xs="24" :sm="6" :md="4">
            <el-select
              id="sort-select"
              v-model="sortBy"
              placeholder="排序方式"
              aria-label="选择排序方式"
              @change="handleSort"
            >
              <el-option label="涨跌幅" value="changePercent" />
              <el-option label="成交量" value="volume" />
              <el-option label="成交额" value="amount" />
              <el-option label="股价" value="currentPrice" />
            </el-select>
          </el-col>

          <el-col :xs="24" :sm="6" :md="4">
            <el-button-group>
              <el-button
                :type="viewMode === 'list' ? 'primary' : 'default'"
                @click="viewMode = 'list'"
              >
                <el-icon><List /></el-icon>
              </el-button>
              <el-button
                :type="viewMode === 'grid' ? 'primary' : 'default'"
                @click="viewMode = 'grid'"
              >
                <el-icon><Grid /></el-icon>
              </el-button>
            </el-button-group>
          </el-col>
        </el-row>
      </div>

      <!-- 主要内容区域 -->
      <div class="market-content">
        <el-row :gutter="20">
          <!-- 股票列表 -->
          <el-col :xs="24" :lg="18">
            <el-card v-loading="loading.stocks">
              <template #header>
                <div class="card-header">
                  <span>股票列表</span>
                  <span class="stock-count">共 {{ totalStocks }} 只股票</span>
                </div>
              </template>

              <!-- 空数据状态 -->
              <el-empty v-if="displayStocks.length === 0" description="暂无匹配的股票数据">
                <el-button @click="clearFilters">清除筛选条件</el-button>
              </el-empty>

              <!-- 列表视图 -->
              <template v-else>
                <div v-if="viewMode === 'list'">
                  <el-table
                    :data="displayStocks"
                    style="width: 100%"
                    @row-click="handleStockClick"
                    :row-class-name="getRowClassName"
                    height="600"
                  >
                    <el-table-column prop="symbol" label="代码" width="80" fixed="left" />
                    <el-table-column prop="name" label="名称" width="120" fixed="left">
                      <template #default="{ row }">
                        <span v-html="highlightText(row.name, searchQuery)"></span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="currentPrice" label="现价" width="80" align="right">
                      <template #default="{ row }">
                        <span :class="getChangeClass(row.change)">
                          {{ formatPrice(row.currentPrice) }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="change" label="涨跌额" width="80" align="right">
                      <template #default="{ row }">
                        <span :class="getChangeClass(row.change)">
                          {{ formatChange(row.change) }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="changePercent" label="涨跌幅" width="90" align="right">
                      <template #default="{ row }">
                        <el-tag :type="getTagType(row.changePercent)" size="small">
                          {{ formatPercent(row.changePercent) }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="volume" label="成交量" width="100" align="right">
                      <template #default="{ row }">
                        {{ formatVolume(row.volume) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="成交额" width="100" align="right">
                      <template #default="{ row }">
                        {{ formatAmount(row.amount) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="200" fixed="right">
                      <template #default="{ row }">
                        <el-button size="small" type="info" @click.stop="viewStockDetail(row)">
                          详情
                        </el-button>
                        <el-button
                          size="small"
                          :type="isInWatchlist(row.symbol) ? 'warning' : 'default'"
                          @click.stop="toggleWatchlist(row)"
                          :loading="watchlistLoading[row.symbol]"
                        >
                          {{ isInWatchlist(row.symbol) ? '已关注' : '自选' }}
                        </el-button>
                        <el-button size="small" type="primary" @click.stop="quickTrade(row)">
                          交易
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                  <!-- 分页 -->
                  <div class="pagination-container">
                    <el-pagination
                      v-model:current-page="currentPage"
                      v-model:page-size="pageSize"
                      :page-sizes="[20, 50, 100, 200]"
                      :total="totalStocks"
                      layout="total, sizes, prev, pager, next, jumper"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                    />
                  </div>
                </div>

                <!-- 网格视图 -->
                <div v-else class="grid-view">
                  <el-row :gutter="16">
                    <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="stock in displayStocks" :key="stock.symbol">
                      <StockCard
                        :stock="stock"
                        @click="handleStockClick(stock)"
                        @add-watchlist="toggleWatchlist(stock)"
                        @quick-trade="quickTrade(stock)"
                      />
                    </el-col>
                  </el-row>
                </div>
              </template>
            </el-card>
          </el-col>

          <!-- 侧边栏 -->
          <el-col :xs="24" :lg="6">
            <div class="sidebar-container">
              <!-- 排行榜 -->
              <el-card class="sidebar-panel" v-loading="loading.ranking">
                <template #header>
                  <div class="panel-header">
                    <el-tabs v-model="activeRankingTab" size="small">
                      <el-tab-pane label="涨幅榜" name="gainers" />
                      <el-tab-pane label="跌幅榜" name="losers" />
                      <el-tab-pane label="成交额" name="volume" />
                    </el-tabs>
                  </div>
                </template>

                <div class="ranking-list">
                  <div
                    class="ranking-item"
                    v-for="(stock, index) in getCurrentRanking"
                    :key="stock.symbol"
                    @click="handleStockClick(stock)"
                  >
                    <div class="rank-number">{{ index + 1 }}</div>
                    <div class="rank-info">
                      <div class="stock-name">{{ stock.name }}</div>
                      <div class="stock-code">{{ stock.symbol }}</div>
                    </div>
                    <div class="rank-value" :class="getChangeClass(stock.changePercent)">
                      {{ formatPercent(stock.changePercent) }}
                    </div>
                  </div>
                </div>
              </el-card>

              <!-- 板块行情 -->
              <el-card class="sidebar-panel" v-loading="loading.sectors">
                <template #header>
                  <span>板块行情</span>
                </template>

                <div class="sector-list">
                  <div
                    class="sector-item"
                    v-for="sector in topSectors"
                    :key="sector.code"
                    @click="selectSector(sector)"
                  >
                    <span class="sector-name">{{ sector.name }}</span>
                    <span class="sector-change" :class="getChangeClass(sector.changePercent)">
                      {{ formatPercent(sector.changePercent) }}
                    </span>
                  </div>
                </div>
              </el-card>

              <!-- 市场资讯 -->
              <el-card class="sidebar-panel" v-loading="loading.news">
                <template #header>
                  <span>市场资讯</span>
                </template>

                <div class="news-list">
                  <div
                    class="news-item"
                    v-for="news in marketNews.slice(0, 5)"
                    :key="news.id"
                    @click="viewNewsDetail(news)"
                  >
                    <el-tag v-if="news.importance === 'high'" type="danger" size="small">重要</el-tag>
                    <div class="news-title">{{ news.title }}</div>
                    <div class="news-time">{{ formatTime(news.publishTime) }}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Search, Refresh, List, Grid, Star, TrendCharts, Loading, Check, Close, Warning } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { useMarketStore } from '@/stores/modules/market'
import { safeInitChart, enhancedInitChart, setupChartResize, createLineChartOption } from '@/utils/chartUtils'
import { useTradingStore } from '@/stores/modules/trading'
import { formatVolume, formatAmount, formatPrice, formatTime } from '@/utils/formatters'
import { debounce } from 'lodash-es'
import StockCard from '@/components/market/StockCard.vue'
// import '@/debug-market' // 导入调试工具 - 暂时禁用

// Store
const marketStore = useMarketStore()
const tradingStore = useTradingStore()
const router = useRouter()

// 响应式数据
const searchQuery = ref('')
const selectedMarket = ref('all')
const selectedIndustry = ref('')
const sortBy = ref('changePercent')
const sortOrder = ref<'asc' | 'desc'>('desc')
const viewMode = ref<'list' | 'grid'>('list')
const activeRankingTab = ref('gainers')
const currentPage = ref(1)
const pageSize = ref(50)
const error = ref('')

// Loading states
const loading = ref({
  initial: true,
  refresh: false,
  stocks: false,
  indices: false,
  ranking: false,
  sectors: false,
  news: false,
  chart: false
})

// 图表状态
const chartStatus = ref<'loading' | 'ready' | 'error'>('loading')
const chartLoadingProgress = ref(0)

// Watchlist loading states
const watchlistLoading = ref<Record<string, boolean>>({})

// 图表相关状态
const marketChartRef = ref<HTMLDivElement>()
const marketChart = ref<echarts.ECharts>()
const chartTimeRange = ref('1d')

// 计算属性
const marketIndices = computed(() => {
  console.log('🔍 计算市场指数显示数据, 原始数据:', marketStore.indices)

  return Object.entries(marketStore.indices).map(([symbol, data]) => ({
    symbol,
    name: data.name || getIndexName(symbol), // 优先使用API返回的名称
    price: data.currentPrice,
    change: data.change,
    changePercent: data.changePercent,
    volume: data.volume,
    amount: data.amount
  }))
})
const industries = computed(() => {
  const result = marketStore.industries || []
  console.log('Industries computed:', result)
  return result
})
const marketNews = computed(() => marketStore.news || [])
const topSectors = computed(() => marketStore.sectors?.slice(0, 10) || [])

// 筛选和排序后的股票
const filteredAndSortedStocks = computed(() => {
  const stockList = Array.isArray(marketStore.stockList) ? marketStore.stockList : []
  let stocks = [...stockList]
  console.log('🔍 Stock list from store:', stocks.length, 'stocks')
  if (stocks.length > 0) {
    console.log('📊 Sample stocks:', stocks.slice(0, 3))
  } else {
    console.warn('⚠️ No stocks found in store')
    // 返回空数组，避免在computed中调用异步方法
    return []
  }

  // 搜索筛选
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    stocks = stocks.filter(stock =>
      stock.symbol.includes(query) ||
      stock.name.toLowerCase().includes(query) ||
      stock.pinyin?.toLowerCase().includes(query)
    )
  }

  // 市场筛选
  if (selectedMarket.value !== 'all') {
    stocks = stocks.filter(stock => getMarketType(stock.symbol) === selectedMarket.value)
  }

  // 行业筛选
  if (selectedIndustry.value) {
    stocks = stocks.filter(stock => stock.industry === selectedIndustry.value)
  }

  // 排序
  const sorted = [...stocks].sort((a, b) => {
    const field = sortBy.value as keyof typeof a
    const aValue = a[field] || 0
    const bValue = b[field] || 0
    const multiplier = sortOrder.value === 'desc' ? -1 : 1
    return (Number(aValue) - Number(bValue)) * multiplier
  })

  return sorted
})

// 分页后的股票
const totalStocks = computed(() => filteredAndSortedStocks.value.length)
const displayStocks = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredAndSortedStocks.value.slice(start, end)
})

// 排行榜数据
const getCurrentRanking = computed(() => {
  switch (activeRankingTab.value) {
    case 'gainers':
      return marketStore.topGainers
    case 'losers':
      return marketStore.topLosers
    case 'volume':
      return marketStore.topTurnover
    default:
      return []
  }
})

// 图表相关方法
const initMarketChart = async () => {
  try {
    console.log('🎨 开始初始化市场图表...')

    // 设置加载状态
    chartStatus.value = 'loading'
    loading.value.chart = true
    chartLoadingProgress.value = 0

    // 等待DOM完全渲染
    await nextTick()
    chartLoadingProgress.value = 20

    // 多重检查容器是否存在和可见
    const checkContainer = () => {
      if (!marketChartRef.value) {
        console.warn('⚠️ 市场图表容器引用未找到')
        return false
      }

      const container = marketChartRef.value
      const rect = container.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(container)

      console.log('📊 容器状态检查:', {
        exists: !!container,
        width: rect.width,
        height: rect.height,
        display: computedStyle.display,
        visibility: computedStyle.visibility,
        offsetWidth: container.offsetWidth,
        offsetHeight: container.offsetHeight
      })

      // 检查容器是否真正可见和有尺寸
      if (rect.width === 0 || rect.height === 0) {
        console.warn('⚠️ 容器尺寸为0')
        return false
      }

      if (computedStyle.display === 'none' || computedStyle.visibility === 'hidden') {
        console.warn('⚠️ 容器被隐藏')
        return false
      }

      return true
    }

    // 等待容器准备好，最多重试10次
    let retryCount = 0
    const maxRetries = 10

    const waitForContainer = async (): Promise<boolean> => {
      while (retryCount < maxRetries) {
        chartLoadingProgress.value = 20 + (retryCount / maxRetries) * 30 // 20-50%

        if (checkContainer()) {
          console.log('✅ 容器检查通过')
          chartLoadingProgress.value = 50
          return true
        }

        retryCount++
        console.warn(`🔄 容器未准备好，重试 ${retryCount}/${maxRetries}`)
        await new Promise(resolve => setTimeout(resolve, 500))
      }

      console.error('❌ 容器检查失败，超过最大重试次数')
      return false
    }

    if (!(await waitForContainer())) {
      console.error('❌ 容器准备失败，尝试fallback方案')
      chartStatus.value = 'error'
      loading.value.chart = false
      setTimeout(() => initMarketChartFallback(), 1000)
      return
    }

    chartLoadingProgress.value = 60

    // 使用增强的图表初始化
    const chart = await enhancedInitChart(marketChartRef, {
      maxRetries: 5,
      retryDelay: 500,
      waitForDOM: false, // 我们已经等待过了
      forceVisible: true,
      onRetry: (retryCount) => {
        console.warn(`🔄 图表初始化重试 (${retryCount}/5)`)
        chartLoadingProgress.value = 60 + (retryCount / 5) * 20 // 60-80%
      },
      onError: (error) => {
        console.error('❌ 图表初始化失败:', error)
        chartStatus.value = 'error'
        loading.value.chart = false
      },
      onSuccess: (chartInstance) => {
        marketChart.value = chartInstance
        console.log('✅ 市场图表实例创建成功')
        chartLoadingProgress.value = 90
        setupChartContent(chartInstance)
      }
    })

    if (!chart) {
      console.error('❌ 图表初始化返回null，尝试fallback方案')
      chartStatus.value = 'error'
      loading.value.chart = false
      setTimeout(() => initMarketChartFallback(), 1000)
    }

  } catch (error) {
    console.error('❌ 市场图表初始化异常:', error)
    chartStatus.value = 'error'
    loading.value.chart = false
    setTimeout(() => initMarketChartFallback(), 1000)
  }
}

// 设置图表内容的独立函数
const setupChartContent = (chartInstance: any) => {
  try {
    // 设置默认图表配置
    const defaultData = [
      { name: '09:30', value: 3200 },
      { name: '10:00', value: 3210 },
      { name: '10:30', value: 3205 },
      { name: '11:00', value: 3220 },
      { name: '11:30', value: 3215 },
      { name: '14:00', value: 3225 },
      { name: '14:30', value: 3230 },
      { name: '15:00', value: 3235 }
    ]

    const option = createLineChartOption(defaultData, '市场趋势')
    chartInstance.setOption(option)
    console.log('📊 市场图表配置已设置')

    // 更新进度
    chartLoadingProgress.value = 95

    // 设置响应式处理
    const cleanupResize = setupChartResize(chartInstance)

    // 组件卸载时清理
    onUnmounted(() => {
      cleanupResize()
      if (chartInstance && !chartInstance.isDisposed()) {
        chartInstance.dispose()
      }
    })

    // 完成加载
    setTimeout(() => {
      chartLoadingProgress.value = 100
      chartStatus.value = 'ready'
      loading.value.chart = false
      console.log('🎉 市场图表初始化完成')
    }, 200)

  } catch (error) {
    console.error('❌ 图表内容设置失败:', error)
    chartStatus.value = 'error'
    loading.value.chart = false
  }
}

// Fallback图表初始化方案
const initMarketChartFallback = async () => {
  try {
    console.log('🔧 使用fallback方案初始化图表...')

    // 再次等待DOM
    await nextTick()
    await new Promise(resolve => setTimeout(resolve, 200))

    if (!marketChartRef.value) {
      console.error('⚠️ Fallback: 图表容器仍未找到，放弃初始化')
      return
    }

    const container = marketChartRef.value

    // 强制设置容器样式和属性
    container.style.width = '100%'
    container.style.height = '300px'
    container.style.display = 'block'
    container.style.visibility = 'visible'
    container.style.position = 'relative'
    container.style.minHeight = '300px'

    // 确保父容器也是可见的
    let parent = container.parentElement
    while (parent) {
      if (parent.style.display === 'none') {
        console.warn('⚠️ 发现隐藏的父容器，强制显示')
        parent.style.display = 'block'
      }
      parent = parent.parentElement
    }

    // 等待样式应用
    await new Promise(resolve => setTimeout(resolve, 100))

    // 检查容器最终状态
    const rect = container.getBoundingClientRect()
    console.log('📊 Fallback容器最终状态:', {
      width: rect.width,
      height: rect.height,
      offsetWidth: container.offsetWidth,
      offsetHeight: container.offsetHeight
    })

    if (rect.width === 0 || rect.height === 0) {
      console.error('❌ Fallback: 容器尺寸仍为0，无法初始化图表')
      return
    }

    // 销毁可能存在的图表实例
    const existingChart = echarts.getInstanceByDom(container)
    if (existingChart) {
      existingChart.dispose()
    }

    // 直接使用echarts.init
    const chart = echarts.init(container, undefined, {
      renderer: 'canvas',
      useDirtyRect: false // fallback模式使用更稳定的渲染
    })

    if (!chart) {
      console.error('❌ Fallback: echarts.init返回null')
      return
    }

    marketChart.value = chart

    // 设置简单但完整的配置
    const option = {
      title: {
        text: '市场趋势',
        left: 'center',
        textStyle: { fontSize: 16, color: '#333' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c}'
      },
      xAxis: {
        type: 'category',
        data: ['09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00'],
        axisLabel: { color: '#666' }
      },
      yAxis: {
        type: 'value',
        axisLabel: { color: '#666' }
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '20%'
      },
      series: [{
        data: [3200, 3210, 3205, 3220, 3215, 3225, 3230, 3235],
        type: 'line',
        smooth: true,
        lineStyle: { color: '#409EFF', width: 2 },
        itemStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }]
    }

    // 设置图表配置
    chart.setOption(option, true) // 强制重新渲染

    // 手动触发resize确保图表正确显示
    setTimeout(() => {
      if (chart && !chart.isDisposed()) {
        chart.resize()
        console.log('📊 Fallback图表resize完成')
      }
    }, 100)

    // 设置响应式处理
    const resizeHandler = () => {
      if (chart && !chart.isDisposed()) {
        chart.resize()
      }
    }
    window.addEventListener('resize', resizeHandler)

    // 组件卸载时清理
    onUnmounted(() => {
      window.removeEventListener('resize', resizeHandler)
      if (chart && !chart.isDisposed()) {
        chart.dispose()
      }
    })

    console.log('✅ Fallback图表初始化成功')

    // 设置状态为ready
    chartStatus.value = 'ready'
    loading.value.chart = false
    chartLoadingProgress.value = 100

  } catch (error) {
    console.error('❌ Fallback图表初始化也失败:', error)
    // 最后的fallback：显示错误信息，但仍然设置为ready状态
    if (marketChartRef.value) {
      marketChartRef.value.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 300px; color: #999; font-size: 14px;">
          <div style="text-align: center;">
            <div>📊</div>
            <div style="margin-top: 8px;">图表加载失败</div>
            <div style="margin-top: 4px; font-size: 12px;">请刷新页面重试</div>
          </div>
        </div>
      `
    }
    // 即使失败也设置为ready，确保容器可见
    chartStatus.value = 'ready'
    loading.value.chart = false
  }
}

const changeChartTimeRange = (range: string) => {
  chartTimeRange.value = range
  // 这里可以根据时间范围更新图表数据
  console.log('切换图表时间范围:', range)
}

// 重试初始化图表
const retryInitChart = () => {
  console.log('🔄 用户手动重试图表初始化')
  chartStatus.value = 'loading'
  chartLoadingProgress.value = 0
  initMarketChart()
}

// 方法
const getIndexName = (symbol: string) => {
  const INDEX_MAP: Record<string, string> = {
    '000001.SH': '上证指数',
    'SH000001': '上证指数',
    '399001.SZ': '深证成指',
    'SZ399001': '深证成指',
    '399006.SZ': '创业板指',
    'SZ399006': '创业板指',
    '000688.SH': '科创50',
    'SH000688': '科创50'
  }
  return INDEX_MAP[symbol] || symbol
}

const getChangeClass = (value: number) => {
  if (value > 0) return 'text-up'
  if (value < 0) return 'text-down'
  return 'text-muted'
}

const getTagType = (changePercent: number) => {
  if (changePercent > 0) return 'danger'
  if (changePercent < 0) return 'success'
  return 'info'
}

const getRowClassName = ({ row }: { row: any }) => {
  if (row.status === 'suspended') return 'suspended-row'
  if (row.status === 'delisted') return 'delisted-row'
  return ''
}

const getMarketType = (symbol: string) => {
  if (symbol.startsWith('60')) return 'sh'
  if (symbol.startsWith('00')) return 'sz'
  if (symbol.startsWith('30')) return 'cyb'
  if (symbol.startsWith('68')) return 'kcb'
  return 'other'
}

const formatChange = (value: number) => {
  return (value >= 0 ? '+' : '') + value.toFixed(2)
}

const formatPercent = (value: number) => {
  return (value >= 0 ? '+' : '') + value.toFixed(2) + '%'
}

const formatNumber = (value: number) => {
  return value.toFixed(2)
}

const highlightText = (text: string, query: string) => {
  if (!query) return text
  const regex = new RegExp(`(${query})`, 'gi')
  return text.replace(regex, '<mark>$1</mark>')
}

// 数据加载
const loadData = async () => {
  try {
    console.log('loadData called')
    loading.value.initial = true
    error.value = ''

    // 调用initialize，它会处理所有数据加载
    await marketStore.initialize()
    console.log('Market store initialized, indices:', marketStore.indices)

    // 确保股票列表已加载
    if (!marketStore.stockList || marketStore.stockList.length === 0) {
      console.log('Stock list empty, fetching...')
      await marketStore.fetchStockList({ pageSize: 100 })
    }

    console.log('Final stock list length:', marketStore.stockList?.length || 0)
  } catch (e) {
    error.value = '数据加载失败，请稍后重试'
    console.error('Market data loading error:', e)
  } finally {
    loading.value.initial = false
  }
}

const retryLoad = () => {
  loadData()
}

const refreshData = async () => {
  try {
    loading.value.refresh = true
    await Promise.all([
      marketStore.fetchMarketOverview(),
      marketStore.fetchStockList({ pageSize: 100 }),
      marketStore.fetchRankings(),
      marketStore.fetchSectors(),
      marketStore.fetchNews()
    ])
    ElMessage.success('数据已刷新')
  } catch (e) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value.refresh = false
  }
}

// 搜索处理（防抖）
const handleSearchDebounced = debounce(() => {
  currentPage.value = 1
}, 300)

const selectMarket = (market: string) => {
  selectedMarket.value = market
  handleMarketChange()
}

const handleMarketChange = () => {
  currentPage.value = 1
}

const handleIndustryChange = () => {
  currentPage.value = 1
}

const handleSort = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
}

const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedMarket.value = 'all'
  selectedIndustry.value = ''
  currentPage.value = 1
}

// 股票操作
const handleStockClick = (stock: any) => {
  router.push(`/market/${stock.symbol}`)
}

const toggleWatchlist = async (stock: any) => {
  try {
    watchlistLoading.value[stock.symbol] = true

    if (isInWatchlist(stock.symbol)) {
      await marketStore.removeFromWatchlist(stock.symbol)
      ElMessage.success('已移出自选股')
    } else {
      await marketStore.addToWatchlist(stock.symbol, stock.name)
      ElMessage.success('已添加到自选股')
    }
  } catch (error) {
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    watchlistLoading.value[stock.symbol] = false
  }
}

const isInWatchlist = (symbol: string) => {
  return marketStore.watchlist?.some((w: any) => w.symbol === symbol) || false
}

const quickTrade = (stock: any) => {
  router.push({
    path: '/trading',
    query: { symbol: stock.symbol }
  })
}

const viewStockDetail = (stock: any) => {
  router.push(`/market/${stock.symbol}`)
}

const selectSector = (sector: any) => {
  // 可以跳转到板块详情或筛选该板块股票
  selectedIndustry.value = sector.code
}

const viewNewsDetail = (news: any) => {
  // 打开新闻详情
  if (news.url) {
    window.open(news.url, '_blank')
  }
}


// 定时刷新
let refreshTimer: number
const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    if (!loading.value.refresh) {
      marketStore.fetchStockList({ pageSize: 100 })
    }
  }, 30000) // 30秒刷新一次
}

// 生命周期
onMounted(() => {
  console.log('MarketViewOptimized mounted, loading data...')
  loadData()
  startAutoRefresh()

  // 优化的图表初始化策略
  const initChartWithDelay = async () => {
    // 等待数据加载完成
    await nextTick()

    // 等待页面布局稳定
    await new Promise(resolve => setTimeout(resolve, 800))

    // 检查页面是否可见
    if (document.hidden) {
      console.log('📊 页面不可见，延迟图表初始化')
      document.addEventListener('visibilitychange', () => {
        if (!document.hidden) {
          setTimeout(() => initMarketChart(), 300)
        }
      }, { once: true })
      return
    }

    console.log('🎨 开始初始化市场图表...')
    initMarketChart()
  }

  initChartWithDelay()
})

onUnmounted(() => {
  clearInterval(refreshTimer)
})

// 监听路由变化
watch(() => router.currentRoute.value, () => {
  // 可以根据路由参数更新筛选条件
})
</script>

<style lang="scss" scoped>
.market-view-optimized {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: auto !important;
  background: #f5f7fa;

  .loading-container,
  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 50vh;
    padding: 40px;
  }

  .market-header {
    background: #fff;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .page-title {
        font-size: 20px;
        font-weight: 600;
        margin: 0;
        color: #303133;
      }

      .header-actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .market-indices {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;

    .index-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .index-content {
        .index-name {
          font-size: 14px;
          color: #909399;
          margin-bottom: 8px;
        }

        .index-value {
          font-size: 24px;
          font-weight: 600;
          color: #303133;
          margin-bottom: 4px;
        }

        .index-change {
          font-size: 14px;
        }
      }
    }
  }

  // 图表相关样式
  .market-chart-section {
    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .chart-status {
        margin: 0 16px;
      }
    }

    .chart-container {
      position: relative;
      height: 300px;
    }

    .chart-loading {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      z-index: 10;

      .loading-content {
        text-align: center;

        .loading-icon {
          font-size: 32px;
          color: #409EFF;
          margin-bottom: 16px;
        }

        .loading-text {
          font-size: 14px;
          color: #666;
          margin-bottom: 16px;
        }

        .loading-progress {
          width: 200px;
          margin: 0 auto;
        }
      }
    }

    .chart-error {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.95);
      z-index: 10;

      .error-content {
        text-align: center;

        .error-icon {
          font-size: 32px;
          color: #F56C6C;
          margin-bottom: 16px;
        }

        .error-text {
          font-size: 14px;
          color: #666;
          margin-bottom: 16px;
        }
      }
    }

    .market-chart {
      width: 100%;
      height: 300px;
    }
  }

  .filter-toolbar {
    padding: 16px 20px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;

    .market-buttons {
      display: flex;
      width: 100%;

      .el-button {
        flex: 1;
        font-size: 14px;
      }
    }

    :deep(.el-select) {
      width: 100%;
    }
  }

  .market-content {
    flex: 1;
    padding: 20px;
    overflow-x: hidden;
    overflow-y: auto;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .stock-count {
        font-size: 14px;
        color: #909399;
      }
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }

    .grid-view {
      .el-col {
        margin-bottom: 16px;
      }
    }

    .sidebar-container {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-height: 100%;
      overflow-y: auto;
    }

    .sidebar-panel {
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .ranking-list {
        .ranking-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }

          .rank-number {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;

            &:nth-child(1) { color: #ff3030; }
            &:nth-child(2) { color: #ff6600; }
            &:nth-child(3) { color: #ff9900; }
          }

          .rank-info {
            flex: 1;
            overflow: hidden;

            .stock-name {
              font-size: 14px;
              color: #303133;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .stock-code {
              font-size: 12px;
              color: #909399;
            }
          }

          .rank-value {
            font-size: 14px;
            font-weight: 600;
          }
        }
      }

      .sector-list {
        .sector-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 10px 0;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }

          .sector-name {
            font-size: 14px;
            color: #303133;
          }

          .sector-change {
            font-size: 14px;
            font-weight: 600;
          }
        }
      }

      .news-list {
        .news-item {
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: #f5f5f5;
          }

          &:last-child {
            border-bottom: none;
          }

          .news-title {
            font-size: 14px;
            color: #303133;
            margin: 4px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }

          .news-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }

  // 表格样式
  :deep(.el-table) {
    .suspended-row {
      background: #f5f5f5;
      color: #909399;
    }

    .delisted-row {
      background: #fef0f0;
      color: #f56c6c;
    }

    mark {
      background: #ffe58f;
      padding: 0 2px;
    }
  }

  // 中国股市颜色
  .text-up {
    color: #f56c6c;  // 红涨
  }

  .text-down {
    color: #67c23a;  // 绿跌
  }

  .text-muted {
    color: #909399;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .sidebar-container {
      max-height: 400px;
    }
  }

  @media (max-width: 768px) {
    .filter-toolbar {
      .el-col {
        margin-bottom: 12px;
      }
    }

    .market-content {
      padding: 10px;
    }

    .sidebar-container {
      margin-top: 20px;
    }
  }
}
</style>
