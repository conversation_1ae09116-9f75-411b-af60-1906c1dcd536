# 策略服务
from datetime import date, datetime
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, desc, func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.models.strategy import Strategy
from app.db.models.user import User
from app.db.models.strategy import StrategyStatus, StrategyType, StrategyTemplate as TemplateModel, StrategySignal as SignalModel, StrategyPerformance as PerformanceModel
from app.schemas.strategy import (
    FrequencyType,
    StrategyCreate,
    StrategyLog,
    StrategyOptimizationRequest,
    StrategyPerformance,
    StrategySignal,
    StrategyTemplate,
    StrategyUpdate,
    SignalType,
)
from app.utils.exceptions import DataNotFoundError


class ValidationResult:
    """代码验证结果"""

    def __init__(self, is_valid: bool, error_message: str = ""):
        self.is_valid = is_valid
        self.error_message = error_message


class StrategyService:
    """策略服务类"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_strategy(
        self, user_id: int, strategy_data: StrategyCreate
    ) -> Strategy:
        """创建策略"""
        from app.utils.helpers import generate_uuid

        strategy = Strategy(
            id=generate_uuid(),
            user_id=user_id,
            name=strategy_data.name,
            description=strategy_data.description,
            strategy_type=strategy_data.strategy_type,
            code=strategy_data.code,
            parameters=strategy_data.parameters,
            status=StrategyStatus.DRAFT,
            is_active=True,
            created_at=datetime.now(),
        )

        self.db.add(strategy)
        await self.db.commit()
        await self.db.refresh(strategy)

        return strategy

    async def get_strategy_by_id(self, strategy_id: UUID) -> Optional[Strategy]:
        """根据ID获取策略"""
        result = await self.db.execute(
            select(Strategy).where(Strategy.id == strategy_id)
        )
        return result.scalar_one_or_none()

    async def get_strategy_by_name(self, user_id: int, name: str) -> Optional[Strategy]:
        """根据名称获取用户策略"""
        result = await self.db.execute(
            select(Strategy).where(
                and_(Strategy.user_id == user_id, Strategy.name == name)
            )
        )
        return result.scalar_one_or_none()

    async def get_user_strategies(
        self,
        user_id: int,
        strategy_type: Optional[StrategyType] = None,
        status: Optional[StrategyStatus] = None,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Strategy], int]:
        """获取用户策略列表"""
        # 构建查询条件
        conditions = [Strategy.user_id == user_id]

        if strategy_type:
            conditions.append(Strategy.strategy_type == strategy_type)
        if status:
            conditions.append(Strategy.status == status)

        # 查询策略列表
        query = select(Strategy).where(and_(*conditions)).offset(skip).limit(limit)
        result = await self.db.execute(query)
        strategies = result.scalars().all()

        # 查询总数
        count_query = select(func.count(Strategy.id)).where(and_(*conditions))
        count_result = await self.db.execute(count_query)
        total = count_result.scalar()

        return list(strategies), total

    async def update_strategy(
        self, strategy_id: UUID, strategy_update: StrategyUpdate
    ) -> Strategy:
        """更新策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        # 更新字段
        update_data = strategy_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(strategy, field, value)

        strategy.updated_at = datetime.now()

        await self.db.commit()
        await self.db.refresh(strategy)

        return strategy

    async def delete_strategy(self, strategy_id: UUID) -> bool:
        """删除策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        await self.db.delete(strategy)
        await self.db.commit()

        return True

    async def validate_strategy_code(self, code: str) -> ValidationResult:
        """验证策略代码"""
        import ast
        import sys
        from io import StringIO
        
        if not code or not code.strip():
            return ValidationResult(False, "策略代码不能为空")
        
        try:
            # 基本语法检查
            ast.parse(code)
            
            # 检查必要的函数（聚宽策略通常需要initialize函数）
            tree = ast.parse(code)
            
            has_initialize = False
            function_names = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    function_names.append(node.name)
                    if node.name == 'initialize':
                        has_initialize = True
            
            # 检查导入的模块是否可用
            forbidden_imports = ['os', 'subprocess', 'sys', 'eval', 'exec']
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        if alias.name in forbidden_imports:
                            return ValidationResult(
                                False, 
                                f"禁止导入模块: {alias.name}"
                            )
                elif isinstance(node, ast.ImportFrom):
                    if node.module in forbidden_imports:
                        return ValidationResult(
                            False, 
                            f"禁止导入模块: {node.module}"
                        )
            
            # 基本的安全检查
            code_lower = code.lower()
            dangerous_keywords = ['__import__', 'eval(', 'exec(', 'compile(', 'open(']
            for keyword in dangerous_keywords:
                if keyword in code_lower:
                    return ValidationResult(
                        False, 
                        f"代码包含潜在危险操作: {keyword}"
                    )
            
            return ValidationResult(
                True, 
                f"代码验证通过，找到 {len(function_names)} 个函数: {', '.join(function_names[:5])}"
            )
            
        except SyntaxError as e:
            return ValidationResult(
                False, 
                f"语法错误 (第{e.lineno}行): {e.msg}"
            )
        except Exception as e:
            return ValidationResult(False, f"代码验证失败: {str(e)}")
    
    async def get_strategy_templates(self, strategy_type: Optional[StrategyType] = None) -> List[StrategyTemplate]:
        """获取策略模板"""
        import uuid
        from datetime import datetime
        
        # 检查数据库中的模板
        conditions = [TemplateModel.is_active == True]
        if strategy_type:
            conditions.append(TemplateModel.strategy_type == strategy_type)
        
        query = select(TemplateModel).where(and_(*conditions))
        result = await self.db.execute(query)
        db_templates = result.scalars().all()
        
        # 如果数据库中有模板，返回数据库中的模板
        if db_templates:
            templates = []
            for db_template in db_templates:
                template = StrategyTemplate(
                    id=db_template.id,
                    name=db_template.name,
                    description=db_template.description or "",
                    strategy_type=db_template.strategy_type,
                    template_code=db_template.template_code,
                    default_parameters=db_template.default_parameters or {},
                    parameter_schema=[],  # TODO: 从模板代码中解析参数schema
                    tags=[db_template.category] if db_template.category else [],
                    is_public=True,
                    created_by=1,  # TODO: 获取实际创建者
                    created_at=db_template.created_at
                )
                templates.append(template)
            return templates
        
        # 如果数据库中没有模板，返回内置模板
        templates = [
            StrategyTemplate(
                id=uuid.uuid4(),
                name="均线交叉策略",
                description="基于短期和长期移动平均线交叉的策略模板",
                strategy_type=StrategyType.TREND_FOLLOWING,
                template_code="""
# 均线交叉策略模板
def initialize(context):
    # 设置基准和股票
    g.benchmark = '000300.XSHG'  # 沪深300作为基准
    g.stock = '000001.XSHE'      # 平安银行
    
    # 设置参数
    g.short_period = 5   # 短期均线
    g.long_period = 20   # 长期均线
    
    # 每日运行
    run_daily(trade, time='09:30')

def trade(context):
    stock = g.stock
    
    # 获取历史数据
    hist = attribute_history(stock, g.long_period, '1d', ['close'])
    
    # 计算均线
    short_ma = hist['close'][-g.short_period:].mean()
    long_ma = hist['close'].mean()
    
    current_price = get_current_data()[stock].last_price
    
    # 交易逻辑
    if short_ma > long_ma and stock not in context.portfolio.positions:
        # 金叉买入
        order_target_percent(stock, 0.8, LimitOrderStyle(current_price))
        log.info(f"买入 {stock}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}")
        
    elif short_ma < long_ma and stock in context.portfolio.positions:
        # 死叉卖出  
        order_target_percent(stock, 0, LimitOrderStyle(current_price))
        log.info(f"卖出 {stock}, 短期均线: {short_ma:.2f}, 长期均线: {long_ma:.2f}")
""",
                default_parameters={
                    "short_period": 5,
                    "long_period": 20,
                    "position_ratio": 0.8
                },
                parameter_schema=[],
                tags=["趋势跟踪", "均线"],
                is_public=True,
                created_by=1,
                created_at=datetime.utcnow()
            ),
            StrategyTemplate(
                id=uuid.uuid4(),
                name="动量策略",
                description="基于股票价格动量的策略模板",
                strategy_type=StrategyType.MOMENTUM,
                template_code="""
# 动量策略模板
def initialize(context):
    g.stocks = ['000001.XSHE', '000002.XSHE', '600000.XSHG']
    g.lookback_period = 20  # 回看周期
    g.top_n = 2  # 选择前N只股票
    
    # 每月调仓
    run_monthly(rebalance, monthday=1)

def rebalance(context):
    # 计算动量分数
    momentum_scores = {}
    
    for stock in g.stocks:
        hist = attribute_history(stock, g.lookback_period + 1, '1d', ['close'])
        if len(hist) > g.lookback_period:
            momentum = (hist['close'][-1] / hist['close'][0]) - 1
            momentum_scores[stock] = momentum
    
    # 选择动量最强的股票
    sorted_stocks = sorted(momentum_scores.items(), key=lambda x: x[1], reverse=True)
    selected_stocks = [stock for stock, score in sorted_stocks[:g.top_n]]
    
    # 清仓不在选择列表中的股票
    for stock in context.portfolio.positions:
        if stock not in selected_stocks:
            order_target_percent(stock, 0)
    
    # 等权重买入选中的股票
    target_weight = 1.0 / len(selected_stocks) if selected_stocks else 0
    for stock in selected_stocks:
        order_target_percent(stock, target_weight)
        
    log.info(f"调仓完成，选中股票: {selected_stocks}")
""",
                default_parameters={
                    "lookback_period": 20,
                    "top_n": 2,
                    "rebalance_frequency": "monthly"
                },
                parameter_schema=[],
                tags=["动量", "多股票"],
                is_public=True,
                created_by=1,
                created_at=datetime.utcnow()
            )
        ]
        
        if strategy_type:
            templates = [t for t in templates if t.strategy_type == strategy_type]
        
        return templates

    async def start_strategy(self, strategy_id: UUID) -> bool:
        """启动策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        strategy.status = StrategyStatus.RUNNING
        strategy.last_run_at = datetime.now()
        strategy.updated_at = datetime.now()

        await self.db.commit()

        # TODO: 启动策略执行引擎

        return True

    async def stop_strategy(self, strategy_id: UUID) -> bool:
        """停止策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        strategy.status = StrategyStatus.STOPPED
        strategy.updated_at = datetime.now()

        await self.db.commit()

        # TODO: 停止策略执行引擎

        return True

    async def pause_strategy(self, strategy_id: UUID) -> bool:
        """暂停策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        strategy.status = StrategyStatus.PAUSED
        strategy.updated_at = datetime.now()

        await self.db.commit()

        return True

    async def resume_strategy(self, strategy_id: UUID) -> bool:
        """恢复策略"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")

        strategy.status = StrategyStatus.RUNNING
        strategy.updated_at = datetime.now()

        await self.db.commit()

        return True


    async def get_strategy_performance(
        self,
        strategy_id: UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """获取策略绩效"""
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")
        
        # 从数据库中读取策略性能数据
        return {
            "total_return": float(strategy.total_return or 0.0),
            "annual_return": float(strategy.annual_return or 0.0),
            "max_drawdown": float(strategy.max_drawdown or 0.0),
            "sharpe_ratio": float(strategy.sharpe_ratio or 0.0),
            "win_rate": float(strategy.win_rate or 0.0),
            "total_trades": strategy.total_trades or 0,
            "profitable_trades": strategy.profitable_trades or 0,
            "avg_trade_return": float(strategy.avg_trade_return or 0.0),
        }

    async def get_strategy_signals(
        self,
        strategy_id: UUID,
        signal_type: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[StrategySignal]:
        """获取策略信号"""
        from app.db.models.strategy import StrategyInstance, StrategySignal as DBSignal
        
        # 查找策略实例
        instances_query = select(StrategyInstance.id).where(StrategyInstance.strategy_id == strategy_id)
        instance_result = await self.db.execute(instances_query)
        instance_ids = [row[0] for row in instance_result.all()]
        
        if not instance_ids:
            return []
        
        # 构建查询条件
        conditions = [DBSignal.instance_id.in_(instance_ids)]
        
        if signal_type:
            conditions.append(DBSignal.signal_type == signal_type)
        if start_time:
            conditions.append(DBSignal.signal_time >= start_time)
        if end_time:
            conditions.append(DBSignal.signal_time <= end_time)
        
        # 查询信号
        query = select(DBSignal).where(and_(*conditions)).order_by(desc(DBSignal.signal_time)).limit(limit)
        result = await self.db.execute(query)
        db_signals = result.scalars().all()
        
        # 转换为Pydantic模型
        signals = []
        for db_signal in db_signals:
            signal = StrategySignal(
                strategy_id=strategy_id,
                symbol=db_signal.symbol_code,
                signal_type=db_signal.signal_type,
                strength=float(db_signal.confidence or 1.0),
                price=float(db_signal.price) if db_signal.price else None,
                volume=float(db_signal.quantity) if db_signal.quantity else None,
                timestamp=db_signal.signal_time,
                metadata=db_signal.indicators or {}
            )
            signals.append(signal)
        
        return signals

    async def get_strategy_logs(
        self,
        strategy_id: UUID,
        level: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[StrategyLog]:
        """获取策略日志"""
        # 目前返回模拟日志数据，实际实现需要配合日志存储系统
        logs = []
        
        # 示例日志数据
        if not start_time:
            start_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        sample_logs = [
            {
                "level": "INFO", 
                "message": "策略启动成功",
                "timestamp": start_time
            },
            {
                "level": "INFO", 
                "message": "开始处理市场数据",
                "timestamp": start_time.replace(hour=9, minute=30)
            },
            {
                "level": "DEBUG", 
                "message": "计算技术指标完成",
                "timestamp": start_time.replace(hour=10, minute=0)
            }
        ]
        
        for log_data in sample_logs:
            if not level or log_data["level"] == level.upper():
                if not end_time or log_data["timestamp"] <= end_time:
                    log = StrategyLog(
                        strategy_id=strategy_id,
                        level=log_data["level"],
                        message=log_data["message"],
                        timestamp=log_data["timestamp"]
                    )
                    logs.append(log)
        
        return logs[:limit]

    async def start_optimization(
        self, strategy_id: UUID, optimization_request: StrategyOptimizationRequest
    ) -> str:
        """启动策略参数优化"""
        import uuid
        
        # 验证策略存在
        strategy = await self.get_strategy_by_id(strategy_id)
        if not strategy:
            raise DataNotFoundError("策略不存在")
        
        # 验证优化参数
        if not optimization_request.parameters:
            raise ValueError("优化参数不能为空")
        
        # 创建优化任务ID  
        task_id = str(uuid.uuid4())
        
        # TODO: 实际的参数优化实现
        # 1. 创建优化任务记录
        # 2. 启动后台优化进程
        # 3. 返回任务ID用于跟踪进度
        
        return task_id


    async def get_user_strategy_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户策略统计"""
        # 查询各状态策略数量
        result = await self.db.execute(
            select(Strategy.status, func.count(Strategy.id))
            .where(Strategy.user_id == user_id)
            .group_by(Strategy.status)
        )
        status_counts = dict(result.all())

        # 查询策略类型分布
        result = await self.db.execute(
            select(Strategy.strategy_type, func.count(Strategy.id))
            .where(Strategy.user_id == user_id)
            .group_by(Strategy.strategy_type)
        )
        type_distribution = dict(result.all())

        return {
            "total_strategies": sum(status_counts.values()),
            "running_strategies": status_counts.get(StrategyStatus.RUNNING, 0),
            "paused_strategies": status_counts.get(StrategyStatus.PAUSED, 0),
            "stopped_strategies": status_counts.get(StrategyStatus.STOPPED, 0),
            "error_strategies": status_counts.get(StrategyStatus.ERROR, 0),
            "draft_strategies": status_counts.get(StrategyStatus.DRAFT, 0),
            "strategy_type_distribution": type_distribution,
            "performance_summary": {},
            "top_performers": [],
        }
