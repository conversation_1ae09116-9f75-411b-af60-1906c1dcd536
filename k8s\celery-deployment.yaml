apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-worker
  namespace: quant-platform
spec:
  replicas: 2
  selector:
    matchLabels:
      app: celery-worker
  template:
    metadata:
      labels:
        app: celery-worker
    spec:
      containers:
      - name: celery-worker
        image: quant-platform/backend:latest
        imagePullPolicy: Always
        command: ["celery", "-A", "app.tasks.celery_app", "worker", "--loglevel=info"]
        env:
        - name: DATABASE_URL
          value: "postgresql://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_PASSWORD
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_PORT
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_NAME
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: SECRET_KEY
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: celery-beat
  namespace: quant-platform
spec:
  replicas: 1  # Beat调度器只能有一个实例
  selector:
    matchLabels:
      app: celery-beat
  template:
    metadata:
      labels:
        app: celery-beat
    spec:
      containers:
      - name: celery-beat
        image: quant-platform/backend:latest
        imagePullPolicy: Always
        command: ["celery", "-A", "app.tasks.celery_app", "beat", "--loglevel=info"]
        env:
        - name: DATABASE_URL
          value: "postgresql://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_PASSWORD
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_PORT
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_NAME
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: SECRET_KEY
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"