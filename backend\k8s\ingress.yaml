apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: quant-platform-ingress
  namespace: quant-platform
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/websocket-services: "quant-platform-backend"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://quant-platform.com"
    nginx.ingress.kubernetes.io/rate-limit: "300"
spec:
  tls:
  - hosts:
    - api.quant-platform.com
    secretName: quant-platform-tls
  rules:
  - host: api.quant-platform.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: quant-platform-backend
            port:
              number: 8000