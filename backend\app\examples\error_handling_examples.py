"""
错误处理示例
展示如何在不同场景下正确使用错误处理系统
"""

import asyncio
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import BaseModel, validator

from app.core.service_base import ServiceBase, service_method, database_operation, external_api_call
from app.core.exceptions import (
    ValidationError,
    DatabaseError, 
    ExternalServiceError,
    BusinessLogicError,
    ResourceNotFoundError,
    AuthenticationError,
    RateLimitError,
    MarketDataError,
    TradingError
)
from app.core.logging_config import get_contextual_logger

router = APIRouter(prefix="/examples", tags=["错误处理示例"])
logger = get_contextual_logger("error_examples")


# 1. 数据模型验证示例
class UserCreateRequest(BaseModel):
    username: str
    email: str
    password: str
    age: Optional[int] = None
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValidationError(
                "用户名长度不能少于3个字符",
                details={"field": "username", "min_length": 3, "provided_length": len(v)}
            )
        return v
    
    @validator('email')
    def validate_email(cls, v):
        if '@' not in v:
            raise ValidationError(
                "邮箱格式不正确",
                details={"field": "email", "provided_value": v}
            )
        return v


# 2. 服务类示例
class UserService(ServiceBase):
    """用户服务示例"""
    
    def __init__(self):
        super().__init__("user_service")
        self.users_db = {}  # 模拟数据库
        self.user_counter = 1
    
    async def _initialize_service(self):
        """初始化用户服务"""
        logger.info("Initializing user service")
        # 模拟初始化过程
        await asyncio.sleep(0.1)
    
    @database_operation(timeout=30.0, retry_count=2)
    async def create_user(self, user_data: UserCreateRequest) -> dict:
        """创建用户 - 展示数据库操作错误处理"""
        
        # 业务逻辑验证
        if user_data.username in self.users_db:
            raise BusinessLogicError(
                "用户名已存在",
                details={"username": user_data.username},
                recovery_hint="请选择不同的用户名"
            )
        
        # 模拟数据库操作可能失败
        if user_data.username == "database_error":
            raise DatabaseError(
                "数据库连接失败",
                details={"operation": "create_user", "table": "users"},
                should_retry=True,
                retry_after=30
            )
        
        # 成功创建用户
        user_id = self.user_counter
        self.user_counter += 1
        
        user = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "created_at": datetime.utcnow().isoformat()
        }
        
        self.users_db[user_data.username] = user
        logger.info("User created successfully", user_id=user_id, username=user_data.username)
        
        return user
    
    async def get_user(self, username: str) -> dict:
        """获取用户 - 展示资源不存在错误处理"""
        if username not in self.users_db:
            raise ResourceNotFoundError(
                f"用户 {username} 不存在",
                details={"username": username},
                recovery_hint="请检查用户名是否正确"
            )
        
        return self.users_db[username]
    
    @external_api_call(timeout=10.0, retry_count=3)
    async def verify_email(self, email: str) -> bool:
        """验证邮箱 - 展示外部服务调用错误处理"""
        
        # 模拟外部服务调用
        if email == "<EMAIL>":
            raise ExternalServiceError(
                "邮箱验证服务不可用",
                details={"service": "email_verification", "email": email},
                should_retry=True,
                retry_after=60
            )
        
        # 模拟验证结果
        return "@" in email and "." in email


# 3. 交易服务示例
class TradingService(ServiceBase):
    """交易服务示例"""
    
    def __init__(self):
        super().__init__("trading_service")
        self.positions = {}
        self.orders = {}
        self.order_counter = 1000
    
    async def _initialize_service(self):
        """初始化交易服务"""
        logger.info("Initializing trading service")
    
    @service_method(timeout=5.0, retry_count=0)  # 交易操作不重试
    async def place_order(self, symbol: str, quantity: int, price: float, user_id: str) -> dict:
        """下单 - 展示交易错误处理"""
        
        # 市场状态检查
        if symbol == "CLOSED_MARKET":
            raise MarketDataError(
                "市场已关闭",
                details={"symbol": symbol, "action": "place_order"},
                recovery_hint="请在交易时间内操作"
            )
        
        # 风控检查
        if quantity > 10000:
            raise TradingError(
                "单笔订单数量超过限制",
                details={
                    "symbol": symbol,
                    "requested_quantity": quantity,
                    "max_quantity": 10000
                },
                recovery_hint="请减少订单数量"
            )
        
        # 资金检查
        required_amount = quantity * price
        if required_amount > 1000000:  # 模拟资金不足
            raise TradingError(
                "资金不足",
                details={
                    "required_amount": required_amount,
                    "available_amount": 500000  # 模拟可用资金
                },
                recovery_hint="请减少订单金额或充值"
            )
        
        # 创建订单
        order_id = self.order_counter
        self.order_counter += 1
        
        order = {
            "order_id": order_id,
            "symbol": symbol,
            "quantity": quantity,
            "price": price,
            "user_id": user_id,
            "status": "submitted",
            "created_at": datetime.utcnow().isoformat()
        }
        
        self.orders[order_id] = order
        logger.info("Order placed successfully", 
                   order_id=order_id, 
                   symbol=symbol, 
                   quantity=quantity)
        
        return order


# 全局服务实例
user_service = UserService()
trading_service = TradingService()


# 4. API端点示例
@router.post("/users", summary="创建用户")
async def create_user_endpoint(
    user_data: UserCreateRequest,
    request: Request
) -> dict:
    """创建用户端点 - 展示API层错误处理"""
    
    # 速率限制检查
    client_ip = request.client.host
    if client_ip == "127.0.0.1" and "rate_limit_test" in str(request.url):
        raise RateLimitError(
            "请求过于频繁",
            details={"client_ip": client_ip},
            retry_after=60
        )
    
    # 调用服务层
    return await user_service.create_user(user_data)


@router.get("/users/{username}", summary="获取用户")
async def get_user_endpoint(username: str) -> dict:
    """获取用户端点"""
    return await user_service.get_user(username)


@router.post("/users/{username}/verify-email", summary="验证邮箱")
async def verify_email_endpoint(username: str) -> dict:
    """验证用户邮箱"""
    user = await user_service.get_user(username)
    is_valid = await user_service.verify_email(user["email"])
    
    return {
        "username": username,
        "email": user["email"],
        "is_valid": is_valid,
        "verified_at": datetime.utcnow().isoformat()
    }


@router.post("/trading/orders", summary="下单")
async def place_order_endpoint(
    symbol: str,
    quantity: int,
    price: float,
    request: Request
) -> dict:
    """下单端点"""
    
    # 模拟用户认证
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise AuthenticationError(
            "缺少认证信息",
            details={"required_header": "Authorization"},
            recovery_hint="请提供有效的认证令牌"
        )
    
    user_id = "user123"  # 从认证令牌解析
    
    return await trading_service.place_order(symbol, quantity, price, user_id)


@router.get("/trading/orders/{order_id}", summary="查询订单")
async def get_order_endpoint(order_id: int) -> dict:
    """查询订单"""
    if order_id not in trading_service.orders:
        raise ResourceNotFoundError(
            "订单不存在",
            details={"order_id": order_id},
            recovery_hint="请检查订单ID是否正确"
        )
    
    return trading_service.orders[order_id]


# 5. 错误场景测试端点
@router.get("/test/validation-error", summary="测试验证错误")
async def test_validation_error():
    """测试验证错误"""
    raise ValidationError(
        "测试验证错误",
        details={"field": "test_field", "value": "invalid_value"},
        recovery_hint="这是一个测试错误"
    )


@router.get("/test/database-error", summary="测试数据库错误")
async def test_database_error():
    """测试数据库错误"""
    raise DatabaseError(
        "测试数据库错误",
        should_retry=True,
        retry_after=30
    )


@router.get("/test/external-service-error", summary="测试外部服务错误")
async def test_external_service_error():
    """测试外部服务错误"""
    raise ExternalServiceError(
        "测试外部服务错误",
        details={"service": "test_service"},
        should_retry=True,
        retry_after=60
    )


@router.get("/test/business-logic-error", summary="测试业务逻辑错误")
async def test_business_logic_error():
    """测试业务逻辑错误"""
    raise BusinessLogicError(
        "测试业务逻辑错误",
        details={"rule": "test_rule", "violation": "test_violation"}
    )


@router.get("/test/unexpected-error", summary="测试未预期错误")
async def test_unexpected_error():
    """测试未预期错误"""
    # 这会被包装为BusinessLogicError
    raise ValueError("这是一个未预期的错误")


# 6. 初始化服务
async def initialize_example_services():
    """初始化示例服务"""
    await user_service.initialize()
    await trading_service.initialize()


async def cleanup_example_services():
    """清理示例服务"""
    await user_service.cleanup()
    await trading_service.cleanup()


# 7. 错误处理最佳实践示例
class BestPracticesExamples:
    """错误处理最佳实践示例"""
    
    @staticmethod
    def validate_input_data():
        """输入数据验证示例"""
        # ✅ 好的做法
        def validate_user_age(age: int):
            if not isinstance(age, int):
                raise ValidationError(
                    "年龄必须为整数",
                    details={"provided_type": type(age).__name__}
                )
            
            if age < 0 or age > 150:
                raise ValidationError(
                    "年龄必须在0-150之间",
                    details={"provided_age": age, "valid_range": "0-150"}
                )
        
        # ❌ 不好的做法
        def bad_validate_age(age):
            if age < 0:
                raise Exception("Invalid age")  # 信息不够详细
    
    @staticmethod
    def handle_database_operations():
        """数据库操作错误处理示例"""
        # ✅ 好的做法
        async def good_database_operation():
            try:
                # 数据库操作
                pass
            except ConnectionError as e:
                raise DatabaseError(
                    "数据库连接失败",
                    details={"original_error": str(e)},
                    should_retry=True,
                    retry_after=30
                )
            except Exception as e:
                raise DatabaseError(
                    "数据库操作失败",
                    details={"operation": "user_query", "error": str(e)}
                )
        
        # ❌ 不好的做法
        async def bad_database_operation():
            try:
                # 数据库操作
                pass
            except Exception:
                pass  # 静默忽略错误
    
    @staticmethod
    def handle_external_services():
        """外部服务调用错误处理示例"""
        # ✅ 好的做法
        async def good_external_call():
            try:
                # 外部服务调用
                pass
            except TimeoutError as e:
                raise ExternalServiceError(
                    "外部服务调用超时",
                    details={"service": "payment_gateway", "timeout": 10},
                    should_retry=True,
                    retry_after=60
                )
            except Exception as e:
                raise ExternalServiceError(
                    "外部服务调用失败",
                    details={"service": "payment_gateway", "error": str(e)}
                )
        
        # ❌ 不好的做法
        async def bad_external_call():
            # 外部服务调用，没有错误处理
            pass


# 8. 错误响应格式示例
ERROR_RESPONSE_EXAMPLES = {
    "validation_error": {
        "error": {
            "type": "validation_error",
            "code": "VALIDATION_FAILED",
            "message": "输入数据格式不正确",
            "details": {
                "field": "email",
                "provided_value": "invalid-email",
                "validation_rule": "email_format"
            },
            "request_id": "req_12345",
            "timestamp": "2024-01-01T12:00:00Z",
            "recovery_hint": "请提供有效的邮箱地址"
        }
    },
    
    "database_error": {
        "error": {
            "type": "database_error",
            "code": "DATABASE_ERROR",
            "message": "数据库服务暂时不可用",
            "details": {},
            "request_id": "req_12346",
            "timestamp": "2024-01-01T12:00:00Z",
            "recovery_hint": "请稍后重试，如问题持续请联系技术支持",
            "should_retry": True,
            "retry_after": 30
        }
    },
    
    "business_logic_error": {
        "error": {
            "type": "business_logic_error",
            "code": "INSUFFICIENT_FUNDS",
            "message": "账户余额不足",
            "details": {
                "required_amount": 1000.00,
                "available_amount": 500.00,
                "currency": "CNY"
            },
            "request_id": "req_12347",
            "timestamp": "2024-01-01T12:00:00Z",
            "recovery_hint": "请充值或减少交易金额"
        }
    }
}