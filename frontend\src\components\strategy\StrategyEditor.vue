<template>
  <div class="strategy-editor">
    <div class="editor-header">
      <h3>策略编辑器</h3>
      <div class="header-actions">
        <el-button size="small" @click="loadPreset">加载模板</el-button>
        <el-button size="small" @click="saveStrategy" type="primary">保存策略</el-button>
        <el-button size="small" @click="testStrategy" type="success">回测验证</el-button>
      </div>
    </div>

    <div class="editor-content">
      <el-row :gutter="20">
        <!-- 策略配置面板 -->
        <el-col :span="8">
          <el-card class="config-panel">
            <template #header>
              <span>策略配置</span>
            </template>

            <el-form ref="strategyForm" :model="strategyConfig" label-width="80px" size="small">
              <el-form-item label="策略名称">
                <el-input
                  id="strategy-editor-name-input"
                  v-model="strategyConfig.name"
                  placeholder="请输入策略名称"
                  aria-label="策略名称输入框"
                />
              </el-form-item>

              <el-form-item label="策略类型">
                <el-select
                  id="strategy-type-select"
                  v-model="strategyConfig.type"
                  placeholder="选择策略类型"
                  aria-label="策略类型选择器"
                  @change="onStrategyTypeChange"
                >
                  <el-option
                    v-for="strategy in availableStrategies"
                    :key="strategy.value"
                    :label="strategy.label"
                    :value="strategy.value"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="描述">
                <el-input
                  id="strategy-editor-description-input"
                  v-model="strategyConfig.description"
                  type="textarea"
                  :rows="3"
                  placeholder="策略描述"
                  aria-label="策略描述输入框"
                />
              </el-form-item>
            </el-form>

            <!-- 动态参数配置 -->
            <div class="parameter-section" v-if="strategyConfig.type">
              <h4>参数配置</h4>
              <div class="parameter-grid">
                <div
                  v-for="param in currentParameterConfig"
                  :key="param.name"
                  class="parameter-item"
                >
                  <label>{{ param.label }}:</label>
                  <el-input-number
                    v-if="param.type === 'number'"
                    v-model="strategyConfig.parameters[param.name]"
                    :min="param.min"
                    :max="param.max"
                    :step="param.step"
                    :precision="param.precision"
                    size="small"
                  />
                  <el-select
                    v-else-if="param.type === 'select'"
                    v-model="strategyConfig.parameters[param.name]"
                    size="small"
                  >
                    <el-option
                      v-for="option in param.options"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                  <el-switch
                    v-else-if="param.type === 'boolean'"
                    v-model="strategyConfig.parameters[param.name]"
                  />
                  <small class="param-desc">{{ param.description }}</small>
                </div>
              </div>
            </div>

            <!-- 风险控制 -->
            <div class="risk-section">
              <h4>风险控制</h4>
              <el-form label-width="100px" size="small">
                <el-form-item label="最大仓位">
                  <el-input-number
                    v-model="strategyConfig.riskControl.maxPosition"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    :precision="2"
                  />
                </el-form-item>
                <el-form-item label="止损比例">
                  <el-input-number
                    v-model="strategyConfig.riskControl.stopLoss"
                    :min="0"
                    :max="0.5"
                    :step="0.01"
                    :precision="2"
                  />
                </el-form-item>
                <el-form-item label="止盈比例">
                  <el-input-number
                    v-model="strategyConfig.riskControl.takeProfit"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>

        <!-- 可视化编辑器 -->
        <el-col :span="16">
          <el-card class="visual-editor">
            <template #header>
              <span>可视化策略构建器</span>
              <el-button-group style="float: right; margin-top: -3px;">
                <el-button size="small" @click="addNode">添加节点</el-button>
                <el-button size="small" @click="clearAll">清空</el-button>
              </el-button-group>
            </template>

            <div class="strategy-canvas" ref="canvasContainer">
              <!-- 策略节点 -->
              <div
                v-for="node in strategyNodes"
                :key="node.id"
                class="strategy-node"
                :class="{ active: selectedNode === node.id }"
                :style="{ left: node.x + 'px', top: node.y + 'px' }"
                @click="selectNode(node.id)"
                @mousedown="startDrag(node.id, $event)"
              >
                <div class="node-header">
                  <span class="node-type">{{ node.type }}</span>
                  <el-button
                    size="mini"
                    type="danger"
                    circle
                    @click.stop="removeNode(node.id)"
                  >
                    ×
                  </el-button>
                </div>
                <div class="node-content">
                  <div v-if="node.type === 'indicator'" class="indicator-config">
                    <el-select v-model="node.config.indicator" size="mini">
                      <el-option label="SMA" value="sma" />
                      <el-option label="EMA" value="ema" />
                      <el-option label="RSI" value="rsi" />
                      <el-option label="MACD" value="macd" />
                      <el-option label="布林带" value="bollinger" />
                    </el-select>
                    <el-input-number
                      v-model="node.config.period"
                      :min="1"
                      :max="200"
                      size="mini"
                      placeholder="周期"
                    />
                  </div>
                  <div v-else-if="node.type === 'condition'" class="condition-config">
                    <el-select v-model="node.config.operator" size="mini">
                      <el-option label=">" value="gt" />
                      <el-option label="<" value="lt" />
                      <el-option label="=" value="eq" />
                      <el-option label="交叉向上" value="cross_up" />
                      <el-option label="交叉向下" value="cross_down" />
                    </el-select>
                  </div>
                  <div v-else-if="node.type === 'signal'" class="signal-config">
                    <el-select v-model="node.config.action" size="mini">
                      <el-option label="买入" value="buy" />
                      <el-option label="卖出" value="sell" />
                      <el-option label="持有" value="hold" />
                    </el-select>
                  </div>
                </div>

                <!-- 连接点 -->
                <div class="connection-points">
                  <div class="input-point" v-if="node.type !== 'indicator'"></div>
                  <div class="output-point"></div>
                </div>
              </div>

              <!-- 连接线 -->
              <svg class="connections-layer">
                <path
                  v-for="connection in connections"
                  :key="`${connection.from}-${connection.to}`"
                  :d="getConnectionPath(connection)"
                  stroke="#409EFF"
                  stroke-width="2"
                  fill="none"
                />
              </svg>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 代码预览 -->
      <el-row style="margin-top: 20px;">
        <el-col :span="24">
          <el-card>
            <template #header>
              <span>生成的策略代码</span>
              <el-button
                style="float: right; margin-top: -3px;"
                size="small"
                @click="copyCode"
              >
                复制代码
              </el-button>
            </template>
            <div class="code-preview">
              <pre><code>{{ generatedCode }}</code></pre>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 预设模板对话框 -->
    <el-dialog v-model="showPresetDialog" title="选择策略模板" width="600px">
      <div class="preset-templates">
        <div
          v-for="template in strategyTemplates"
          :key="template.id"
          class="template-card"
          @click="selectTemplate(template)"
        >
          <h4>{{ template.name }}</h4>
          <p>{{ template.description }}</p>
          <div class="template-tags">
            <el-tag v-for="tag in template.tags" :key="tag" size="small">{{ tag }}</el-tag>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 回测结果对话框 -->
    <el-dialog v-model="showBacktestDialog" title="回测结果" width="80%">
      <div class="backtest-results" v-if="backtestResults">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="result-stats">
              <h4>回测统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <span>总收益率:</span>
                  <span :class="getReturnClass(backtestResults.totalReturn)">
                    {{ formatPercent(backtestResults.totalReturn) }}
                  </span>
                </div>
                <div class="stat-item">
                  <span>夏普比率:</span>
                  <span>{{ backtestResults.sharpeRatio?.toFixed(2) }}</span>
                </div>
                <div class="stat-item">
                  <span>最大回撤:</span>
                  <span class="negative">{{ formatPercent(backtestResults.maxDrawdown) }}</span>
                </div>
                <div class="stat-item">
                  <span>胜率:</span>
                  <span>{{ formatPercent(backtestResults.winRate) }}</span>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="equity-chart-container">
              <EquityCurveChart
                v-if="backtestResults.equityCurve"
                :data="backtestResults.equityCurve"
                :statistics="backtestResults"
                title="策略回测结果"
              />
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import EquityCurveChart from '@/components/charts/EquityCurveChart.vue'
import { formatPercent } from '@/utils/format'

interface StrategyNode {
  id: string
  type: 'indicator' | 'condition' | 'signal'
  x: number
  y: number
  config: any
}

interface Connection {
  from: string
  to: string
}

interface StrategyConfig {
  name: string
  type: string
  description: string
  parameters: Record<string, any>
  riskControl: {
    maxPosition: number
    stopLoss: number
    takeProfit: number
  }
}

const strategyForm = ref()
const canvasContainer = ref<HTMLElement>()
const showPresetDialog = ref(false)
const showBacktestDialog = ref(false)
const selectedNode = ref<string | null>(null)
const isDragging = ref(false)
const dragStartPos = ref({ x: 0, y: 0 })

const strategyConfig = ref<StrategyConfig>({
  name: '',
  type: '',
  description: '',
  parameters: {},
  riskControl: {
    maxPosition: 1.0,
    stopLoss: 0.1,
    takeProfit: 0.2
  }
})

const strategyNodes = ref<StrategyNode[]>([])
const connections = ref<Connection[]>([])
const backtestResults = ref<any>(null)

// 可用策略类型
const availableStrategies = [
  { label: '双均线策略', value: 'double_ma' },
  { label: 'RSI策略', value: 'rsi' },
  { label: 'MACD策略', value: 'macd' },
  { label: '布林带策略', value: 'bollinger_bands' },
  { label: '多因子策略', value: 'multi_factor' },
  { label: '自定义策略', value: 'custom' }
]

// 参数配置定义
const parameterConfigs = {
  double_ma: [
    {
      name: 'short_period',
      label: '短期均线周期',
      type: 'number',
      min: 1,
      max: 50,
      step: 1,
      precision: 0,
      description: '短期移动平均线的计算周期'
    },
    {
      name: 'long_period',
      label: '长期均线周期',
      type: 'number',
      min: 10,
      max: 200,
      step: 1,
      precision: 0,
      description: '长期移动平均线的计算周期'
    }
  ],
  rsi: [
    {
      name: 'period',
      label: 'RSI周期',
      type: 'number',
      min: 2,
      max: 50,
      step: 1,
      precision: 0,
      description: 'RSI指标的计算周期'
    },
    {
      name: 'overbought',
      label: '超买阈值',
      type: 'number',
      min: 50,
      max: 90,
      step: 1,
      precision: 0,
      description: 'RSI超买阈值'
    },
    {
      name: 'oversold',
      label: '超卖阈值',
      type: 'number',
      min: 10,
      max: 50,
      step: 1,
      precision: 0,
      description: 'RSI超卖阈值'
    }
  ],
  macd: [
    {
      name: 'fast_period',
      label: '快线周期',
      type: 'number',
      min: 5,
      max: 25,
      step: 1,
      precision: 0,
      description: 'MACD快线EMA周期'
    },
    {
      name: 'slow_period',
      label: '慢线周期',
      type: 'number',
      min: 15,
      max: 50,
      step: 1,
      precision: 0,
      description: 'MACD慢线EMA周期'
    },
    {
      name: 'signal_period',
      label: '信号线周期',
      type: 'number',
      min: 5,
      max: 15,
      step: 1,
      precision: 0,
      description: 'MACD信号线EMA周期'
    }
  ],
  bollinger_bands: [
    {
      name: 'period',
      label: '均线周期',
      type: 'number',
      min: 10,
      max: 50,
      step: 1,
      precision: 0,
      description: '布林带中轨移动平均周期'
    },
    {
      name: 'std_dev',
      label: '标准差倍数',
      type: 'number',
      min: 1,
      max: 3,
      step: 0.1,
      precision: 1,
      description: '布林带上下轨标准差倍数'
    }
  ]
}

// 策略模板
const strategyTemplates = [
  {
    id: 'ma_cross',
    name: '均线突破策略',
    description: '基于短期和长期移动平均线交叉的经典策略',
    tags: ['趋势跟踪', '经典策略'],
    config: {
      type: 'double_ma',
      parameters: { short_period: 5, long_period: 20 }
    }
  },
  {
    id: 'rsi_reversal',
    name: 'RSI反转策略',
    description: '基于RSI指标超买超卖的反转策略',
    tags: ['反转策略', '震荡指标'],
    config: {
      type: 'rsi',
      parameters: { period: 14, overbought: 70, oversold: 30 }
    }
  },
  {
    id: 'macd_momentum',
    name: 'MACD动量策略',
    description: '基于MACD指标的动量追踪策略',
    tags: ['动量策略', '趋势跟踪'],
    config: {
      type: 'macd',
      parameters: { fast_period: 12, slow_period: 26, signal_period: 9 }
    }
  }
]

// 计算当前参数配置
const currentParameterConfig = computed(() => {
  return parameterConfigs[strategyConfig.value.type] || []
})

// 生成的策略代码
const generatedCode = computed(() => {
  if (!strategyConfig.value.type) return ''

  const params = Object.entries(strategyConfig.value.parameters)
    .map(([key, value]) => `${key}=${value}`)
    .join(', ')

  return `
# ${strategyConfig.value.name}
# ${strategyConfig.value.description}

from strategies.classic_strategies import StrategyFactory

# 创建策略实例
strategy = StrategyFactory.create_strategy(
    '${strategyConfig.value.type}',
    ${params}
)

# 风险控制参数
strategy.max_position = ${strategyConfig.value.riskControl.maxPosition}
strategy.stop_loss = ${strategyConfig.value.riskControl.stopLoss}
strategy.take_profit = ${strategyConfig.value.riskControl.takeProfit}

# 执行回测
result = strategy.backtest(data)
print(f"总收益率: {result.metrics['total_return']:.2%}")
print(f"夏普比率: {result.metrics['sharpe_ratio']:.2f}")
print(f"最大回撤: {result.metrics['max_drawdown']:.2%}")
  `.trim()
})

// 方法
const onStrategyTypeChange = (type: string) => {
  // 重置参数
  strategyConfig.value.parameters = {}

  // 设置默认参数
  const config = currentParameterConfig.value
  config.forEach(param => {
    strategyConfig.value.parameters[param.name] = param.min || 0
  })
}

const loadPreset = () => {
  showPresetDialog.value = true
}

const selectTemplate = (template: any) => {
  strategyConfig.value.name = template.name
  strategyConfig.value.type = template.config.type
  strategyConfig.value.description = template.description
  strategyConfig.value.parameters = { ...template.config.parameters }
  showPresetDialog.value = false
  ElMessage.success('模板加载成功')
}

const saveStrategy = async () => {
  if (!strategyConfig.value.name || !strategyConfig.value.type) {
    ElMessage.warning('请填写策略名称和选择策略类型')
    return
  }

  try {
    // 这里应该调用API保存策略
    // await api.saveStrategy(strategyConfig.value)
    ElMessage.success('策略保存成功')
  } catch (error) {
    ElMessage.error('策略保存失败')
  }
}

const testStrategy = async () => {
  if (!strategyConfig.value.type) {
    ElMessage.warning('请先选择策略类型')
    return
  }

  try {
    // 这里应该调用API执行回测
    // const result = await api.backtestStrategy(strategyConfig.value)

    // 模拟回测结果
    backtestResults.value = {
      totalReturn: 0.15,
      annualReturn: 0.12,
      sharpeRatio: 1.5,
      maxDrawdown: -0.08,
      volatility: 0.18,
      winRate: 0.65,
      equityCurve: generateMockEquityCurve()
    }

    showBacktestDialog.value = true
    ElMessage.success('回测完成')
  } catch (error) {
    ElMessage.error('回测失败')
  }
}

const generateMockEquityCurve = () => {
  const data = []
  let value = 100000
  const startDate = new Date('2023-01-01')

  for (let i = 0; i < 250; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)

    value *= (1 + (Math.random() - 0.45) * 0.02)
    data.push({
      date: date.toISOString().split('T')[0],
      value: value
    })
  }

  return data
}

// 可视化编辑器方法
const addNode = () => {
  const newNode: StrategyNode = {
    id: Date.now().toString(),
    type: 'indicator',
    x: Math.random() * 400 + 50,
    y: Math.random() * 200 + 50,
    config: {}
  }
  strategyNodes.value.push(newNode)
}

const selectNode = (id: string) => {
  selectedNode.value = id
}

const removeNode = (id: string) => {
  strategyNodes.value = strategyNodes.value.filter(node => node.id !== id)
  connections.value = connections.value.filter(
    conn => conn.from !== id && conn.to !== id
  )
}

const clearAll = () => {
  strategyNodes.value = []
  connections.value = []
  selectedNode.value = null
}

const startDrag = (nodeId: string, event: MouseEvent) => {
  selectedNode.value = nodeId
  isDragging.value = true

  const rect = canvasContainer.value?.getBoundingClientRect()
  if (rect) {
    dragStartPos.value = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    }
  }

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event: MouseEvent) => {
  if (!isDragging.value || !selectedNode.value) return

  const rect = canvasContainer.value?.getBoundingClientRect()
  if (rect) {
    const node = strategyNodes.value.find(n => n.id === selectedNode.value)
    if (node) {
      node.x = event.clientX - rect.left - 50
      node.y = event.clientY - rect.top - 25
    }
  }
}

const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

const getConnectionPath = (connection: Connection) => {
  const fromNode = strategyNodes.value.find(n => n.id === connection.from)
  const toNode = strategyNodes.value.find(n => n.id === connection.to)

  if (!fromNode || !toNode) return ''

  const startX = fromNode.x + 100
  const startY = fromNode.y + 25
  const endX = toNode.x
  const endY = toNode.y + 25

  return `M ${startX} ${startY} Q ${(startX + endX) / 2} ${startY} ${endX} ${endY}`
}

const copyCode = () => {
  navigator.clipboard.writeText(generatedCode.value)
  ElMessage.success('代码已复制到剪贴板')
}

const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}
</script>

<style scoped>
.strategy-editor {
  width: 100%;
  height: 100%;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
}

.editor-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.editor-content {
  padding: 20px;
}

.config-panel {
  height: 800px;
  overflow-y: auto;
}

.parameter-section,
.risk-section {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.parameter-section h4,
.risk-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.parameter-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.parameter-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.parameter-item label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.param-desc {
  font-size: 11px;
  color: #909399;
  line-height: 1.3;
}

.visual-editor {
  height: 600px;
}

.strategy-canvas {
  position: relative;
  width: 100%;
  height: 500px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.strategy-node {
  position: absolute;
  width: 120px;
  min-height: 80px;
  background: white;
  border: 2px solid #dcdfe6;
  border-radius: 8px;
  cursor: move;
  user-select: none;
}

.strategy-node.active {
  border-color: #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 6px 6px 0 0;
  font-size: 12px;
  font-weight: 600;
}

.node-content {
  padding: 8px;
}

.indicator-config,
.condition-config,
.signal-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.connection-points {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.input-point {
  position: absolute;
  left: -6px;
  width: 12px;
  height: 12px;
  background: #67C23A;
  border-radius: 50%;
  cursor: pointer;
}

.output-point {
  position: absolute;
  right: -6px;
  width: 12px;
  height: 12px;
  background: #E6A23C;
  border-radius: 50%;
  cursor: pointer;
}

.connections-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.code-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 300px;
  overflow-y: auto;
}

.preset-templates {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.template-card {
  padding: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-card:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.template-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
}

.template-card p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.template-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.backtest-results {
  min-height: 400px;
}

.result-stats h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
}

.stat-item span:first-child {
  font-size: 14px;
  color: #606266;
}

.stat-item span:last-child {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.positive {
  color: #67c23a !important;
}

.negative {
  color: #f56c6c !important;
}

.equity-chart-container {
  height: 300px;
}

@media (max-width: 1200px) {
  .editor-content .el-col {
    margin-bottom: 20px;
  }
}
</style>
