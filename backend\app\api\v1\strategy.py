"""
修复后的策略管理API
提供策略创建、编辑、运行、回测等功能
"""
from datetime import datetime
from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from app.core.database import get_db
from app.core.dependencies_fixed import get_current_active_user
from app.db.models.user import User
from app.schemas.strategy import (
    StrategyCreate, StrategyUpdate, StrategyResponse,
    StrategyListResponse, BacktestRequest
)
from app.schemas.backtest import BacktestResponse

router = APIRouter()


# 临时的策略存储（实际应该使用数据库）
strategies_db = {}


@router.post("/strategies", response_model=StrategyResponse)
async def create_strategy(
    strategy_data: StrategyCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新策略
    
    - **name**: 策略名称
    - **description**: 策略描述
    - **code**: 策略代码
    - **parameters**: 策略参数
    - **tags**: 标签列表
    """
    # 生成策略ID
    strategy_id = f"STG{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
    
    # 创建策略对象
    strategy = {
        "id": strategy_id,
        "user_id": current_user.id,
        "name": strategy_data.name,
        "description": strategy_data.description,
        "code": strategy_data.code,
        "parameters": strategy_data.parameters or {},
        "tags": strategy_data.tags or [],
        "status": "draft",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "version": 1,
        "is_public": False,
        "performance": {
            "total_return": 0,
            "sharpe_ratio": 0,
            "max_drawdown": 0,
            "win_rate": 0
        }
    }
    
    # 保存到临时数据库
    strategies_db[strategy_id] = strategy
    
    return StrategyResponse(**strategy)


@router.get("/strategies", response_model=StrategyListResponse)
async def list_strategies(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = None,
    tag: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略列表
    
    - **skip**: 跳过记录数
    - **limit**: 返回记录数
    - **status**: 策略状态筛选
    - **tag**: 标签筛选
    """
    # 筛选用户的策略
    user_strategies = [
        s for s in strategies_db.values() 
        if s["user_id"] == current_user.id
    ]
    
    # 应用筛选条件
    if status:
        user_strategies = [s for s in user_strategies if s["status"] == status]
    if tag:
        user_strategies = [s for s in user_strategies if tag in s["tags"]]
    
    # 排序（按创建时间倒序）
    user_strategies.sort(key=lambda x: x["created_at"], reverse=True)
    
    # 分页
    total = len(user_strategies)
    strategies = user_strategies[skip:skip + limit]
    
    return StrategyListResponse(
        strategies=[StrategyResponse(**s) for s in strategies],
        total=total,
        skip=skip,
        limit=limit
    )


@router.get("/strategies/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略详情
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id and not strategy.get("is_public"):
        raise HTTPException(status_code=403, detail="无权访问此策略")
    
    return StrategyResponse(**strategy)


@router.put("/strategies/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: str,
    strategy_update: StrategyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新策略
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此策略")
    
    # 更新字段
    update_data = strategy_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if field in strategy and value is not None:
            strategy[field] = value
    
    strategy["updated_at"] = datetime.utcnow()
    strategy["version"] += 1
    
    return StrategyResponse(**strategy)


@router.delete("/strategies/{strategy_id}")
async def delete_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除策略
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此策略")
    
    # 删除策略
    del strategies_db[strategy_id]
    
    return {"message": "策略删除成功"}


@router.post("/strategies/{strategy_id}/backtest", response_model=BacktestResponse)
async def run_backtest(
    strategy_id: str,
    backtest_params: BacktestRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    运行策略回测
    
    - **start_date**: 回测开始日期
    - **end_date**: 回测结束日期
    - **initial_capital**: 初始资金
    - **symbols**: 交易标的列表
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权回测此策略")
    
    # 模拟回测结果
    backtest_id = f"BT{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
    
    result = {
        "backtest_id": backtest_id,
        "strategy_id": strategy_id,
        "status": "completed",
        "start_date": backtest_params.start_date,
        "end_date": backtest_params.end_date,
        "initial_capital": backtest_params.initial_capital,
        "final_capital": backtest_params.initial_capital * 1.15,  # 模拟15%收益
        "total_return": 0.15,
        "annual_return": 0.12,
        "sharpe_ratio": 1.5,
        "max_drawdown": -0.08,
        "win_rate": 0.65,
        "total_trades": 48,
        "winning_trades": 31,
        "losing_trades": 17,
        "avg_win": 0.025,
        "avg_loss": -0.015,
        "profit_factor": 1.8,
        "created_at": datetime.utcnow()
    }
    
    return BacktestResponse(**result)


@router.post("/strategies/{strategy_id}/start")
async def start_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    启动策略
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    # 更新状态
    strategy["status"] = "running"
    strategy["started_at"] = datetime.utcnow()
    
    return {"message": "策略已启动", "status": "running"}


@router.post("/strategies/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    停止策略
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此策略")
    
    # 更新状态
    strategy["status"] = "stopped"
    strategy["stopped_at"] = datetime.utcnow()
    
    return {"message": "策略已停止", "status": "stopped"}


@router.get("/strategies/{strategy_id}/performance")
async def get_strategy_performance(
    strategy_id: str,
    period: str = Query("1M", pattern="^(1D|1W|1M|3M|6M|1Y|ALL)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取策略绩效
    
    - **period**: 时间周期（1D/1W/1M/3M/6M/1Y/ALL）
    """
    strategy = strategies_db.get(strategy_id)
    
    if not strategy:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    # 检查权限
    if strategy["user_id"] != current_user.id and not strategy.get("is_public"):
        raise HTTPException(status_code=403, detail="无权访问此策略")
    
    # 模拟绩效数据
    return {
        "strategy_id": strategy_id,
        "period": period,
        "performance": {
            "total_return": 0.12,
            "annual_return": 0.15,
            "sharpe_ratio": 1.8,
            "max_drawdown": -0.06,
            "win_rate": 0.68,
            "volatility": 0.08
        },
        "equity_curve": [
            {"date": "2024-01-01", "value": 100000},
            {"date": "2024-02-01", "value": 102000},
            {"date": "2024-03-01", "value": 105000},
            {"date": "2024-04-01", "value": 108000},
            {"date": "2024-05-01", "value": 110000},
            {"date": "2024-06-01", "value": 112000}
        ]
    }


@router.post("/strategies/import")
async def import_strategy(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """
    导入策略文件
    
    支持格式：.py, .txt, .json
    """
    # 检查文件类型
    allowed_extensions = [".py", ".txt", ".json"]
    file_ext = file.filename.lower().split(".")[-1] if "." in file.filename else ""
    
    if f".{file_ext}" not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件类型，仅支持: {', '.join(allowed_extensions)}"
        )
    
    # 读取文件内容
    content = await file.read()
    
    # 创建策略
    strategy_id = f"STG{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8].upper()}"
    
    strategy = {
        "id": strategy_id,
        "user_id": current_user.id,
        "name": file.filename.replace(f".{file_ext}", ""),
        "description": f"从文件 {file.filename} 导入",
        "code": content.decode("utf-8") if file_ext in ["py", "txt"] else "",
        "parameters": {},
        "tags": ["imported"],
        "status": "draft",
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "version": 1,
        "is_public": False,
        "performance": {
            "total_return": 0,
            "sharpe_ratio": 0,
            "max_drawdown": 0,
            "win_rate": 0
        }
    }
    
    strategies_db[strategy_id] = strategy
    
    return {
        "message": "策略导入成功",
        "strategy_id": strategy_id,
        "name": strategy["name"]
    }


@router.get("/strategies/public/list")
async def list_public_strategies(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    sort_by: str = Query("performance", pattern="^(performance|popularity|recent)$")
):
    """
    获取公开策略列表
    
    - **sort_by**: 排序方式（performance/popularity/recent）
    """
    # 获取所有公开策略
    public_strategies = [
        s for s in strategies_db.values() 
        if s.get("is_public", False)
    ]
    
    # 排序
    if sort_by == "performance":
        public_strategies.sort(
            key=lambda x: x["performance"]["total_return"], 
            reverse=True
        )
    elif sort_by == "recent":
        public_strategies.sort(
            key=lambda x: x["created_at"], 
            reverse=True
        )
    
    # 分页
    total = len(public_strategies)
    strategies = public_strategies[skip:skip + limit]
    
    return {
        "strategies": strategies,
        "total": total,
        "skip": skip,
        "limit": limit
    }


@router.get("/health")
async def health_check():
    """
    策略模块健康检查
    """
    return {
        "status": "healthy",
        "module": "strategy",
        "timestamp": datetime.now().isoformat(),
        "strategies_count": len(strategies_db)
    }