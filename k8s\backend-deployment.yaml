apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-backend
  namespace: quant-platform
spec:
  replicas: 3
  selector:
    matchLabels:
      app: quant-backend
  template:
    metadata:
      labels:
        app: quant-backend
    spec:
      containers:
      - name: backend
        image: quant-platform/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "postgresql://$(DATABASE_USER):$(DATABASE_PASSWORD)@$(DATABASE_HOST):$(DATABASE_PORT)/$(DATABASE_NAME)"
        - name: DATABASE_USER
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_USER
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: DATABASE_PASSWORD
        - name: DATABASE_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_HOST
        - name: DATABASE_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_PORT
        - name: DATABASE_NAME
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: DATABASE_NAME
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: REDIS_PORT
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-secret
              key: SECRET_KEY
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: ENVIRONMENT
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: quant-config
              key: LOG_LEVEL
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 45
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 15
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3

---
apiVersion: v1
kind: Service
metadata:
  name: quant-backend-service
  namespace: quant-platform
spec:
  selector:
    app: quant-backend
  ports:
  - port: 8000
    targetPort: 8000
  type: ClusterIP

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: quant-backend-hpa
  namespace: quant-platform
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: quant-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80