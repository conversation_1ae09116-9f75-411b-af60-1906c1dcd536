<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .success {
            border-left: 4px solid #28a745;
        }
        .error {
            border-left: 4px solid #dc3545;
        }
        .loading {
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <h1>简单API连接测试</h1>
    <p>测试前端是否能连接到后端API</p>
    
    <div class="test-item">
        <h3>1. 健康检查</h3>
        <button onclick="testHealth()">测试健康检查</button>
        <div id="health-result" class="result"></div>
    </div>
    
    <div class="test-item">
        <h3>2. 市场概览</h3>
        <button onclick="testOverview()">测试市场概览</button>
        <div id="overview-result" class="result"></div>
    </div>
    
    <div class="test-item">
        <h3>3. 股票列表</h3>
        <button onclick="testStocks()">测试股票列表</button>
        <div id="stocks-result" class="result"></div>
    </div>
    
    <div class="test-item">
        <h3>4. 网络诊断</h3>
        <button onclick="diagnoseNetwork()">诊断网络连接</button>
        <div id="network-result" class="result"></div>
    </div>

    <script>
        // 使用相对路径，通过前端代理访问后端API
        const BASE_URL = '';
        
        function setResult(elementId, content, type = 'loading') {
            const element = document.getElementById(elementId);
            element.innerHTML = content;
            element.className = `result ${type}`;
        }
        
        async function testAPI(url, resultId, description) {
            setResult(resultId, `⏳ 测试 ${description}...`, 'loading');
            
            try {
                console.log(`开始测试: ${url}`);
                
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                console.log(`响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    setResult(resultId, `✅ ${description} 成功\n状态: ${response.status}\n数据: ${JSON.stringify(data, null, 2)}`, 'success');
                    return data;
                } else {
                    const errorText = await response.text();
                    setResult(resultId, `❌ ${description} 失败\n状态: ${response.status}\n错误: ${errorText}`, 'error');
                    return null;
                }
            } catch (error) {
                console.error(`测试失败: ${error}`);
                
                let errorMessage = error.message;
                if (error.name === 'AbortError') {
                    errorMessage = '请求超时 (10秒)';
                } else if (error.message.includes('Failed to fetch')) {
                    errorMessage = '网络连接失败 - 可能是CORS问题或后端未运行';
                }
                
                setResult(resultId, `❌ ${description} 失败\n错误类型: ${error.name}\n错误信息: ${errorMessage}`, 'error');
                return null;
            }
        }
        
        async function testHealth() {
            await testAPI(`/api/health`, 'health-result', '健康检查');
        }
        
        async function testOverview() {
            await testAPI(`/api/v1/market/overview`, 'overview-result', '市场概览');
        }

        async function testStocks() {
            await testAPI(`/api/v1/market/stocks?pageSize=5`, 'stocks-result', '股票列表');
        }
        
        async function diagnoseNetwork() {
            setResult('network-result', '🔍 开始网络诊断...', 'loading');
            
            const tests = [
                { name: '代理健康检查', url: '/api/health' },
                { name: '代理市场概览', url: '/api/v1/market/overview' },
                { name: '直连健康检查', url: 'http://localhost:8000/api/health' },
                { name: 'CORS预检', url: '/api/v1/market/overview', method: 'OPTIONS' }
            ];
            
            let results = '网络诊断结果:\n\n';
            
            for (const test of tests) {
                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 5000);
                    
                    const response = await fetch(test.url, {
                        method: test.method || 'GET',
                        signal: controller.signal
                    });
                    
                    clearTimeout(timeoutId);
                    results += `✅ ${test.name}: ${response.status}\n`;
                } catch (error) {
                    results += `❌ ${test.name}: ${error.message}\n`;
                }
            }
            
            // 检查浏览器信息
            results += '\n浏览器信息:\n';
            results += `User Agent: ${navigator.userAgent}\n`;
            results += `当前页面: ${window.location.href}\n`;
            results += `目标API: ${BASE_URL}\n`;
            
            setResult('network-result', results, 'success');
        }
        
        // 页面加载时自动运行基本测试
        window.onload = function() {
            console.log('页面加载完成，开始自动测试...');
            setTimeout(() => {
                testHealth();
            }, 500);
        };
        
        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });
        
        window.addEventListener('unhandledrejection', function(e) {
            console.error('未处理的Promise拒绝:', e.reason);
        });
    </script>
</body>
</html>
