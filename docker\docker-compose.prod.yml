version: '3.8'

services:
  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ../frontend/dist:/usr/share/nginx/html
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - quant-network
    restart: unless-stopped

  # 前端服务 (生产模式)
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile.prod
    environment:
      - NODE_ENV=production
    networks:
      - quant-network
    restart: unless-stopped

  # 后端服务 (生产模式)
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile.prod
    environment:
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      - ENVIRONMENT=production
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # PostgreSQL数据库
  postgres:
    image: timescale/timescaledb:latest-pg15
    environment:
      - POSTGRES_DB=quant_db
      - POSTGRES_USER=quant_user
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../backend/scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - quant-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Redis缓存
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - quant-network
    restart: unless-stopped

  # Celery Worker (异步任务)
  celery-worker:
    build:
      context: ../backend
      dockerfile: Dockerfile.prod
    command: celery -A app.tasks.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped
    deploy:
      replicas: 2

  # Celery Beat (定时任务)
  celery-beat:
    build:
      context: ../backend
      dockerfile: Dockerfile.prod
    command: celery -A app.tasks.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://quant_user:${POSTGRES_PASSWORD}@postgres:5432/quant_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    networks:
      - quant-network
    restart: unless-stopped

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ../monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ../monitoring/prometheus/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - quant-network
    restart: unless-stopped

  # Grafana监控面板
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ../monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ../monitoring/grafana/dashboard.json:/var/lib/grafana/dashboards/dashboard.json
    depends_on:
      - prometheus
    networks:
      - quant-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  quant-network:
    driver: bridge