apiVersion: v1
kind: ConfigMap
metadata:
  name: quant-platform-config
  namespace: quant-platform
data:
  app.conf: |
    # 应用配置
    APP_NAME="量化交易平台"
    APP_ENV=production
    APP_DEBUG=false
    
    # 日志配置
    LOG_LEVEL=INFO
    LOG_FORMAT=json
    
    # 缓存配置
    CACHE_TTL=600
    MARKET_DATA_CACHE_TTL=30
    
    # 限流配置
    RATE_LIMIT_ENABLED=true
    RATE_LIMIT_PER_MINUTE=300
    
    # WebSocket配置
    WS_HEARTBEAT_INTERVAL=60
    WS_MAX_CONNECTIONS=10000
    
    # 特性开关
    FEATURE_ADVANCED_CHARTS=true
    FEATURE_AI_TRADING=true
    FEATURE_SOCIAL_TRADING=true
    
  nginx.conf: |
    upstream backend {
        server quant-platform-backend:8000;
    }
    
    server {
        listen 80;
        server_name api.quant-platform.com;
        
        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket支持
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        
        location /metrics {
            proxy_pass http://backend:9090;
            allow 10.0.0.0/8;
            deny all;
        }
    }