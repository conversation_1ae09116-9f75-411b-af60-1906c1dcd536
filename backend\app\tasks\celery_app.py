"""
Celery应用配置
"""

import os

from celery import Celery
from celery.schedules import crontab

from app.core.config import settings

# 创建Celery应用
celery_app = Celery(
    "quant_platform",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=[
        "app.tasks.trading_tasks",
        "app.tasks.backtest_tasks",
        "app.tasks.report_tasks",
        "app.tasks.data_tasks",
        "app.tasks.notification_tasks",
    ],
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="Asia/Shanghai",
    enable_utc=True,
    # 任务路由
    task_routes={
        "app.tasks.trading_tasks.*": {"queue": "trading"},
        "app.tasks.backtest_tasks.*": {"queue": "backtest"},
        "app.tasks.report_tasks.*": {"queue": "report"},
        "app.tasks.data_tasks.*": {"queue": "data"},
        "app.tasks.notification_tasks.*": {"queue": "notification"},
    },
    # 任务结果过期时间
    result_expires=3600,
    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    # 任务限制
    task_time_limit=300,  # 5分钟
    task_soft_time_limit=240,  # 4分钟
    # 监控配置
    worker_send_task_events=True,
    task_send_sent_event=True,
    # Beat调度配置
    beat_schedule={
        # 市场数据同步任务
        "sync-market-data": {
            "task": "app.tasks.data_tasks.sync_market_data",
            "schedule": 60.0,  # 每分钟执行一次
            "options": {"queue": "data"},
        },
        # 日终报告生成
        "generate-daily-report": {
            "task": "app.tasks.report_tasks.generate_daily_report",
            "schedule": crontab(hour=18, minute=0),  # 每天18:00执行
            "options": {"queue": "report"},
        },
        # 风险监控检查
        "risk-monitoring": {
            "task": "app.tasks.data_tasks.risk_monitoring_check",
            "schedule": 30.0,  # 每30秒执行一次
            "options": {"queue": "monitoring"},
        },
        # 系统健康检查
        "health-check": {
            "task": "app.tasks.data_tasks.system_health_check",
            "schedule": 300.0,  # 每5分钟执行一次
            "options": {"queue": "monitoring"},
        },
        # 清理过期数据
        "cleanup-expired-data": {
            "task": "app.tasks.data_tasks.cleanup_expired_data",
            "schedule": crontab(hour=2, minute=0),  # 每天凌晨2点执行
            "options": {"queue": "maintenance"},
        },
        # 发送通知
        "send-pending-notifications": {
            "task": "app.tasks.notification_tasks.send_pending_notifications",
            "schedule": 120.0,  # 每2分钟执行一次
            "options": {"queue": "notification"},
        },
    },
    beat_schedule_filename="celerybeat-schedule",
)

# 任务发现
celery_app.autodiscover_tasks()

if __name__ == "__main__":
    celery_app.start()
