<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API路径测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .pending { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 API路径修复测试</h1>
        <p>测试前后端API路径是否正确匹配</p>
        
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <div class="container">
        <h2>📊 API端点测试</h2>
        
        <div id="test-health" class="test-item pending">
            <h3>1. 健康检查</h3>
            <div class="url">GET /api/health</div>
            <button onclick="testHealth()">测试</button>
            <div class="result" id="result-health"></div>
        </div>

        <div id="test-quote" class="test-item pending">
            <h3>2. 股票行情</h3>
            <div class="url">GET /api/v1/market/quote/000001</div>
            <button onclick="testQuote()">测试</button>
            <div class="result" id="result-quote"></div>
        </div>

        <div id="test-stocks" class="test-item pending">
            <h3>3. 股票列表</h3>
            <div class="url">GET /api/v1/market/stocks</div>
            <button onclick="testStocks()">测试</button>
            <div class="result" id="result-stocks"></div>
        </div>

        <div id="test-user" class="test-item pending">
            <h3>4. 用户信息</h3>
            <div class="url">GET /api/user/info</div>
            <button onclick="testUser()">测试</button>
            <div class="result" id="result-user"></div>
        </div>

        <div id="test-websocket" class="test-item pending">
            <h3>5. WebSocket连接</h3>
            <div class="url">ws://localhost:8000/api/v1/ws</div>
            <button onclick="testWebSocket()">测试</button>
            <div class="result" id="result-websocket"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';

        async function testAPI(url, testId, description) {
            const testElement = document.getElementById(`test-${testId}`);
            const resultElement = document.getElementById(`result-${testId}`);
            
            testElement.className = 'test-item pending';
            resultElement.innerHTML = '⏳ 测试中...';

            try {
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                const data = await response.json();

                if (response.ok) {
                    testElement.className = 'test-item success';
                    resultElement.innerHTML = `
                        <div style="color: green; font-weight: bold;">✅ 成功 (${endTime - startTime}ms)</div>
                        <div><strong>状态码:</strong> ${response.status}</div>
                        <div><strong>响应数据:</strong></div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testElement.className = 'test-item error';
                resultElement.innerHTML = `
                    <div style="color: red; font-weight: bold;">❌ 失败</div>
                    <div><strong>错误:</strong> ${error.message}</div>
                `;
            }
        }

        async function testHealth() {
            await testAPI(`${BASE_URL}/api/health`, 'health', '健康检查');
        }

        async function testQuote() {
            await testAPI(`${BASE_URL}/api/v1/market/quote/000001`, 'quote', '股票行情');
        }

        async function testStocks() {
            await testAPI(`${BASE_URL}/api/v1/market/stocks?pageSize=5`, 'stocks', '股票列表');
        }

        async function testUser() {
            await testAPI(`${BASE_URL}/api/user/info`, 'user', '用户信息');
        }

        async function testWebSocket() {
            const testElement = document.getElementById('test-websocket');
            const resultElement = document.getElementById('result-websocket');
            
            testElement.className = 'test-item pending';
            resultElement.innerHTML = '⏳ 测试WebSocket连接...';

            try {
                const ws = new WebSocket('ws://localhost:8000/api/v1/ws');
                
                const timeout = setTimeout(() => {
                    ws.close();
                    throw new Error('连接超时');
                }, 5000);

                ws.onopen = function() {
                    clearTimeout(timeout);
                    testElement.className = 'test-item success';
                    resultElement.innerHTML = `
                        <div style="color: green; font-weight: bold;">✅ WebSocket连接成功</div>
                        <div><strong>状态:</strong> 已连接</div>
                        <div><strong>URL:</strong> ws://localhost:8000/api/v1/ws</div>
                    `;
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({
                        type: 'ping',
                        timestamp: Date.now()
                    }));
                };

                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    resultElement.innerHTML += `
                        <div><strong>收到消息:</strong></div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                };

                ws.onerror = function(error) {
                    clearTimeout(timeout);
                    testElement.className = 'test-item error';
                    resultElement.innerHTML = `
                        <div style="color: red; font-weight: bold;">❌ WebSocket连接失败</div>
                        <div><strong>错误:</strong> ${error.message || '连接错误'}</div>
                    `;
                };

                ws.onclose = function(event) {
                    if (testElement.className === 'test-item pending') {
                        testElement.className = 'test-item error';
                        resultElement.innerHTML = `
                            <div style="color: red; font-weight: bold;">❌ WebSocket连接关闭</div>
                            <div><strong>代码:</strong> ${event.code}</div>
                            <div><strong>原因:</strong> ${event.reason || '未知'}</div>
                        `;
                    }
                };

            } catch (error) {
                testElement.className = 'test-item error';
                resultElement.innerHTML = `
                    <div style="color: red; font-weight: bold;">❌ WebSocket测试失败</div>
                    <div><strong>错误:</strong> ${error.message}</div>
                `;
            }
        }

        async function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testQuote();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStocks();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testUser();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWebSocket();
            
            console.log('✅ 所有测试完成');
        }

        function clearResults() {
            const testItems = document.querySelectorAll('.test-item');
            testItems.forEach(item => {
                item.className = 'test-item pending';
            });
            
            const results = document.querySelectorAll('.result');
            results.forEach(result => {
                result.innerHTML = '';
            });
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('📋 API路径测试页面已加载');
            console.log('🔧 修复内容: 将 /market-data/market 路径简化为 /market');
        };
    </script>
</body>
</html>
