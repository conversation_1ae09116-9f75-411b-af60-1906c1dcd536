# 超级简化版依赖，专为演示使用
# FastAPI核心
fastapi==0.116.1
uvicorn[standard]==0.35.0

# 数据库
aiosqlite==0.21.0

# 数据处理
pandas>=2.2.0
numpy>=1.26.0

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
pyjwt==2.10.1

# HTTP客户端
httpx==0.25.2

# 配置管理
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 环境变量
python-dotenv==1.0.0

# WebSocket支持
websockets==15.0.1

# 图像处理
Pillow>=10.0.0

# 数据获取
tushare>=1.4.0
requests>=2.31.0

# 时间处理
python-dateutil>=2.8.2

# 测试
pytest>=7.4.0
pytest-asyncio>=0.21.0