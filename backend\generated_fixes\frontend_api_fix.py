
// 修复后的前端API配置
import axios from 'axios'

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'

// 创建axios实例
const http = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response?.status === 401) {
      // 未授权，跳转到登录页
      localStorage.removeItem('token')
      window.location.href = '/login'
    } else if (error.response?.status === 405) {
      // 方法不允许，记录错误
      console.error('Method not allowed:', error.config.method, error.config.url)
    }
    return Promise.reject(error)
  }
)

// 交易API
export const tradingApi = {
  // 创建订单
  createOrder: (data) => http.post('/api/v1/trading/orders', data),
  
  // 获取订单列表
  getOrders: (params) => http.get('/api/v1/trading/orders', { params }),
  
  // 取消订单
  cancelOrder: (orderId) => http.delete(`/api/v1/trading/orders/${orderId}`),
  
  // 获取持仓
  getPositions: () => http.get('/api/v1/trading/positions'),
  
  // 获取账户信息
  getAccount: () => http.get('/api/v1/trading/account')
}

// 认证API
export const authApi = {
  // 注册
  register: (data) => http.post('/api/v1/auth/register', data),
  
  // 登录
  login: (data) => http.post('/api/v1/auth/login', data),
  
  // 获取当前用户
  getCurrentUser: () => http.get('/api/v1/auth/me'),
  
  // 登出
  logout: () => http.post('/api/v1/auth/logout')
}

export default http
