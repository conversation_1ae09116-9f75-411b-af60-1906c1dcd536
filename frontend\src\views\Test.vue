<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果您能看到这个页面，说明Vue应用正常工作。</p>
    <el-button type="primary" @click="goToTrading">前往实盘交易</el-button>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goToTrading = () => {
  router.push('/trading/miniqmt')
}
</script>

<style scoped>
.test-page {
  padding: 20px;
  text-align: center;
}

h1 {
  color: #409eff;
  margin-bottom: 20px;
}

p {
  margin-bottom: 20px;
  color: #606266;
}
</style>
