"""简化版主应用 - 用于开发测试"""

import logging
import os
import time
from contextlib import asynccontextmanager
import secrets
import base64
from io import BytesIO

from fastapi import FastAPI, Request, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from decimal import Decimal
from typing import List, Optional, Dict
import json
import asyncio
# import tushare as ts  # 可选依赖，如果没有安装则使用模拟数据
try:
    import tushare as ts
    TUSHARE_AVAILABLE = True
except ImportError:
    TUSHARE_AVAILABLE = False
    ts = None
# import pandas as pd  # 可选依赖
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False
    pd = None
from datetime import datetime, timedelta

# 设置环境变量
os.environ["USE_REALTIME_DATA"] = "false"
os.environ["WS_ENABLED"] = "false"
os.environ["ENABLE_IP_WHITELIST"] = "false"

logger = logging.getLogger(__name__)

# 验证码存储
CAPTCHA_ANSWERS = {}

# 初始化Tushare
TUSHARE_TOKEN = "f7eedf159b0e6e4e4fc97a59f11fcb6936201c698a6974cbb8e18400"
if TUSHARE_AVAILABLE and TUSHARE_TOKEN:
    ts.set_token(TUSHARE_TOKEN)
    pro = ts.pro_api()
else:
    pro = None

# 用户存储（简化版，实际应用中应使用数据库）
USERS_DB = {
    "admin": {
        "id": "admin_user",
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",  # 实际应用中应该加密
        "created_at": "2025-07-26T19:00:00"
    }
}

# Pydantic 模型
class SliderVerifyRequest(BaseModel):
    id: str
    position: int

class SliderCaptchaResponse(BaseModel):
    success: bool
    data: dict
    message: str

class UserRegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    confirmPassword: str = None

class UserLoginRequest(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    success: bool
    data: dict = None
    message: str


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("Starting simplified Quant Platform API")
    yield
    logger.info("Shutting down application")


app = FastAPI(
    title="量化投资平台 API (Dev)",
    description="开发版本 - 简化配置",
    version="1.0.0",
    lifespan=lifespan
)

# CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173", "http://localhost:5174", "http://127.0.0.1:5174", "http://localhost:5175", "http://127.0.0.1:5175"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 基础路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Welcome to Quant Platform API (Dev)",
        "version": "1.0.0",
        "docs": "/docs"
    }


@app.get("/health")
async def health():
    """健康检查"""
    return {"status": "healthy"}





# ============ 市场数据相关API ============
@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览"""
    try:
        import datetime
        return {
            "success": True,
            "data": {
                "market_status": "open",  # open, closed, pre_market, after_market
                "timestamp": datetime.datetime.now().isoformat(),
                "indices": [
                    {
                        "name": "上证指数",
                        "symbol": "000001.SH",
                        "current": 3245.68,
                        "change": 15.23,
                        "change_percent": 0.47,
                        "volume": 245678900000,
                        "turnover": 3456789000000
                    },
                    {
                        "name": "深证成指",
                        "symbol": "399001.SZ",
                        "current": 10876.45,
                        "change": -23.56,
                        "change_percent": -0.22,
                        "volume": 189234500000,
                        "turnover": 2987654000000
                    },
                    {
                        "name": "创业板指",
                        "symbol": "399006.SZ",
                        "current": 2234.89,
                        "change": 8.76,
                        "change_percent": 0.39,
                        "volume": 98765400000,
                        "turnover": 1876543000000
                    }
                ],
                "market_stats": {
                    "total_stocks": 4856,
                    "rising_stocks": 2234,
                    "falling_stocks": 1987,
                    "unchanged_stocks": 635,
                    "limit_up": 45,
                    "limit_down": 12,
                    "total_turnover": 8320986000000,
                    "total_volume": 533679300000
                },
                "hot_sectors": [
                    {"name": "人工智能", "change_percent": 3.45, "stocks_count": 156},
                    {"name": "新能源汽车", "change_percent": 2.87, "stocks_count": 234},
                    {"name": "半导体", "change_percent": 1.98, "stocks_count": 189},
                    {"name": "医药生物", "change_percent": -1.23, "stocks_count": 345},
                    {"name": "房地产", "change_percent": -2.56, "stocks_count": 123}
                ]
            }
        }
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail="获取市场概览失败")

# 股票列表缓存
STOCK_LIST_CACHE = {}
STOCK_LIST_CACHE_TIME = None
CACHE_DURATION = 300  # 5分钟缓存

@app.get("/api/v1/market/stocks")
async def get_stock_list(exchange: str = "", limit: int = 20):
    """获取股票列表（使用缓存优化）"""
    global STOCK_LIST_CACHE, STOCK_LIST_CACHE_TIME
    
    # 检查缓存
    cache_key = f"{exchange}_{limit}"
    now = datetime.now()
    
    if (STOCK_LIST_CACHE_TIME and 
        (now - STOCK_LIST_CACHE_TIME).seconds < CACHE_DURATION and 
        cache_key in STOCK_LIST_CACHE):
        logger.info("使用缓存的股票列表数据")
        return STOCK_LIST_CACHE[cache_key]
    
    # 生成模拟数据（避免频繁调用Tushare API）
    mock_stocks = [
        {"symbol": "000001", "name": "平安银行", "exchange": "SZSE", "industry": "银行", "ts_code": "000001.SZ"},
        {"symbol": "000002", "name": "万科A", "exchange": "SZSE", "industry": "房地产", "ts_code": "000002.SZ"},
        {"symbol": "000858", "name": "五粮液", "exchange": "SZSE", "industry": "食品饮料", "ts_code": "000858.SZ"},
        {"symbol": "002415", "name": "海康威视", "exchange": "SZSE", "industry": "电子", "ts_code": "002415.SZ"},
        {"symbol": "300750", "name": "宁德时代", "exchange": "SZSE", "industry": "电力设备", "ts_code": "300750.SZ"},
        {"symbol": "600519", "name": "贵州茅台", "exchange": "SSE", "industry": "食品饮料", "ts_code": "600519.SH"},
        {"symbol": "600036", "name": "招商银行", "exchange": "SSE", "industry": "银行", "ts_code": "600036.SH"},
        {"symbol": "601318", "name": "中国平安", "exchange": "SSE", "industry": "保险", "ts_code": "601318.SH"},
        {"symbol": "600276", "name": "恒瑞医药", "exchange": "SSE", "industry": "医药生物", "ts_code": "600276.SH"},
        {"symbol": "600309", "name": "万华化学", "exchange": "SSE", "industry": "化工", "ts_code": "600309.SH"},
        {"symbol": "000333", "name": "美的集团", "exchange": "SZSE", "industry": "家用电器", "ts_code": "000333.SZ"},
        {"symbol": "002594", "name": "比亚迪", "exchange": "SZSE", "industry": "汽车", "ts_code": "002594.SZ"},
        {"symbol": "600900", "name": "长江电力", "exchange": "SSE", "industry": "公用事业", "ts_code": "600900.SH"},
        {"symbol": "601012", "name": "隆基绿能", "exchange": "SSE", "industry": "电力设备", "ts_code": "601012.SH"},
        {"symbol": "600438", "name": "通威股份", "exchange": "SSE", "industry": "农林牧渔", "ts_code": "600438.SH"},
        {"symbol": "002475", "name": "立讯精密", "exchange": "SZSE", "industry": "电子", "ts_code": "002475.SZ"},
        {"symbol": "300059", "name": "东方财富", "exchange": "SZSE", "industry": "非银金融", "ts_code": "300059.SZ"},
        {"symbol": "002271", "name": "东方雨虹", "exchange": "SZSE", "industry": "建筑材料", "ts_code": "002271.SZ"},
        {"symbol": "603288", "name": "海天味业", "exchange": "SSE", "industry": "食品饮料", "ts_code": "603288.SH"},
        {"symbol": "000568", "name": "泸州老窖", "exchange": "SZSE", "industry": "食品饮料", "ts_code": "000568.SZ"}
    ]
    
    # 根据交易所过滤
    if exchange:
        mock_stocks = [s for s in mock_stocks if s["exchange"].upper() == exchange.upper()]
    
    # 限制返回数量
    result_stocks = mock_stocks[:limit]
    
    result = {
        "success": True,
        "data": result_stocks,
        "total": len(result_stocks)
    }
    
    # 更新缓存
    STOCK_LIST_CACHE[cache_key] = result
    STOCK_LIST_CACHE_TIME = now
    
    return result


@app.get("/api/v1/market/quote/{symbol}")
async def get_stock_quote(symbol: str):
    """获取股票报价（使用Tushare真实数据）"""
    try:
        # 处理股票代码格式
        ts_code = symbol
        if '.' not in symbol:
            if symbol.startswith('6'):
                ts_code = f"{symbol}.SH"
            elif symbol.startswith('0') or symbol.startswith('3'):
                ts_code = f"{symbol}.SZ"
        
        # 获取最新日线数据
        if pro:
            today = datetime.now().strftime('%Y%m%d')
            df = pro.daily(ts_code=ts_code, start_date=(datetime.now() - timedelta(days=5)).strftime('%Y%m%d'), 
                          end_date=today)
            
            if df.empty:
                raise Exception("无数据")
            
            # 获取最新一条数据
            latest = df.iloc[0]
            
            # 获取股票基本信息
            info_df = pro.stock_basic(ts_code=ts_code, fields='ts_code,symbol,name')
            stock_name = info_df.iloc[0]['name'] if not info_df.empty else symbol
        else:
            raise Exception("Tushare not available")
        
        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "ts_code": ts_code,
                "name": stock_name,
                "price": float(latest['close']),
                "change": float(latest['change']),
                "changePercent": float(latest['pct_chg']),
                "volume": int(latest['vol'] * 100),  # 转换为股
                "turnover": float(latest['amount'] * 1000),  # 转换为元
                "high": float(latest['high']),
                "low": float(latest['low']),
                "open": float(latest['open']),
                "close": float(latest['close']),
                "pre_close": float(latest['pre_close']),
                "trade_date": latest['trade_date'],
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"获取股票报价失败: {e}")
        # 返回模拟数据
        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "name": f"股票{symbol}",
                "price": 50.00,
                "change": 1.25,
                "changePercent": 2.56,
                "volume": 10000000,
                "turnover": 500000000.0,
                "high": 52.50,
                "low": 49.80,
                "open": 50.20,
                "close": 50.00,
                "timestamp": datetime.now().isoformat()
            }
        }


@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(
    symbol: str,
    period: str = "1d",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 100
):
    """获取K线数据（使用Tushare真实数据）"""
    try:
        # 处理股票代码格式
        ts_code = symbol
        if '.' not in symbol:
            if symbol.startswith('6'):
                ts_code = f"{symbol}.SH"
            elif symbol.startswith('0') or symbol.startswith('3'):
                ts_code = f"{symbol}.SZ"
        
        # 处理日期参数
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        else:
            # 转换日期格式 YYYY-MM-DD 到 YYYYMMDD
            end_date = end_date.replace('-', '')
            
        if not start_date:
            # 默认获取limit天的数据
            start_date = (datetime.now() - timedelta(days=limit)).strftime('%Y%m%d')
        else:
            start_date = start_date.replace('-', '')
        
        # 获取日线数据
        if not pro:
            raise Exception("Tushare not available")
        df = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
        
        if df.empty:
            raise Exception("无数据")
        
        # 转换为K线格式
        klines = []
        for _, row in df.iterrows():
            klines.append({
                "timestamp": datetime.strptime(row['trade_date'], '%Y%m%d').strftime("%Y-%m-%d %H:%M:%S"),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
                "volume": int(row['vol'] * 100),  # 转换为股
                "turnover": float(row['amount'] * 1000),  # 转换为元
                "change": float(row['change']),
                "pct_chg": float(row['pct_chg'])
            })
        
        # 按时间正序排列
        klines.reverse()
        
        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "ts_code": ts_code,
                "period": period,
                "klines": klines[:limit]
            }
        }
    except Exception as e:
        logger.error(f"获取K线数据失败: {e}")
        # 返回模拟数据
        base_price = 50.0
        kline_data = []
        
        for i in range(min(limit, 30)):
            date = datetime.now() - timedelta(days=i)
            open_price = base_price + (i % 10 - 5) * 0.5
            close_price = open_price + (i % 5 - 2) * 0.3
            high_price = max(open_price, close_price) + 0.5
            low_price = min(open_price, close_price) - 0.3
            volume = 1000000 + (i % 7) * 200000
            
            kline_data.append({
                "timestamp": date.strftime("%Y-%m-%d %H:%M:%S"),
                "open": round(open_price, 2),
                "high": round(high_price, 2),
                "low": round(low_price, 2),
                "close": round(close_price, 2),
                "volume": volume,
                "turnover": round(volume * close_price, 2)
            })
        
        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "period": period,
                "klines": list(reversed(kline_data))
            }
        }


# ============ 历史数据API ============
@app.get("/api/v1/historical/stats")
async def get_historical_stats():
    """获取历史数据统计信息"""
    try:
        # 模拟统计数据
        stats = {
            "total_stocks": 4500,
            "total_markets": 2,
            "total_industries": 28,
            "cache_status": "正常",
            "last_update": datetime.now().isoformat(),
            "data_coverage": {
                "start_date": "2020-01-01",
                "end_date": datetime.now().strftime("%Y-%m-%d")
            }
        }
        return {
            "success": True,
            "data": stats,
            "message": "获取历史数据统计成功"
        }
    except Exception as e:
        logger.error(f"获取历史数据统计失败: {e}")
        return {
            "success": False,
            "data": None,
            "message": f"获取统计信息失败: {str(e)}"
        }

@app.get("/api/v1/historical/stocks")
async def get_historical_stocks(
    limit: int = 50,
    offset: int = 0,
    market: Optional[str] = None
):
    """获取历史股票列表"""
    try:
        # 调用现有的股票列表API
        result = await get_stock_list(limit=limit)
        if result["success"]:
            stocks = result["data"]
            # 添加历史数据相关字段
            for stock in stocks:
                stock.update({
                    "has_historical_data": True,
                    "data_start_date": "2020-01-01",
                    "data_end_date": datetime.now().strftime("%Y-%m-%d"),
                    "record_count": 1000 + hash(stock["symbol"]) % 2000
                })

            return {
                "success": True,
                "data": stocks,
                "total": len(stocks),
                "limit": limit,
                "offset": offset,
                "message": "获取历史股票列表成功"
            }
        else:
            return result
    except Exception as e:
        logger.error(f"获取历史股票列表失败: {e}")
        return {
            "success": False,
            "data": [],
            "message": f"获取股票列表失败: {str(e)}"
        }

@app.get("/api/v1/historical/search")
async def search_historical_stocks(
    keyword: str,
    limit: int = 20
):
    """搜索历史股票数据"""
    try:
        # 获取股票列表并进行搜索
        result = await get_stock_list(limit=100)
        if result["success"]:
            stocks = result["data"]
            # 根据关键词过滤
            filtered_stocks = [
                stock for stock in stocks
                if keyword.lower() in stock["symbol"].lower() or
                   keyword.lower() in stock["name"].lower()
            ][:limit]

            return {
                "success": True,
                "data": filtered_stocks,
                "total": len(filtered_stocks),
                "keyword": keyword,
                "message": "搜索历史股票数据成功"
            }
        else:
            return {
                "success": False,
                "data": [],
                "message": "搜索失败"
            }
    except Exception as e:
        logger.error(f"搜索历史股票数据失败: {e}")
        return {
            "success": False,
            "data": [],
            "message": f"搜索失败: {str(e)}"
        }

@app.get("/api/v1/historical/stock/{symbol}")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 1000
):
    """获取单只股票的历史数据"""
    try:
        # 调用现有的K线数据API
        result = await get_kline_data(
            symbol=symbol,
            period="1d",
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        if result["success"]:
            return {
                "success": True,
                "data": {
                    "symbol": symbol,
                    "kline_data": result["data"],
                    "start_date": start_date or "2020-01-01",
                    "end_date": end_date or datetime.now().strftime("%Y-%m-%d"),
                    "total_records": len(result["data"])
                },
                "message": "获取历史股票数据成功"
            }
        else:
            return result
    except Exception as e:
        logger.error(f"获取历史股票数据失败: {e}")
        return {
            "success": False,
            "data": None,
            "message": f"获取历史数据失败: {str(e)}"
        }

@app.get("/api/v1/historical/kline")
async def get_historical_kline(
    symbol: str,
    period: str = "1d",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 1000
):
    """获取综合K线历史数据"""
    try:
        # 调用现有的K线数据API
        result = await get_kline_data(
            symbol=symbol,
            period=period,
            start_date=start_date,
            end_date=end_date,
            limit=limit
        )

        if result["success"]:
            # 增强返回数据
            enhanced_data = {
                "symbol": symbol,
                "period": period,
                "data": result["data"],
                "metadata": {
                    "start_date": start_date or "2020-01-01",
                    "end_date": end_date or datetime.now().strftime("%Y-%m-%d"),
                    "total_records": len(result["data"]),
                    "data_source": "historical_csv",
                    "last_updated": datetime.now().isoformat()
                }
            }

            return {
                "success": True,
                "data": enhanced_data,
                "message": "获取综合K线历史数据成功"
            }
        else:
            return result
    except Exception as e:
        logger.error(f"获取综合K线历史数据失败: {e}")
        return {
            "success": False,
            "data": None,
            "message": f"获取K线数据失败: {str(e)}"
        }

# ============ 简化的市场数据API（兼容前端）============
@app.get("/api/stocks")
async def get_stocks():
    """获取股票列表（简化版）"""
    # 直接调用已有的API
    result = await get_stock_list(limit=50)
    return result["data"] if result["success"] else []

@app.get("/api/market_data")
async def get_market_data():
    """获取实时行情（简化版）"""
    try:
        # 获取一些热门股票的行情
        symbols = ["000001.SZ", "000002.SZ", "600519.SH", "000858.SZ", "600036.SH"]
        quotes = []
        
        for symbol in symbols:
            try:
                result = await get_stock_quote(symbol.split('.')[0])
                if result["success"] and result["data"]:
                    quotes.append(result["data"])
            except:
                pass
        
        return quotes
    except Exception as e:
        logger.error(f"获取市场数据失败: {e}")
        return []

@app.get("/api/v1/market/current-prices")
async def get_current_prices():
    """获取当前价格（模拟数据）"""
    return [
        {
            "symbol": "BTCUSDT",
            "price": 45000.00,
            "change": 2.5,
            "changePercent": 0.056,
            "volume": 1234567890
        },
        {
            "symbol": "ETHUSDT",
            "price": 2500.00,
            "change": -30.00,
            "changePercent": -1.186,
            "volume": 987654321
        }
    ]

@app.get("/api/v1/market/sectors")
async def get_market_sectors():
    """获取市场板块数据"""
    return {
        "success": True,
        "data": [
            {"name": "科技", "change_percent": 2.45, "stocks_count": 156, "market_cap": 2500000000000},
            {"name": "金融", "change_percent": 1.23, "stocks_count": 89, "market_cap": 1800000000000},
            {"name": "医药", "change_percent": -0.56, "stocks_count": 234, "market_cap": 1200000000000},
            {"name": "消费", "change_percent": 0.89, "stocks_count": 178, "market_cap": 980000000000},
            {"name": "能源", "change_percent": 3.21, "stocks_count": 67, "market_cap": 750000000000}
        ]
    }

@app.get("/api/v1/market/rankings")
async def get_market_rankings(type: str = "change_percent", limit: int = 50):
    """获取市场排行榜"""
    if type == "change_percent":
        data = [
            {"symbol": "000001", "name": "平安银行", "change_percent": 9.98, "price": 13.75},
            {"symbol": "600519", "name": "贵州茅台", "change_percent": 8.45, "price": 1820.50},
            {"symbol": "000858", "name": "五粮液", "change_percent": 7.23, "price": 169.80}
        ]
    elif type == "turnover":
        data = [
            {"symbol": "000001", "name": "平安银行", "turnover": 2500000000, "volume": 180000000},
            {"symbol": "600036", "name": "招商银行", "turnover": 1800000000, "volume": 50000000},
            {"symbol": "000002", "name": "万科A", "turnover": 1200000000, "volume": 65000000}
        ]
    else:
        data = []

    return {
        "success": True,
        "data": data[:limit],
        "total": len(data)
    }

@app.get("/api/v1/market/watchlist")
async def get_market_watchlist():
    """获取自选股列表"""
    return {
        "success": True,
        "data": [
            {"symbol": "000001", "name": "平安银行", "price": 12.50, "change_percent": 1.22},
            {"symbol": "600519", "name": "贵州茅台", "price": 1680.50, "change_percent": 0.91},
            {"symbol": "000858", "name": "五粮液", "price": 158.20, "change_percent": 1.80}
        ]
    }

@app.get("/api/v1/market/news")
async def get_market_news(limit: int = 10):
    """获取市场新闻"""
    import datetime
    return {
        "success": True,
        "data": [
            {
                "id": 1,
                "title": "A股三大指数集体上涨，科技股表现强势",
                "summary": "今日A股市场表现活跃，上证指数上涨0.47%...",
                "source": "财经新闻",
                "publish_time": datetime.datetime.now().isoformat(),
                "url": "#"
            },
            {
                "id": 2,
                "title": "央行宣布降准0.25个百分点",
                "summary": "中国人民银行决定于近期下调存款准备金率...",
                "source": "央行公告",
                "publish_time": datetime.datetime.now().isoformat(),
                "url": "#"
            }
        ][:limit]
    }


@app.get("/api/v1/account/info")
async def get_account_info():
    """获取账户信息（模拟数据）"""
    return {
        "totalBalance": 1000000.00,
        "availableBalance": 800000.00,
        "frozenBalance": 200000.00,
        "totalProfit": 50000.00,
        "todayProfit": 5000.00,
        "profitRate": 5.0
    }


@app.get("/api/v1/trade/orders")
async def get_orders():
    """获取订单列表（模拟数据）"""
    return []


@app.get("/api/v1/strategies")
async def get_strategies():
    """获取策略列表（模拟数据）"""
    return []


def create_slider_captcha():
    """生成滑块验证码（简化版本，不依赖PIL）"""
    try:
        logger.info("创建滑块验证码：开始")
        
        # 2. 随机确定滑块位置
        slider_x = secrets.randbelow(151) + 100  # 100-250
        slider_y = secrets.randbelow(81) + 20  # 20-100
        slider_size = 40
        logger.info(f"创建滑块验证码：滑块位置 {slider_x}, {slider_y}")

        # 返回简化的数据，让前端处理图片生成
        # 使用占位符base64图片
        placeholder_image = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        
        logger.info("创建滑块验证码：完成")
        return placeholder_image, placeholder_image, slider_x, slider_y, slider_size

    except Exception as e:
        logger.error(f"创建滑块验证码失败: {e}")
        raise


@app.get("/api/captcha/slider", response_model=SliderCaptchaResponse)
async def get_slider_captcha():
    """获取滑块验证码"""
    logger.info("收到获取滑块验证码的请求")
    try:
        background_image, slider_image, position, y_pos, slider_height = create_slider_captcha()
        captcha_id = "captcha_" + secrets.token_hex(8)
        CAPTCHA_ANSWERS[captcha_id] = position
        logger.info(f"成功创建验证码, ID: {captcha_id}")

        return {
            "success": True,
            "data": {
                "id": captcha_id,
                "background_image": background_image,
                "slider_image": slider_image,
                "y": y_pos,
                "h": slider_height
            },
            "message": "验证码生成成功"
        }
    except Exception as e:
        logger.error(f"[/slider] 端点处理异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="生成验证码时发生内部错误")


# 添加v1版本的验证码API路由
@app.get("/api/v1/auth/captcha", response_model=SliderCaptchaResponse)
async def get_captcha_v1():
    """获取验证码 - v1版本 (兼容性路由)"""
    return await get_slider_captcha()


@app.get("/api/v1/captcha/slider", response_model=SliderCaptchaResponse)
async def get_slider_captcha_v1():
    """获取滑块验证码 - v1版本"""
    return await get_slider_captcha()


@app.post("/api/captcha/slider/verify")
async def verify_slider_captcha(request: SliderVerifyRequest):
    """验证滑块验证码"""
    logger.info(f"收到验证请求: ID={request.id}, Position={request.position}")

    if request.id not in CAPTCHA_ANSWERS:
        logger.warning(f"验证码ID不存在: {request.id}")
        raise HTTPException(status_code=400, detail="验证码已过期或无效")

    correct_position = CAPTCHA_ANSWERS[request.id]
    tolerance = 5  # 允许5像素误差

    if abs(request.position - correct_position) <= tolerance:
        # 验证成功，清除验证码
        del CAPTCHA_ANSWERS[request.id]
        logger.info(f"验证成功: ID={request.id}")
        return {
            "success": True,
            "data": {"token": f"verify_token_{secrets.token_hex(16)}"},
            "message": "验证成功"
        }
    else:
        logger.info(f"验证失败: ID={request.id}, 期望={correct_position}, 实际={request.position}")
        return {
            "success": False,
            "data": None,
            "message": "验证失败，请重试"
        }


# 添加v1版本的验证路由
@app.post("/api/v1/auth/captcha/verify")
async def verify_captcha_v1(request: SliderVerifyRequest):
    """验证验证码 - v1版本 (兼容性路由)"""
    return await verify_slider_captcha(request)


@app.post("/api/v1/captcha/slider/verify")
async def verify_slider_captcha_v1(request: SliderVerifyRequest):
    """验证滑块验证码 - v1版本"""
    return await verify_slider_captcha(request)


# 删除错误的 /api/auth/register 路径，只保留 /api/v1/auth/register


@app.post("/api/auth/login", response_model=UserResponse)
async def login_user(request: UserLoginRequest):
    """用户登录"""
    logger.info(f"收到登录请求: username={request.username}")

    try:
        # 检查用户是否存在
        if request.username not in USERS_DB:
            logger.warning(f"用户不存在: {request.username}")
            return {
                "success": False,
                "data": None,
                "message": "用户名或密码错误"
            }

        user_data = USERS_DB[request.username]

        # 验证密码
        if user_data["password"] != request.password:
            logger.warning(f"密码错误: {request.username}")
            return {
                "success": False,
                "data": None,
                "message": "用户名或密码错误"
            }

        # 生成简单的token
        token = f"token_{secrets.token_hex(16)}"

        logger.info(f"用户登录成功: {request.username}")
        return {
            "success": True,
            "data": {
                "token": token,
                "user": {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"]
                }
            },
            "message": "登录成功"
        }

    except Exception as e:
        logger.error(f"登录失败: {e}")
        return {
            "success": False,
            "data": None,
            "message": "登录失败，请重试"
        }


# 添加支持表单数据的登录端点
from fastapi import Form

@app.post("/api/v1/auth/login")
async def login_user_v1(
    username: str = Form(...),
    password: str = Form(...)
):
    """用户登录 (v1版本) - 支持表单数据"""
    print(f"🔍 收到v1登录请求: username={username}, password={password}")
    print(f"🔍 当前用户数据库: {USERS_DB}")
    logger.info(f"收到v1登录请求: username={username}")

    try:
        # 检查用户是否存在
        if username not in USERS_DB:
            print(f"❌ 用户不存在: {username}")
            logger.warning(f"用户不存在: {username}")
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        user_data = USERS_DB[username]
        print(f"🔍 找到用户数据: {user_data}")

        # 验证密码
        if user_data["password"] != password:
            print(f"❌ 密码错误: 期望={user_data['password']}, 实际={password}")
            logger.warning(f"密码错误: {username}")
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 生成简单的token
        access_token = f"token_{secrets.token_hex(16)}"

        print(f"✅ 用户登录成功: {username}")
        logger.info(f"用户登录成功: {username}")

        # 返回标准OAuth2格式
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user_data["id"],
                "username": user_data["username"],
                "email": user_data["email"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        logger.error(f"登录失败: {e}")
        raise HTTPException(status_code=500, detail="登录失败，请重试")

# 保留JSON格式的登录端点
@app.post("/api/v1/auth/login-json")
async def login_user_json(request: UserLoginRequest):
    """用户登录 (JSON格式)"""
    print(f"🔍 收到JSON登录请求: username={request.username}, password={request.password}")
    logger.info(f"收到JSON登录请求: username={request.username}")

    try:
        # 检查用户是否存在
        if request.username not in USERS_DB:
            print(f"❌ 用户不存在: {request.username}")
            logger.warning(f"用户不存在: {request.username}")
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        user_data = USERS_DB[request.username]
        print(f"🔍 找到用户数据: {user_data}")

        # 验证密码
        if user_data["password"] != request.password:
            print(f"❌ 密码错误: 期望={user_data['password']}, 实际={request.password}")
            logger.warning(f"密码错误: {request.username}")
            raise HTTPException(status_code=401, detail="用户名或密码错误")

        # 生成简单的token
        token = f"token_{secrets.token_hex(16)}"

        print(f"✅ 用户登录成功: {request.username}")
        logger.info(f"用户登录成功: {request.username}")

        # 返回前端期望的格式
        response_data = {
            "user": {
                "id": user_data["id"],
                "username": user_data["username"],
                "email": user_data["email"]
            },
            "token": token
        }

        return {
            "success": True,
            "data": response_data,
            "message": "登录成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        logger.error(f"登录失败: {e}")
        raise HTTPException(status_code=500, detail="登录失败，请重试")


@app.post("/api/v1/auth/register")
async def register_user_v1(request: UserRegisterRequest):
    """用户注册 (v1版本) - 返回格式适配前端"""
    logger.info(f"收到v1注册请求: username={request.username}, email={request.email}")

    try:
        # 检查用户名是否已存在
        if request.username in USERS_DB:
            logger.warning(f"用户名已存在: {request.username}")
            raise HTTPException(status_code=400, detail="用户名已存在")

        # 检查邮箱是否已存在
        for user_data in USERS_DB.values():
            if user_data["email"] == request.email:
                logger.warning(f"邮箱已存在: {request.email}")
                raise HTTPException(status_code=400, detail="邮箱已存在")

        # 创建新用户
        user_id = len(USERS_DB) + 1
        new_user = {
            "id": user_id,
            "username": request.username,
            "email": request.email,
            "password": request.password  # 实际应用中应该加密
        }

        USERS_DB[request.username] = new_user
        logger.info(f"用户注册成功: {request.username}")

        # 生成token
        token = f"token_{secrets.token_hex(16)}"

        # 返回前端期望的格式
        return {
            "user": {
                "id": new_user["id"],
                "username": new_user["username"],
                "email": new_user["email"]
            },
            "token": token
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"注册失败: {e}")
        raise HTTPException(status_code=500, detail="注册失败，请重试")


# ============ 增强的交易相关API ============
@app.post("/api/v1/trading/order")
async def create_order(order_data: dict):
    """下单接口"""
    try:
        import datetime
        order_id = f"ORDER_{secrets.token_hex(8)}"
        
        # 模拟订单处理
        order = {
            "order_id": order_id,
            "symbol": order_data.get("symbol", "000001"),
            "side": order_data.get("side", "buy"),  # buy/sell
            "type": order_data.get("type", "limit"),  # limit/market
            "quantity": order_data.get("quantity", 100),
            "price": order_data.get("price", 10.0),
            "status": "submitted",
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": order,
            "message": "订单提交成功"
        }
    except Exception as e:
        logger.error(f"下单失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 策略相关API ============
@app.post("/api/v1/strategies/backtest")
async def run_backtest(backtest_request: dict):
    """策略回测接口"""
    try:
        import datetime
        from decimal import Decimal
        
        # 获取回测参数
        strategy_code = backtest_request.get("strategy_code", "")
        start_date = backtest_request.get("start_date", "2024-01-01")
        end_date = backtest_request.get("end_date", "2024-12-31")
        initial_capital = backtest_request.get("initial_capital", 1000000)
        symbols = backtest_request.get("symbols", ["000001", "600519"])
        
        # 生成模拟回测结果
        backtest_id = f"BACKTEST_{secrets.token_hex(8)}"
        
        # 模拟回测性能指标
        total_return = 0.15  # 15%收益
        sharpe_ratio = 1.2
        max_drawdown = -0.08  # 8%最大回撤
        volatility = 0.16  # 16%波动率
        
        # 生成模拟交易记录
        trades = []
        for i in range(5):  # 生成5笔交易
            trade_date = datetime.datetime.now() - datetime.timedelta(days=i*10)
            trades.append({
                "date": trade_date.strftime("%Y-%m-%d"),
                "symbol": symbols[i % len(symbols)],
                "action": "buy" if i % 2 == 0 else "sell",
                "quantity": 1000,
                "price": 50.0 + i * 2.5,
                "amount": (50.0 + i * 2.5) * 1000
            })
        
        # 生成净值曲线数据
        nav_curve = []
        for i in range(30):  # 30天的净值数据
            date = datetime.datetime.now() - datetime.timedelta(days=29-i)
            nav_value = 1.0 + (total_return * i / 29) + (i % 5 - 2) * 0.002  # 加入一些波动
            nav_curve.append({
                "date": date.strftime("%Y-%m-%d"),
                "nav": round(nav_value, 4),
                "cumulative_return": round((nav_value - 1) * 100, 2)
            })
        
        result = {
            "backtest_id": backtest_id,
            "status": "completed",
            "start_date": start_date,
            "end_date": end_date,
            "initial_capital": initial_capital,
            "final_capital": initial_capital * (1 + total_return),
            "performance": {
                "total_return": total_return,
                "annualized_return": total_return * 2,  # 假设是半年回测
                "sharpe_ratio": sharpe_ratio,
                "max_drawdown": max_drawdown,
                "volatility": volatility,
                "win_rate": 0.6,  # 60%胜率
                "profit_loss_ratio": 1.5,  # 盈亏比
                "total_trades": len(trades),
                "winning_trades": int(len(trades) * 0.6),
                "losing_trades": int(len(trades) * 0.4)
            },
            "trades": trades,
            "nav_curve": nav_curve,
            "created_at": datetime.datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": result,
            "message": "回测完成"
        }
        
    except Exception as e:
        logger.error(f"回测失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 风险管理API ============
@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    try:
        import datetime
        from decimal import Decimal
        
        # 模拟风险指标数据
        metrics = {
            "portfolio_value": 1250000.0,  # 组合总价值
            "cash": 250000.0,  # 现金
            "market_value": 1000000.0,  # 市值
            "total_pnl": 250000.0,  # 总盈亏
            "daily_pnl": 15000.0,  # 日盈亏
            "var": {  # 风险价值
                "1_day_95": -18000.0,  # 1天95%置信度VaR
                "1_day_99": -25000.0,  # 1天99%置信度VaR
                "10_day_95": -56000.0,  # 10天95%置信度VaR
                "10_day_99": -78000.0   # 10天99%置信度VaR
            },
            "beta": 0.85,  # 贝塔系数
            "alpha": 0.03,  # 阿尔法
            "sharpe_ratio": 1.35,  # 夏普比率
            "max_drawdown": -0.12,  # 最大回撤
            "volatility": 0.18,  # 波动率
            "concentration": {  # 集中度风险
                "top_5_holdings": 0.45,  # 前5大持仓占比
                "single_stock_limit": 0.10,  # 单股票限制
                "sector_concentration": {
                    "金融": 0.30,
                    "科技": 0.25,
                    "消费": 0.20,
                    "其他": 0.25
                }
            },
            "leverage": {  # 杠杆信息
                "total_leverage": 1.2,  # 总杠杆
                "net_leverage": 1.0,  # 净杠杆
                "gross_leverage": 1.4   # 总杠杆
            },
            "liquidity": {  # 流动性指标
                "liquid_ratio": 0.95,  # 流动性比率
                "avg_daily_volume": 50000000,  # 平均日成交量
                "days_to_liquidate": 2.5  # 平仓天数
            },
            "stress_test": {  # 压力测试
                "market_down_10": -125000.0,  # 市场下跌10%
                "market_down_20": -280000.0,  # 市场下跌20%
                "volatility_spike": -95000.0,  # 波动率飙升
                "liquidity_crisis": -150000.0  # 流动性危机
            },
            "updated_at": datetime.datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": metrics
        }
        
    except Exception as e:
        logger.error(f"获取风险指标失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/risk/alerts")
async def get_risk_alerts():
    """获取风险警报"""
    try:
        import datetime
        
        # 模拟风险警报数据
        alerts = [
            {
                "id": f"ALERT_{secrets.token_hex(4)}",
                "type": "concentration",
                "level": "medium",  # low, medium, high, critical
                "title": "持仓集中度警告",
                "message": "单只股票持仓超过10%限制",
                "symbol": "600519",
                "current_value": 0.125,
                "threshold": 0.10,
                "status": "active",
                "created_at": datetime.datetime.now().isoformat(),
                "updated_at": datetime.datetime.now().isoformat()
            },
            {
                "id": f"ALERT_{secrets.token_hex(4)}",
                "type": "var",
                "level": "high",
                "title": "VaR突破警告",
                "message": "1天VaR超过预设阈值",
                "current_value": -28000.0,
                "threshold": -25000.0,
                "status": "active",
                "created_at": (datetime.datetime.now() - datetime.timedelta(hours=2)).isoformat(),
                "updated_at": (datetime.datetime.now() - datetime.timedelta(hours=2)).isoformat()
            },
            {
                "id": f"ALERT_{secrets.token_hex(4)}",
                "type": "drawdown",
                "level": "medium",
                "title": "回撤警告",
                "message": "投资组合回撤达到预警线",
                "current_value": -0.08,
                "threshold": -0.075,
                "status": "resolved",
                "created_at": (datetime.datetime.now() - datetime.timedelta(days=1)).isoformat(),
                "updated_at": (datetime.datetime.now() - datetime.timedelta(hours=6)).isoformat()
            },
            {
                "id": f"ALERT_{secrets.token_hex(4)}",
                "type": "leverage",
                "level": "low",
                "title": "杠杆提醒",
                "message": "账户杠杆接近上限",
                "current_value": 1.8,
                "threshold": 2.0,
                "status": "active",
                "created_at": (datetime.datetime.now() - datetime.timedelta(hours=4)).isoformat(),
                "updated_at": (datetime.datetime.now() - datetime.timedelta(hours=4)).isoformat()
            }
        ]
        
        return {
            "success": True,
            "data": alerts,
            "total": len(alerts),
            "summary": {
                "critical": 0,
                "high": 1,
                "medium": 2,
                "low": 1,
                "active": 3,
                "resolved": 1
            }
        }
        
    except Exception as e:
        logger.error(f"获取风险警报失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 原有交易相关API ============
# 简化导入，避免依赖问题
try:
    from app.schemas.trading import (
        OrderRequest, OrderData, OrderStatus,
        PositionData, AccountData, TradeData
    )
    from app.services.simple_trading_service import simple_trading_service
    TRADING_SERVICE_AVAILABLE = True
except ImportError:
    # 如果导入失败，使用简化版本
    TRADING_SERVICE_AVAILABLE = False
    logger.warning("Trading service not available, using mock data")

# 简单的用户认证
def get_current_user(token: str = None):
    """简单的用户认证"""
    # 默认返回admin用户
    return "admin_user"


@app.post("/api/v1/trading/orders")
async def submit_order(order_request: dict):
    """提交订单"""
    try:
        import datetime
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                # 尝试使用真实的交易服务
                order = await simple_trading_service.submit_order(user_id, order_request)
                return {
                    "success": True,
                    "data": order.dict(),
                    "message": "订单提交成功"
                }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock: {e}")
        
        # 使用模拟数据
        order_id = f"ORDER_{secrets.token_hex(8)}"
        order = {
            "order_id": order_id,
            "user_id": user_id,
            "symbol": order_request.get("symbol", "000001"),
            "side": order_request.get("side", "buy"),
            "type": order_request.get("type", "limit"),
            "quantity": order_request.get("quantity", 100),
            "price": order_request.get("price", 10.0),
            "status": "submitted",
            "filled_quantity": 0,
            "avg_price": 0.0,
            "commission": 0.0,
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": order,
            "message": "订单提交成功"
        }
    except Exception as e:
        logger.error(f"提交订单失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.delete("/api/v1/trading/orders/{order_id}")
async def cancel_order(order_id: str):
    """撤销订单"""
    try:
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                result = await simple_trading_service.cancel_order(user_id, order_id)
                return {
                    "success": True,
                    "message": "订单撤销成功"
                }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock: {e}")
        
        # 模拟撤销成功
        return {
            "success": True,
            "data": {"order_id": order_id, "status": "cancelled"},
            "message": "订单撤销成功"
        }
    except Exception as e:
        logger.error(f"撤销订单失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/trading/orders")
async def get_orders(
    status: Optional[str] = None,
    symbol: Optional[str] = None,
    limit: int = 100
):
    """查询订单"""
    try:
        import datetime
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                status_enum = OrderStatus(status) if status else None
                orders = await simple_trading_service.get_orders(
                    user_id, status=status_enum, symbol=symbol, limit=limit
                )
                return {
                    "success": True,
                    "data": [order.dict() for order in orders],
                    "total": len(orders)
                }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock: {e}")
        
        # 生成模拟订单数据
        mock_orders = []
        for i in range(min(5, limit)):  # 生成5个模拟订单
            order = {
                "order_id": f"ORDER_{secrets.token_hex(4)}",
                "user_id": user_id,
                "symbol": symbol if symbol else f"00000{i+1}",
                "side": "buy" if i % 2 == 0 else "sell",
                "type": "limit",
                "quantity": (i + 1) * 100,
                "price": 10.0 + i * 2.5,
                "status": status if status else ("filled" if i < 2 else "submitted"),
                "filled_quantity": (i + 1) * 100 if i < 2 else 0,
                "avg_price": 10.0 + i * 2.5 if i < 2 else 0.0,
                "commission": (10.0 + i * 2.5) * (i + 1) * 100 * 0.0003 if i < 2 else 0.0,
                "created_at": (datetime.datetime.now() - datetime.timedelta(days=i)).isoformat(),
                "updated_at": (datetime.datetime.now() - datetime.timedelta(days=i)).isoformat()
            }
            mock_orders.append(order)
        
        return {
            "success": True,
            "data": mock_orders,
            "total": len(mock_orders)
        }
    except Exception as e:
        logger.error(f"查询订单失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/trading/positions")
async def get_positions():
    """查询持仓"""
    try:
        import datetime
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                positions = await simple_trading_service.get_positions(user_id)
                return {
                    "success": True,
                    "data": [pos.dict() for pos in positions],
                    "total": len(positions)
                }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock: {e}")
        
        # 生成模拟持仓数据
        positions = [
            {
                "symbol": "000001",
                "name": "平安银行",
                "quantity": 10000,
                "available_quantity": 8000,
                "frozen_quantity": 2000,
                "avg_cost": 12.50,
                "current_price": 13.45,
                "market_value": 134500.0,
                "cost_value": 125000.0,
                "unrealized_pnl": 9500.0,
                "unrealized_pnl_ratio": 0.076,
                "position_ratio": 0.089,  # 占总资产比例
                "side": "long",
                "updated_at": datetime.datetime.now().isoformat()
            },
            {
                "symbol": "600519",
                "name": "贵州茅台",
                "quantity": 500,
                "available_quantity": 500,
                "frozen_quantity": 0,
                "avg_cost": 1680.00,
                "current_price": 1653.00,
                "market_value": 826500.0,
                "cost_value": 840000.0,
                "unrealized_pnl": -13500.0,
                "unrealized_pnl_ratio": -0.016,
                "position_ratio": 0.551,
                "side": "long",
                "updated_at": datetime.datetime.now().isoformat()
            },
            {
                "symbol": "300059",
                "name": "东方财富",
                "quantity": 5000,
                "available_quantity": 5000,
                "frozen_quantity": 0,
                "avg_cost": 24.80,
                "current_price": 26.15,
                "market_value": 130750.0,
                "cost_value": 124000.0,
                "unrealized_pnl": 6750.0,
                "unrealized_pnl_ratio": 0.054,
                "position_ratio": 0.087,
                "side": "long",
                "updated_at": datetime.datetime.now().isoformat()
            }
        ]
        
        return {
            "success": True,
            "data": positions,
            "total": len(positions)
        }
    except Exception as e:
        logger.error(f"查询持仓失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/trading/accounts")
async def get_trading_accounts():
    """获取交易账户列表"""
    try:
        import datetime
        user_id = get_current_user()

        # 模拟多个交易账户
        accounts = [
            {
                "account_id": "MAIN_001",
                "account_name": "主账户",
                "account_type": "股票账户",
                "status": "active",
                "balance": 1000000.00,
                "available": 850000.00,
                "frozen": 150000.00,
                "market_value": 2500000.00,
                "total_assets": 3500000.00,
                "profit_loss": 250000.00,
                "profit_loss_rate": 7.69,
                "created_at": "2024-01-15T09:30:00",
                "last_updated": datetime.datetime.now().isoformat()
            },
            {
                "account_id": "FUTURES_001",
                "account_name": "期货账户",
                "account_type": "期货账户",
                "status": "active",
                "balance": 500000.00,
                "available": 450000.00,
                "frozen": 50000.00,
                "margin": 80000.00,
                "profit_loss": 15000.00,
                "profit_loss_rate": 3.08,
                "created_at": "2024-02-20T14:15:00",
                "last_updated": datetime.datetime.now().isoformat()
            }
        ]

        return {
            "success": True,
            "data": accounts,
            "total": len(accounts)
        }
    except Exception as e:
        logger.error(f"获取交易账户失败: {e}")
        raise HTTPException(status_code=500, detail="获取交易账户失败")

@app.get("/api/v1/trading/account")
async def get_account():
    """查询账户信息（增强版）"""
    try:
        import datetime
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                account = await simple_trading_service.get_account(user_id)
                if account:
                    return {
                        "success": True,
                        "data": account.dict()
                    }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock data: {e}")
        
        # 使用详细的模拟账户数据
        account_data = {
            "user_id": user_id,
            "account_id": f"ACC_{user_id}",
            "account_type": "margin",  # cash, margin, futures
            "status": "active",
            "balance": {
                "total_equity": 1500000.0,  # 总资产
                "cash": 300000.0,  # 现金
                "market_value": 1200000.0,  # 市值
                "available_cash": 250000.0,  # 可用现金
                "frozen_cash": 50000.0,  # 冻结资金
                "margin_used": 100000.0,  # 已用保证金
                "margin_available": 200000.0,  # 可用保证金
                "buying_power": 450000.0,  # 购买力
                "maintenance_margin": 80000.0,  # 维持保证金
                "initial_margin": 100000.0,  # 初始保证金
            },
            "pnl": {
                "total_pnl": 300000.0,  # 总盈亏
                "realized_pnl": 150000.0,  # 已实现盈亏
                "unrealized_pnl": 150000.0,  # 未实现盈亏
                "daily_pnl": 25000.0,  # 日盈亏
                "weekly_pnl": 75000.0,  # 周盈亏
                "monthly_pnl": 180000.0,  # 月盈亏
                "ytd_pnl": 300000.0,  # 年度盈亏
                "total_return": 0.25,  # 总收益率
                "daily_return": 0.017,  # 日收益率
                "annualized_return": 0.28,  # 年化收益率
            },
            "positions": {
                "long_positions": 8,  # 多头持仓数
                "short_positions": 2,  # 空头持仓数
                "total_positions": 10,  # 总持仓数
                "concentrated_positions": 3,  # 集中持仓数（占比>5%）
            },
            "risk": {
                "risk_level": "medium",  # low, medium, high
                "leverage_ratio": 1.2,  # 杠杆比率
                "margin_ratio": 0.067,  # 保证金比率
                "concentration_ratio": 0.25,  # 集中度比率
                "max_single_position": 0.15,  # 最大单一持仓
                "sector_exposure": {
                    "technology": 0.30,
                    "finance": 0.25,
                    "healthcare": 0.20,
                    "consumer": 0.15,
                    "others": 0.10
                }
            },
            "trading_stats": {
                "orders_today": 5,  # 今日订单数
                "trades_today": 3,  # 今日成交数
                "volume_today": 1500000.0,  # 今日成交额
                "commission_today": 150.0,  # 今日佣金
                "orders_this_month": 120,  # 本月订单数
                "trades_this_month": 85,  # 本月成交数
                "success_rate": 0.65,  # 成功率
                "avg_holding_period": 15.5,  # 平均持仓天数
            },
            "fees": {
                "commission_rate": 0.0003,  # 佣金费率
                "management_fee": 0.015,  # 管理费
                "performance_fee": 0.20,  # 绩效费
                "total_commission_ytd": 5400.0,  # 年度总佣金
                "total_fees_ytd": 18000.0,  # 年度总费用
            },
            "limits": {
                "daily_trade_limit": 10000000.0,  # 日交易限额
                "single_order_limit": 1000000.0,  # 单笔订单限额
                "position_limit": 5000000.0,  # 持仓限额
                "leverage_limit": 3.0,  # 杠杆限制
                "margin_call_level": 1.3,  # 追保水平
                "force_close_level": 1.1,  # 强制平仓水平
            },
            "updated_at": datetime.datetime.now().isoformat(),
            "created_at": "2024-01-01T00:00:00"
        }
        
        return {
            "success": True,
            "data": account_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询账户失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/trading/trades")
async def get_trades(
    order_id: Optional[str] = None,
    symbol: Optional[str] = None,
    limit: int = 100
):
    """查询成交记录"""
    try:
        import datetime
        user_id = get_current_user()
        
        if TRADING_SERVICE_AVAILABLE:
            try:
                trades = await simple_trading_service.get_trades(
                    user_id, order_id=order_id, symbol=symbol, limit=limit
                )
                return {
                    "success": True,
                    "data": [trade.dict() for trade in trades],
                    "total": len(trades)
                }
            except Exception as e:
                logger.warning(f"Trading service failed, using mock: {e}")
        
        # 生成模拟成交记录
        trades = []
        for i in range(min(3, limit)):  # 生成3个模拟成交记录
            trade = {
                "trade_id": f"TRADE_{secrets.token_hex(6)}",
                "order_id": order_id if order_id else f"ORDER_{secrets.token_hex(4)}",
                "user_id": user_id,
                "symbol": symbol if symbol else f"00000{i+1}",
                "side": "buy" if i % 2 == 0 else "sell",
                "quantity": (i + 1) * 100,
                "price": 10.0 + i * 2.5,
                "amount": (10.0 + i * 2.5) * (i + 1) * 100,
                "commission": (10.0 + i * 2.5) * (i + 1) * 100 * 0.0003,
                "trade_time": (datetime.datetime.now() - datetime.timedelta(days=i)).isoformat(),
                "settlement_date": (datetime.datetime.now() - datetime.timedelta(days=i-1)).strftime("%Y-%m-%d"),
                "counterparty": f"COUNTER_{i+1}",
                "market": "SZSE" if i % 2 == 0 else "SSE"
            }
            trades.append(trade)
        
        return {
            "success": True,
            "data": trades,
            "total": len(trades)
        }
    except Exception as e:
        logger.error(f"查询成交记录失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 投资组合管理API ============
@app.get("/api/v1/portfolio/overview")
async def get_portfolio_overview():
    """获取投资组合概览"""
    try:
        import datetime
        
        # 模拟投资组合数据
        portfolio_data = {
            "totalAssets": 1500000.0,
            "positionValue": 1200000.0,
            "availableCash": 300000.0,
            "totalChange": 25000.0,
            "totalChangePercent": 0.017,
            "todayPnl": 8500.0,
            "todayPnlPercent": 0.0057,
            "updateTime": datetime.datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": portfolio_data
        }
    except Exception as e:
        logger.error(f"获取投资组合概览失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/portfolio/positions")
async def get_portfolio_positions(
    sortBy: Optional[str] = None,
    order: Optional[str] = "desc"
):
    """获取投资组合持仓"""
    try:
        import datetime
        
        # 模拟持仓数据
        positions = [
            {
                "symbol": "000001",
                "name": "平安银行",
                "quantity": 10000,
                "avgPrice": 12.50,
                "currentPrice": 13.45,
                "marketValue": 134500.0,
                "costValue": 125000.0,
                "pnl": 9500.0,
                "pnlPercent": 0.076,
                "weight": 0.089,
                "changePercent": 0.015,
                "industry": "银行",
                "sector": "金融",
                "lastUpdate": datetime.datetime.now().isoformat()
            },
            {
                "symbol": "600519",
                "name": "贵州茅台",
                "quantity": 500,
                "avgPrice": 1680.00,
                "currentPrice": 1653.00,
                "marketValue": 826500.0,
                "costValue": 840000.0,
                "pnl": -13500.0,
                "pnlPercent": -0.016,
                "weight": 0.551,
                "changePercent": -0.008,
                "industry": "食品饮料",
                "sector": "消费",
                "lastUpdate": datetime.datetime.now().isoformat()
            },
            {
                "symbol": "300059",
                "name": "东方财富",
                "quantity": 5000,
                "avgPrice": 24.80,
                "currentPrice": 26.15,
                "marketValue": 130750.0,
                "costValue": 124000.0,
                "pnl": 6750.0,
                "pnlPercent": 0.054,
                "weight": 0.087,
                "changePercent": 0.022,
                "industry": "互联网金融",
                "sector": "科技",
                "lastUpdate": datetime.datetime.now().isoformat()
            }
        ]
        
        # 排序
        if sortBy:
            reverse = order == "desc"
            positions.sort(key=lambda x: x.get(sortBy, 0), reverse=reverse)
        
        return {
            "success": True,
            "data": positions
        }
    except Exception as e:
        logger.error(f"获取投资组合持仓失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/portfolio/performance")
async def get_portfolio_performance(
    period: str = "1M",
    benchmark: Optional[str] = "000300"
):
    """获取投资组合绩效"""
    try:
        import datetime
        import random
        
        # 根据周期生成数据点数
        period_days = {
            "1D": 1,
            "1W": 7,
            "1M": 30,
            "3M": 90,
            "6M": 180,
            "1Y": 365
        }
        
        days = period_days.get(period, 30)
        
        # 生成绩效数据
        data = []
        base_value = 1000000
        base_nav = 1.0
        
        for i in range(days + 1):
            date = datetime.datetime.now() - datetime.timedelta(days=days-i)
            # 添加一些随机波动
            change = random.uniform(-0.02, 0.025)
            value = base_value * (1 + i * 0.0005 + change)
            nav = base_nav * (1 + i * 0.0005 + change)
            benchmark_value = base_value * (1 + i * 0.0003)  # 基准收益
            
            data.append({
                "date": int(date.timestamp() * 1000),
                "value": round(value, 2),
                "netValue": round(nav, 4),
                "benchmark": round(benchmark_value, 2) if benchmark else None,
                "drawdown": round(min(0, (value - base_value * 1.1) / (base_value * 1.1)), 4)
            })
        
        # 计算统计数据
        total_return = (data[-1]["value"] - data[0]["value"]) / data[0]["value"]
        annualized_return = total_return * (365 / days) if days > 0 else 0
        
        statistics = {
            "totalReturn": round(total_return, 4),
            "annualizedReturn": round(annualized_return, 4),
            "volatility": 0.16,
            "sharpeRatio": 1.35,
            "maxDrawdown": -0.08,
            "winRate": 0.62
        }
        
        return {
            "success": True,
            "data": {
                "period": period,
                "data": data,
                "statistics": statistics
            }
        }
    except Exception as e:
        logger.error(f"获取投资组合绩效失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/portfolio/industry-distribution")
async def get_industry_distribution():
    """获取行业分布"""
    try:
        # 模拟行业分布数据
        distribution = [
            {
                "name": "金融",
                "value": 550000.0,
                "weight": 0.458,
                "count": 3
            },
            {
                "name": "科技",
                "value": 350000.0,
                "weight": 0.292,
                "count": 2
            },
            {
                "name": "消费",
                "value": 200000.0,
                "weight": 0.167,
                "count": 2
            },
            {
                "name": "制造",
                "value": 100000.0,
                "weight": 0.083,
                "count": 1
            }
        ]
        
        return {
            "success": True,
            "data": distribution
        }
    except Exception as e:
        logger.error(f"获取行业分布失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/portfolio/risk-metrics")
async def get_portfolio_risk_metrics():
    """获取投资组合风险指标"""
    try:
        metrics = {
            "volatility": 0.16,
            "sharpeRatio": 1.35,
            "maxDrawdown": -0.08,
            "beta": 0.92,
            "alpha": 0.03,
            "var95": -25000.0,
            "concentrationRisk": {
                "top5": 0.75,
                "largestPosition": 0.30
            }
        }
        
        return {
            "success": True,
            "data": metrics
        }
    except Exception as e:
        logger.error(f"获取投资组合风险指标失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/risk/distribution")
async def get_risk_distribution():
    """获取风险分布"""
    try:
        distribution = [
            {"name": "市场风险", "value": 65, "percentage": 0.65},
            {"name": "信用风险", "value": 15, "percentage": 0.15},
            {"name": "流动性风险", "value": 12, "percentage": 0.12},
            {"name": "操作风险", "value": 8, "percentage": 0.08}
        ]
        
        return {
            "success": True,
            "data": distribution
        }
    except Exception as e:
        logger.error(f"获取风险分布失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.get("/api/v1/risk/limits")
async def get_risk_limits():
    """获取风险限额"""
    try:
        limits = [
            {
                "name": "总风险敞口",
                "current": 2500000,
                "total": 5000000,
                "usage": 0.5,
                "type": "currency"
            },
            {
                "name": "单一股票",
                "current": 0.15,
                "total": 0.20,
                "usage": 0.75,
                "type": "percent"
            },
            {
                "name": "行业集中度",
                "current": 0.35,
                "total": 0.40,
                "usage": 0.875,
                "type": "percent"
            },
            {
                "name": "杠杆使用",
                "current": 1.8,
                "total": 3.0,
                "usage": 0.6,
                "type": "number"
            }
        ]
        
        return {
            "success": True,
            "data": limits
        }
    except Exception as e:
        logger.error(f"获取风险限额失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@app.post("/api/v1/risk/stress-test")
async def run_stress_test(scenarios: Optional[List[str]] = None):
    """运行压力测试"""
    try:
        import datetime
        
        # 默认压力测试场景
        default_scenarios = [
            "market_crash",
            "interest_rate_rise",
            "liquidity_crisis",
            "credit_event"
        ]
        
        test_scenarios = scenarios or default_scenarios
        test_id = f"STRESS_{secrets.token_hex(8)}"
        
        results = {
            "test_id": test_id,
            "timestamp": datetime.datetime.now().isoformat(),
            "scenarios": []
        }
        
        # 模拟每个场景的结果
        scenario_configs = {
            "market_crash": {
                "name": "市场崩盘",
                "description": "主要指数下跌20%",
                "parameters": {"market_change": -0.20},
                "impact": -0.18
            },
            "interest_rate_rise": {
                "name": "利率上升",
                "description": "基准利率上升200bp",
                "parameters": {"interest_rate_change": 0.02},
                "impact": -0.12
            },
            "liquidity_crisis": {
                "name": "流动性危机",
                "description": "市场流动性大幅收紧",
                "parameters": {"liquidity_change": -0.50},
                "impact": -0.15
            },
            "credit_event": {
                "name": "信用事件",
                "description": "重大信用违约事件",
                "parameters": {"credit_spread_change": 0.03},
                "impact": -0.06
            }
        }
        
        for scenario in test_scenarios:
            if scenario in scenario_configs:
                config = scenario_configs[scenario]
                results["scenarios"].append({
                    "id": scenario,
                    "name": config["name"],
                    "description": config["description"],
                    "parameters": config["parameters"],
                    "result": {
                        "portfolio_impact": config["impact"],
                        "var_impact": config["impact"] * 1.5,
                        "expected_loss": abs(config["impact"]) * 1250000,
                        "worst_case_loss": abs(config["impact"]) * 1500000
                    }
                })
        
        return {
            "success": True,
            "data": results
        }
    except Exception as e:
        logger.error(f"运行压力测试失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# ============ 策略系统 CRUD 操作 ============

# 策略数据存储（模拟数据库）
STRATEGIES_DB = {
    "strategy_1": {
        "id": "strategy_1",
        "name": "双均线策略",
        "description": "基于快慢均线交叉的趋势跟踪策略",
        "code": """
def strategy_logic(data):
    fast_ma = data['close'].rolling(5).mean()
    slow_ma = data['close'].rolling(20).mean()
    signal = 1 if fast_ma.iloc[-1] > slow_ma.iloc[-1] else -1
    return signal
        """,
        "parameters": {
            "fast_period": 5,
            "slow_period": 20,
            "max_position": 100000
        },
        "status": "stopped",
        "created_at": "2025-07-26T19:00:00",
        "updated_at": "2025-07-26T19:00:00",
        "performance": {
            "total_return": 0.15,
            "sharpe_ratio": 1.2,
            "max_drawdown": -0.08,
            "win_rate": 0.65
        }
    },
    "strategy_2": {
        "id": "strategy_2", 
        "name": "RSI反转策略",
        "description": "基于RSI超买超卖信号的反转策略",
        "code": """
def strategy_logic(data):
    rsi = calculate_rsi(data['close'], 14)
    if rsi.iloc[-1] < 30:
        return 1  # 买入
    elif rsi.iloc[-1] > 70:
        return -1  # 卖出
    return 0  # 持有
        """,
        "parameters": {
            "rsi_period": 14,
            "oversold_level": 30,
            "overbought_level": 70,
            "max_position": 50000
        },
        "status": "running",
        "created_at": "2025-07-26T18:00:00", 
        "updated_at": "2025-07-26T19:30:00",
        "performance": {
            "total_return": 0.08,
            "sharpe_ratio": 0.9,
            "max_drawdown": -0.12,
            "win_rate": 0.58
        }
    }
}

class StrategyCreateRequest(BaseModel):
    name: str
    description: str = ""
    code: str
    parameters: dict = {}

class StrategyUpdateRequest(BaseModel):
    name: str = None
    description: str = None
    code: str = None
    parameters: dict = None

class StrategyControlRequest(BaseModel):
    action: str  # start, stop, pause

# 获取策略列表
@app.get("/api/v1/strategies")
async def get_strategies(
    page: int = 1,
    limit: int = 20,
    status: str = None
):
    """获取策略列表"""
    try:
        strategies = list(STRATEGIES_DB.values())
        
        # 状态过滤
        if status:
            strategies = [s for s in strategies if s["status"] == status]
        
        # 分页
        total = len(strategies)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        strategies = strategies[start_idx:end_idx]
        
        return {
            "success": True,
            "data": {
                "strategies": strategies,
                "total": total,
                "page": page,
                "limit": limit
            }
        }
    except Exception as e:
        logger.error(f"获取策略列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# 获取单个策略详情
@app.get("/api/v1/strategies/{strategy_id}")
async def get_strategy(strategy_id: str):
    """获取策略详情"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    return {
        "success": True,
        "data": STRATEGIES_DB[strategy_id]
    }

# 创建策略
@app.post("/api/v1/strategies")
async def create_strategy(request: StrategyCreateRequest):
    """创建新策略"""
    try:
        strategy_id = f"strategy_{len(STRATEGIES_DB) + 1}"
        now = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        
        new_strategy = {
            "id": strategy_id,
            "name": request.name,
            "description": request.description,
            "code": request.code,
            "parameters": request.parameters,
            "status": "stopped",
            "created_at": now,
            "updated_at": now,
            "performance": {
                "total_return": 0.0,
                "sharpe_ratio": 0.0,
                "max_drawdown": 0.0,
                "win_rate": 0.0
            }
        }
        
        STRATEGIES_DB[strategy_id] = new_strategy
        
        return {
            "success": True,
            "data": new_strategy,
            "message": "策略创建成功"
        }
    except Exception as e:
        logger.error(f"创建策略失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 更新策略
@app.put("/api/v1/strategies/{strategy_id}")
async def update_strategy(strategy_id: str, request: StrategyUpdateRequest):
    """更新策略"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    try:
        strategy = STRATEGIES_DB[strategy_id]
        
        if request.name is not None:
            strategy["name"] = request.name
        if request.description is not None:
            strategy["description"] = request.description
        if request.code is not None:
            strategy["code"] = request.code
        if request.parameters is not None:
            strategy["parameters"].update(request.parameters)
        
        strategy["updated_at"] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        
        return {
            "success": True,
            "data": strategy,
            "message": "策略更新成功"
        }
    except Exception as e:
        logger.error(f"更新策略失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

# 删除策略
@app.delete("/api/v1/strategies/{strategy_id}")
async def delete_strategy(strategy_id: str):
    """删除策略"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    try:
        # 如果策略在运行，先停止
        if STRATEGIES_DB[strategy_id]["status"] == "running":
            raise HTTPException(status_code=400, detail="请先停止策略再删除")
        
        del STRATEGIES_DB[strategy_id]
        
        return {
            "success": True,
            "message": "策略删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除策略失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============ 策略运行控制 ============

@app.post("/api/v1/strategies/{strategy_id}/control")
async def control_strategy(strategy_id: str, request: StrategyControlRequest):
    """控制策略运行状态"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    try:
        strategy = STRATEGIES_DB[strategy_id]
        action = request.action.lower()
        
        if action == "start":
            if strategy["status"] == "running":
                raise HTTPException(status_code=400, detail="策略已在运行中")
            strategy["status"] = "running"
            message = "策略启动成功"
            
        elif action == "stop":
            if strategy["status"] == "stopped":
                raise HTTPException(status_code=400, detail="策略已停止")
            strategy["status"] = "stopped"
            message = "策略停止成功"
            
        elif action == "pause":
            if strategy["status"] != "running":
                raise HTTPException(status_code=400, detail="只能暂停运行中的策略")
            strategy["status"] = "paused"
            message = "策略暂停成功"
            
        else:
            raise HTTPException(status_code=400, detail="无效的操作类型")
        
        strategy["updated_at"] = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        
        return {
            "success": True,
            "data": strategy,
            "message": message
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"控制策略失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============ 策略绩效分析 ============

@app.get("/api/v1/strategies/{strategy_id}/performance")
async def get_strategy_performance(
    strategy_id: str,
    start_date: str = None,
    end_date: str = None,
    period: str = "1d"
):
    """获取策略绩效分析"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    try:
        strategy = STRATEGIES_DB[strategy_id]
        
        # 生成模拟绩效数据
        performance_data = {
            "strategy_id": strategy_id,
            "strategy_name": strategy["name"],
            "analysis_period": {
                "start_date": start_date or "2025-01-01",
                "end_date": end_date or "2025-07-26",
                "period": period
            },
            "returns": {
                "total_return": strategy["performance"]["total_return"],
                "annual_return": strategy["performance"]["total_return"] * 2,
                "monthly_returns": [0.02, -0.01, 0.03, 0.01, -0.02, 0.04, 0.02],
                "daily_returns": generate_daily_returns(30)
            },
            "risk_metrics": {
                "sharpe_ratio": strategy["performance"]["sharpe_ratio"],
                "sortino_ratio": strategy["performance"]["sharpe_ratio"] * 0.8,
                "max_drawdown": strategy["performance"]["max_drawdown"],
                "volatility": 0.15,
                "beta": 0.8,
                "alpha": 0.03,
                "var_95": -0.025,
                "var_99": -0.04
            },
            "trading_stats": {
                "total_trades": 156,
                "win_rate": strategy["performance"]["win_rate"],
                "average_win": 0.018,
                "average_loss": -0.012,
                "profit_factor": 1.45,
                "max_consecutive_wins": 8,
                "max_consecutive_losses": 4
            },
            "benchmark_comparison": {
                "benchmark": "CSI300",
                "strategy_return": strategy["performance"]["total_return"],
                "benchmark_return": 0.08,
                "excess_return": strategy["performance"]["total_return"] - 0.08,
                "information_ratio": 0.6,
                "tracking_error": 0.12
            }
        }
        
        return {
            "success": True,
            "data": performance_data
        }
    except Exception as e:
        logger.error(f"获取策略绩效失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def generate_daily_returns(days: int):
    """生成模拟日收益率数据"""
    import random
    returns = []
    for _ in range(days):
        returns.append(round(random.gauss(0.001, 0.02), 4))
    return returns

# ============ 参数优化功能 ============

class OptimizationRequest(BaseModel):
    strategy_id: str
    parameters: dict  # 参数范围，如 {"fast_period": [5, 10, 15], "slow_period": [20, 30, 40]}
    optimization_target: str = "sharpe_ratio"  # sharpe_ratio, total_return, max_drawdown
    method: str = "grid_search"  # grid_search, genetic_algorithm

@app.post("/api/v1/strategies/{strategy_id}/optimize")
async def optimize_strategy_parameters(strategy_id: str, request: OptimizationRequest):
    """优化策略参数"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    try:
        strategy = STRATEGIES_DB[strategy_id]
        
        # 模拟参数优化过程
        optimization_results = {
            "strategy_id": strategy_id,
            "optimization_config": {
                "target": request.optimization_target,
                "method": request.method,
                "parameter_ranges": request.parameters
            },
            "results": {
                "best_parameters": generate_best_parameters(request.parameters),
                "best_score": 1.45,
                "total_combinations": calculate_combinations(request.parameters),
                "optimization_time": "02:15:30",
                "convergence": True
            },
            "performance_comparison": {
                "original": {
                    "parameters": strategy["parameters"],
                    "sharpe_ratio": strategy["performance"]["sharpe_ratio"],
                    "total_return": strategy["performance"]["total_return"],
                    "max_drawdown": strategy["performance"]["max_drawdown"]
                },
                "optimized": {
                    "parameters": generate_best_parameters(request.parameters),
                    "sharpe_ratio": 1.45,
                    "total_return": 0.22,
                    "max_drawdown": -0.06
                }
            },
            "parameter_sensitivity": generate_sensitivity_analysis(request.parameters),
            "optimization_history": generate_optimization_history(20)
        }
        
        return {
            "success": True,
            "data": optimization_results,
            "message": "参数优化完成"
        }
    except Exception as e:
        logger.error(f"参数优化失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def generate_best_parameters(param_ranges: dict):
    """生成最优参数"""
    import random
    best_params = {}
    for param, values in param_ranges.items():
        if isinstance(values, list):
            best_params[param] = random.choice(values)
        elif isinstance(values, dict) and "min" in values and "max" in values:
            best_params[param] = random.randint(values["min"], values["max"])
    return best_params

def calculate_combinations(param_ranges: dict):
    """计算参数组合数"""
    total = 1
    for values in param_ranges.values():
        if isinstance(values, list):
            total *= len(values)
        elif isinstance(values, dict) and "min" in values and "max" in values:
            total *= (values["max"] - values["min"] + 1)
    return total

def generate_sensitivity_analysis(param_ranges: dict):
    """生成敏感性分析数据"""
    import random
    sensitivity = {}
    for param in param_ranges.keys():
        sensitivity[param] = {
            "sensitivity_score": round(random.uniform(0.1, 1.0), 2),
            "impact_direction": random.choice(["positive", "negative"]),
            "confidence": round(random.uniform(0.7, 0.95), 2)
        }
    return sensitivity

def generate_optimization_history(steps: int):
    """生成优化历史数据"""
    import random
    history = []
    base_score = 0.8
    for i in range(steps):
        base_score += random.uniform(-0.05, 0.1)
        history.append({
            "iteration": i + 1,
            "score": round(base_score, 3),
            "parameters": {"param_" + str(j): random.randint(5, 50) for j in range(3)}
        })
    return history

# ============ 风险报告生成 ============

@app.get("/api/v1/risk/report")
async def generate_risk_report(
    portfolio_id: str = "default",
    report_type: str = "comprehensive",  # comprehensive, summary, detailed
    start_date: str = None,
    end_date: str = None
):
    """生成风险报告"""
    try:
        report_data = {
            "report_meta": {
                "id": f"risk_report_{int(time.time())}",
                "type": report_type,
                "portfolio_id": portfolio_id,
                "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "period": {
                    "start_date": start_date or "2025-01-01",
                    "end_date": end_date or "2025-07-26"
                }
            },
            "executive_summary": {
                "overall_risk_level": "中等",
                "key_risks": [
                    "市场风险：当前持仓集中度较高",
                    "流动性风险：部分小盘股持仓比例偏大",
                    "信用风险：债券组合信用等级良好"
                ],
                "recommendations": [
                    "建议适当分散持仓，降低集中度风险",
                    "增加大盘蓝筹股配置比例",
                    "建立动态止损机制"
                ]
            },
            "portfolio_overview": {
                "total_value": 12500000.0,
                "asset_allocation": {
                    "stocks": 0.65,
                    "bonds": 0.25,
                    "cash": 0.10
                },
                "sector_allocation": {
                    "technology": 0.25,
                    "finance": 0.20,
                    "healthcare": 0.15,
                    "consumer": 0.15,
                    "others": 0.25
                },
                "top_holdings": [
                    {"symbol": "000001.SZ", "name": "平安银行", "weight": 0.08, "value": 1000000},
                    {"symbol": "000002.SZ", "name": "万科A", "weight": 0.06, "value": 750000},
                    {"symbol": "600036.SH", "name": "招商银行", "weight": 0.05, "value": 625000}
                ]
            },
            "risk_metrics": {
                "value_at_risk": {
                    "var_1d_95": -125000,  # 1日95%VaR
                    "var_1d_99": -187500,  # 1日99%VaR
                    "var_10d_95": -395000, # 10日95%VaR
                    "cvar_1d_95": -156250  # 条件VaR
                },
                "sensitivity_analysis": {
                    "delta": 0.65,  # 价格敏感性
                    "gamma": 0.02,  # 二阶价格敏感性
                    "vega": 0.15,   # 波动率敏感性
                    "theta": -50,   # 时间衰减
                    "rho": 0.08     # 利率敏感性
                },
                "concentration_risk": {
                    "herfindahl_index": 0.12,
                    "max_weight": 0.08,
                    "top10_concentration": 0.45,
                    "sector_concentration": 0.25
                },
                "liquidity_risk": {
                    "avg_daily_volume": 5000000,
                    "liquidity_score": 0.85,
                    "days_to_liquidate": 3.2,
                    "bid_ask_spread": 0.0015
                }
            },
            "stress_testing": {
                "scenarios": [
                    {
                        "name": "2008金融危机重演",
                        "description": "模拟2008年金融危机情况",
                        "portfolio_impact": -0.35,
                        "estimated_loss": 4375000
                    },
                    {
                        "name": "新冠疫情冲击",
                        "description": "模拟新冠疫情市场冲击",
                        "portfolio_impact": -0.25,
                        "estimated_loss": 3125000
                    },
                    {
                        "name": "利率大幅上升",
                        "description": "央行激进加息情景",
                        "portfolio_impact": -0.15,
                        "estimated_loss": 1875000
                    }
                ]
            },
            "compliance_check": {
                "position_limits": {
                    "single_stock_limit": {"limit": 0.10, "current_max": 0.08, "status": "合规"},
                    "sector_limit": {"limit": 0.30, "current_max": 0.25, "status": "合规"},
                    "concentration_limit": {"limit": 0.50, "current": 0.45, "status": "合规"}
                },
                "regulatory_requirements": {
                    "capital_adequacy": {"required": 0.08, "current": 0.12, "status": "合规"},
                    "liquidity_ratio": {"required": 0.20, "current": 0.25, "status": "合规"}
                }
            },
            "attribution_analysis": {
                "return_attribution": {
                    "asset_allocation": 0.002,
                    "security_selection": 0.008,
                    "interaction": 0.001,
                    "total_excess_return": 0.011
                },
                "risk_attribution": {
                    "systematic_risk": 0.75,
                    "specific_risk": 0.25,
                    "sector_risk": 0.40,
                    "style_risk": 0.35
                }
            }
        }
        
        return {
            "success": True,
            "data": report_data
        }
    except Exception as e:
        logger.error(f"生成风险报告失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============ 实时风控规则引擎 ============

# 风控规则存储
RISK_RULES = {
    "position_limit": {
        "id": "position_limit",
        "name": "持仓限制规则",
        "description": "单只股票持仓不得超过总资产的10%",
        "type": "position",
        "parameters": {"max_weight": 0.10},
        "enabled": True,
        "severity": "high"
    },
    "concentration_limit": {
        "id": "concentration_limit", 
        "name": "集中度控制规则",
        "description": "前十大持仓不得超过总资产的50%",
        "type": "concentration",
        "parameters": {"max_concentration": 0.50},
        "enabled": True,
        "severity": "medium"
    },
    "var_limit": {
        "id": "var_limit",
        "name": "VaR限制规则", 
        "description": "日VaR不得超过总资产的2%",
        "type": "risk",
        "parameters": {"max_var": 0.02},
        "enabled": True,
        "severity": "high"
    },
    "sector_limit": {
        "id": "sector_limit",
        "name": "行业集中度规则",
        "description": "单一行业持仓不得超过30%",
        "type": "sector",
        "parameters": {"max_sector_weight": 0.30},
        "enabled": True,
        "severity": "medium"
    }
}

class RiskRuleRequest(BaseModel):
    name: str
    description: str
    type: str
    parameters: dict
    enabled: bool = True
    severity: str = "medium"

@app.get("/api/v1/risk/rules")
async def get_risk_rules():
    """获取风控规则列表"""
    try:
        rules = list(RISK_RULES.values())
        return {
            "success": True,
            "data": {
                "rules": rules,
                "total": len(rules)
            }
        }
    except Exception as e:
        logger.error(f"获取风控规则失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/risk/rules")
async def create_risk_rule(request: RiskRuleRequest):
    """创建风控规则"""
    try:
        rule_id = f"rule_{len(RISK_RULES) + 1}"
        new_rule = {
            "id": rule_id,
            "name": request.name,
            "description": request.description,
            "type": request.type,
            "parameters": request.parameters,
            "enabled": request.enabled,
            "severity": request.severity
        }
        
        RISK_RULES[rule_id] = new_rule
        
        return {
            "success": True,
            "data": new_rule,
            "message": "风控规则创建成功"
        }
    except Exception as e:
        logger.error(f"创建风控规则失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/risk/check")
async def check_risk_compliance(portfolio_data: dict = None):
    """实时风控检查"""
    try:
        # 模拟投资组合数据
        if not portfolio_data:
            portfolio_data = {
                "total_value": 10000000,
                "positions": [
                    {"symbol": "000001.SZ", "value": 800000, "weight": 0.08},
                    {"symbol": "000002.SZ", "value": 600000, "weight": 0.06},
                    {"symbol": "600036.SH", "value": 500000, "weight": 0.05}
                ],
                "sector_allocation": {
                    "finance": 0.30,
                    "technology": 0.25,
                    "healthcare": 0.20
                },
                "var_1d": 150000
            }
        
        violations = []
        warnings = []
        
        # 检查每个启用的规则
        for rule_id, rule in RISK_RULES.items():
            if not rule["enabled"]:
                continue
                
            violation = check_rule_compliance(rule, portfolio_data)
            if violation:
                if rule["severity"] == "high":
                    violations.append(violation)
                else:
                    warnings.append(violation)
        
        # 计算风险评分
        risk_score = calculate_risk_score(violations, warnings)
        
        result = {
            "compliance_status": "违规" if violations else ("警告" if warnings else "合规"),
            "risk_score": risk_score,
            "violations": violations,
            "warnings": warnings,
            "checked_rules": len([r for r in RISK_RULES.values() if r["enabled"]]),
            "check_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return {
            "success": True,
            "data": result
        }
    except Exception as e:
        logger.error(f"风控检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def check_rule_compliance(rule: dict, portfolio_data: dict):
    """检查单个规则合规性"""
    rule_type = rule["type"]
    
    if rule_type == "position":
        max_weight = rule["parameters"]["max_weight"]
        for position in portfolio_data["positions"]:
            if position["weight"] > max_weight:
                return {
                    "rule_id": rule["id"],
                    "rule_name": rule["name"],
                    "violation_type": "持仓超限",
                    "details": f"股票{position['symbol']}持仓比例{position['weight']:.2%}超过限制{max_weight:.2%}",
                    "severity": rule["severity"]
                }
    
    elif rule_type == "concentration":
        max_concentration = rule["parameters"]["max_concentration"]
        top_positions = sorted(portfolio_data["positions"], key=lambda x: x["weight"], reverse=True)[:10]
        total_concentration = sum(p["weight"] for p in top_positions)
        if total_concentration > max_concentration:
            return {
                "rule_id": rule["id"],
                "rule_name": rule["name"],
                "violation_type": "集中度超限",
                "details": f"前十大持仓集中度{total_concentration:.2%}超过限制{max_concentration:.2%}",
                "severity": rule["severity"]
            }
    
    elif rule_type == "risk":
        max_var = rule["parameters"]["max_var"]
        current_var_ratio = portfolio_data["var_1d"] / portfolio_data["total_value"]
        if current_var_ratio > max_var:
            return {
                "rule_id": rule["id"],
                "rule_name": rule["name"],
                "violation_type": "VaR超限",
                "details": f"当前VaR比例{current_var_ratio:.2%}超过限制{max_var:.2%}",
                "severity": rule["severity"]
            }
    
    elif rule_type == "sector":
        max_sector_weight = rule["parameters"]["max_sector_weight"]
        for sector, weight in portfolio_data["sector_allocation"].items():
            if weight > max_sector_weight:
                return {
                    "rule_id": rule["id"],
                    "rule_name": rule["name"],
                    "violation_type": "行业集中度超限",
                    "details": f"{sector}行业持仓比例{weight:.2%}超过限制{max_sector_weight:.2%}",
                    "severity": rule["severity"]
                }
    
    return None

def calculate_risk_score(violations: list, warnings: list):
    """计算风险评分（0-100，分数越高风险越大）"""
    base_score = 0
    
    # 违规项目扣分
    for violation in violations:
        if violation["severity"] == "high":
            base_score += 30
        elif violation["severity"] == "medium":
            base_score += 20
        else:
            base_score += 10
    
    # 警告项目扣分
    for warning in warnings:
        if warning["severity"] == "high":
            base_score += 15
        elif warning["severity"] == "medium":
            base_score += 10
        else:
            base_score += 5
    
    return min(base_score, 100)

# ============ 模拟数据库和缓存 ============

# 模拟Redis缓存
REDIS_CACHE = {}

@app.get("/api/v1/cache/status")
async def get_cache_status():
    """获取缓存状态"""
    return {
        "success": True,
        "data": {
            "type": "memory_cache",
            "status": "active",
            "keys_count": len(REDIS_CACHE),
            "memory_usage": f"{len(str(REDIS_CACHE))} bytes",
            "hit_rate": 0.85,
            "miss_rate": 0.15
        }
    }

@app.post("/api/v1/cache/set")
async def set_cache(key: str, value: str, ttl: int = 300):
    """设置缓存"""
    try:
        REDIS_CACHE[key] = {
            "value": value,
            "created_at": time.time(),
            "ttl": ttl
        }
        return {
            "success": True,
            "message": f"缓存键 {key} 设置成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/cache/get/{key}")
async def get_cache(key: str):
    """获取缓存"""
    try:
        if key not in REDIS_CACHE:
            raise HTTPException(status_code=404, detail="缓存键不存在")
        
        cache_item = REDIS_CACHE[key]
        
        # 检查是否过期
        if time.time() - cache_item["created_at"] > cache_item["ttl"]:
            del REDIS_CACHE[key]
            raise HTTPException(status_code=404, detail="缓存已过期")
        
        return {
            "success": True,
            "data": cache_item["value"]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ============ 模拟Celery任务队列 ============

# 任务存储
TASK_QUEUE = {}

class TaskRequest(BaseModel):
    task_type: str
    parameters: dict = {}
    priority: str = "normal"  # high, normal, low

@app.post("/api/v1/tasks/submit")
async def submit_task(request: TaskRequest):
    """提交异步任务"""
    try:
        task_id = f"task_{int(time.time())}_{len(TASK_QUEUE)}"
        
        task = {
            "id": task_id,
            "type": request.task_type,
            "parameters": request.parameters,
            "priority": request.priority,
            "status": "pending",
            "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "started_at": None,
            "completed_at": None,
            "result": None,
            "error": None
        }
        
        TASK_QUEUE[task_id] = task
        
        # 模拟任务执行
        if request.task_type == "backtest":
            task["status"] = "running"
            task["started_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return {
            "success": True,
            "data": {
                "task_id": task_id,
                "status": task["status"]
            },
            "message": "任务提交成功"
        }
    except Exception as e:
        logger.error(f"提交任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/tasks/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in TASK_QUEUE:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = TASK_QUEUE[task_id]
    
    # 模拟任务完成
    if task["status"] == "running":
        import random
        if random.random() > 0.5:  # 50%概率完成
            task["status"] = "completed"
            task["completed_at"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            task["result"] = {
                "total_return": 0.15,
                "sharpe_ratio": 1.2,
                "max_drawdown": -0.08
            }
    
    return {
        "success": True,
        "data": task
    }

@app.get("/api/v1/tasks")
async def get_tasks(
    status: str = None,
    task_type: str = None,
    limit: int = 20
):
    """获取任务列表"""
    try:
        tasks = list(TASK_QUEUE.values())
        
        # 过滤
        if status:
            tasks = [t for t in tasks if t["status"] == status]
        if task_type:
            tasks = [t for t in tasks if t["type"] == task_type]
        
        # 限制数量
        tasks = tasks[-limit:]
        
        return {
            "success": True,
            "data": {
                "tasks": tasks,
                "total": len(tasks)
            }
        }
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============ WebSocket端点 ============
# WebSocket连接管理器
class WebSocketManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, str] = {}  # user_id -> client_id

    async def connect(self, websocket: WebSocket, client_id: str, user_id: str = None):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        if user_id:
            self.user_connections[user_id] = client_id
        logger.info(f"WebSocket连接建立: {client_id}")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        # 清理用户连接映射
        for user_id, cid in list(self.user_connections.items()):
            if cid == client_id:
                del self.user_connections[user_id]
                break
        logger.info(f"WebSocket连接断开: {client_id}")

    async def send_personal_message(self, message: dict, client_id: str):
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            try:
                await websocket.send_json(message)
            except:
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        disconnected = []
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_json(message)
            except:
                disconnected.append(client_id)

        # 清理断开的连接
        for client_id in disconnected:
            self.disconnect(client_id)

# 全局WebSocket管理器
ws_manager = WebSocketManager()

@app.websocket("/api/v1/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket主端点"""
    client_id = None
    user_id = None

    try:
        # 从查询参数获取token
        token = websocket.query_params.get("token")

        # 验证token
        if token == "dev-token-for-testing":
            user_id = "dev_user"
            logger.info("WebSocket开发认证成功")
        elif token:
            # 这里可以添加真实的token验证逻辑
            logger.info(f"WebSocket token验证: {token[:10]}...")
            user_id = "authenticated_user"
        else:
            logger.info("WebSocket匿名连接")
            user_id = "anonymous"

        # 生成客户端ID
        import uuid
        client_id = str(uuid.uuid4())

        # 建立连接
        await ws_manager.connect(websocket, client_id, user_id)

        # 发送连接确认
        await ws_manager.send_personal_message({
            "type": "connection",
            "status": "connected",
            "client_id": client_id,
            "user_id": user_id,
            "message": "WebSocket连接成功",
            "timestamp": datetime.now().isoformat()
        }, client_id)

        # 消息循环
        while True:
            try:
                data = await websocket.receive_text()
                message = json.loads(data)
                message_type = message.get("type")

                logger.info(f"收到WebSocket消息: {message_type}")

                # 处理不同类型的消息
                if message_type == "ping":
                    await ws_manager.send_personal_message({
                        "type": "pong",
                        "timestamp": datetime.now().isoformat()
                    }, client_id)

                elif message_type == "subscribe":
                    channel = message.get("channel")
                    await ws_manager.send_personal_message({
                        "type": "subscription_confirmed",
                        "channel": channel,
                        "message": f"已订阅频道: {channel}"
                    }, client_id)

                elif message_type == "market_data_request":
                    # 发送模拟市场数据
                    import random
                    await ws_manager.send_personal_message({
                        "type": "market_data",
                        "data": {
                            "symbol": message.get("symbol", "000001"),
                            "price": round(random.uniform(10, 100), 2),
                            "change": round(random.uniform(-5, 5), 2),
                            "volume": random.randint(1000, 100000),
                            "timestamp": datetime.now().isoformat()
                        }
                    }, client_id)

                else:
                    await ws_manager.send_personal_message({
                        "type": "echo",
                        "original_message": message,
                        "timestamp": datetime.now().isoformat()
                    }, client_id)

            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await ws_manager.send_personal_message({
                    "type": "error",
                    "message": "无效的JSON格式"
                }, client_id)
            except Exception as e:
                logger.error(f"WebSocket消息处理错误: {e}")
                await ws_manager.send_personal_message({
                    "type": "error",
                    "message": f"消息处理错误: {str(e)}"
                }, client_id)

    except Exception as e:
        logger.error(f"WebSocket连接错误: {e}")
    finally:
        if client_id:
            ws_manager.disconnect(client_id)

# ================================
# CTP交易API
# ================================

# CTP交易相关的数据模型
class CTOrderRequest(BaseModel):
    symbol: str
    direction: str  # "BUY" or "SELL"
    order_type: str = "LIMIT"  # "LIMIT" or "MARKET"
    volume: int
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"

# 模拟CTP服务状态
ctp_status = {
    "connected": False,
    "logged_in": False,
    "broker_id": "9999",
    "user_id": "",
    "simulation_mode": True
}

# 模拟订单存储
ctp_orders = {}
ctp_positions = {}
ctp_account = {
    "account_id": "simnow_test",
    "balance": 1000000.0,
    "available": 1000000.0,
    "margin": 0.0,
    "commission": 0.0,
    "pnl": 0.0,
    "total_asset": 1000000.0
}

@app.post("/api/v1/ctp/initialize")
async def initialize_ctp():
    """初始化CTP连接"""
    try:
        # 模拟初始化过程
        await asyncio.sleep(1)

        ctp_status["connected"] = True
        ctp_status["logged_in"] = True
        ctp_status["user_id"] = "simnow_test"

        logger.info("CTP连接初始化成功")

        return {
            "success": True,
            "message": "CTP初始化成功",
            "data": ctp_status
        }
    except Exception as e:
        logger.error(f"CTP初始化失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"初始化失败: {str(e)}"}
        )

@app.get("/api/v1/ctp/status")
async def get_ctp_status():
    """获取CTP连接状态"""
    return {
        "success": True,
        "data": ctp_status
    }

@app.post("/api/v1/ctp/orders")
async def submit_ctp_order(order: CTOrderRequest):
    """提交CTP订单"""
    try:
        if not ctp_status["connected"]:
            raise HTTPException(status_code=400, detail="CTP未连接")

        # 生成订单ID
        import time
        order_id = f"CTP{int(time.time())}{len(ctp_orders):04d}"

        # 风险检查
        if order.price and order.volume:
            order_amount = order.price * order.volume
            if order.direction == "BUY" and order_amount > ctp_account["available"]:
                raise HTTPException(status_code=400, detail="可用资金不足")

        # 创建订单
        order_data = {
            "order_id": order_id,
            "symbol": order.symbol,
            "direction": order.direction,
            "order_type": order.order_type,
            "volume": order.volume,
            "price": order.price,
            "status": "SUBMITTED",
            "filled_volume": 0,
            "avg_price": 0.0,
            "create_time": datetime.now().isoformat(),
            "update_time": datetime.now().isoformat()
        }

        ctp_orders[order_id] = order_data

        # 模拟订单执行
        asyncio.create_task(simulate_order_execution(order_id))

        logger.info(f"CTP订单提交成功: {order_id}")

        return {
            "success": True,
            "message": "订单提交成功",
            "order_id": order_id,
            "data": order_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CTP订单提交失败: {e}")
        raise HTTPException(status_code=500, detail=f"订单提交失败: {str(e)}")

async def simulate_order_execution(order_id: str):
    """模拟订单执行"""
    try:
        await asyncio.sleep(2)  # 模拟执行延迟

        if order_id not in ctp_orders:
            return

        order = ctp_orders[order_id]
        if order["status"] != "SUBMITTED":
            return

        # 模拟成交
        trade_price = order["price"] or 3500.0
        trade_volume = order["volume"]

        # 更新订单状态
        order["status"] = "FILLED"
        order["filled_volume"] = trade_volume
        order["avg_price"] = trade_price
        order["update_time"] = datetime.now().isoformat()

        # 更新账户
        trade_amount = trade_price * trade_volume
        commission = trade_amount * 0.0003

        if order["direction"] == "BUY":
            ctp_account["available"] -= (trade_amount + commission)
        else:
            ctp_account["available"] += (trade_amount - commission)

        ctp_account["commission"] += commission

        # 更新持仓
        position_key = f"{order['symbol']}_{order['direction']}"
        if position_key in ctp_positions:
            pos = ctp_positions[position_key]
            total_volume = pos["volume"] + trade_volume
            total_cost = pos["avg_price"] * pos["volume"] + trade_price * trade_volume
            pos["avg_price"] = total_cost / total_volume
            pos["volume"] = total_volume
            pos["market_value"] = total_volume * trade_price
        else:
            ctp_positions[position_key] = {
                "symbol": order["symbol"],
                "direction": order["direction"],
                "volume": trade_volume,
                "avg_price": trade_price,
                "market_value": trade_volume * trade_price,
                "pnl": 0.0,
                "pnl_ratio": 0.0
            }

        # 通过WebSocket推送更新
        await ws_manager.broadcast({
            "type": "ctp_order_update",
            "data": order
        })

        await ws_manager.broadcast({
            "type": "ctp_account_update",
            "data": ctp_account
        })

        logger.info(f"CTP订单执行完成: {order_id}")

    except Exception as e:
        logger.error(f"CTP订单执行模拟失败: {e}")

@app.delete("/api/v1/ctp/orders/{order_id}")
async def cancel_ctp_order(order_id: str):
    """撤销CTP订单"""
    try:
        if order_id not in ctp_orders:
            raise HTTPException(status_code=404, detail="订单不存在")

        order = ctp_orders[order_id]

        if order["status"] in ["FILLED", "CANCELLED"]:
            raise HTTPException(status_code=400, detail="订单已完成或已撤销")

        order["status"] = "CANCELLED"
        order["update_time"] = datetime.now().isoformat()

        # 推送更新
        await ws_manager.broadcast({
            "type": "ctp_order_update",
            "data": order
        })

        logger.info(f"CTP订单撤销成功: {order_id}")

        return {
            "success": True,
            "message": "订单撤销成功",
            "order_id": order_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CTP撤单失败: {e}")
        raise HTTPException(status_code=500, detail=f"撤单失败: {str(e)}")

@app.get("/api/v1/ctp/orders")
async def query_ctp_orders():
    """查询CTP订单"""
    try:
        orders = list(ctp_orders.values())
        return {
            "success": True,
            "data": orders,
            "total": len(orders)
        }
    except Exception as e:
        logger.error(f"CTP订单查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.get("/api/v1/ctp/positions")
async def query_ctp_positions():
    """查询CTP持仓"""
    try:
        positions = list(ctp_positions.values())
        return {
            "success": True,
            "data": positions,
            "total": len(positions)
        }
    except Exception as e:
        logger.error(f"CTP持仓查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.get("/api/v1/ctp/account")
async def query_ctp_account():
    """查询CTP账户"""
    try:
        return {
            "success": True,
            "data": ctp_account
        }
    except Exception as e:
        logger.error(f"CTP账户查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# ================================
# 风险控制API
# ================================

@app.get("/api/v1/risk/report")
async def get_risk_report():
    """获取风险报告"""
    try:
        # 模拟风险报告数据
        risk_report = {
            "timestamp": datetime.now().isoformat(),
            "risk_status": "normal",
            "risk_limits": {
                "max_single_order_amount": 100000.0,
                "max_daily_trade_amount": 1000000.0,
                "max_position_ratio": 0.8,
                "max_daily_loss": 50000.0
            },
            "current_stats": {
                "daily_trade_count": len(ctp_orders),
                "daily_trade_amount": sum(
                    order.get("price", 0) * order.get("volume", 0)
                    for order in ctp_orders.values()
                ),
                "current_pnl": ctp_account.get("pnl", 0),
                "position_count": len(ctp_positions),
                "available_ratio": ctp_account.get("available", 0) / ctp_account.get("balance", 1)
            },
            "risk_alerts": [
                {
                    "id": 1,
                    "level": "warning",
                    "type": "position",
                    "message": "单一品种持仓比例较高",
                    "timestamp": datetime.now().isoformat()
                }
            ],
            "suggestions": [
                "建议分散持仓，降低单一品种风险",
                "关注市场波动，适当控制仓位"
            ]
        }

        return {
            "success": True,
            "data": risk_report
        }
    except Exception as e:
        logger.error(f"获取风险报告失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@app.get("/api/v1/risk/limits")
async def get_risk_limits():
    """获取风险限制配置"""
    try:
        risk_limits = {
            "capital_limits": {
                "max_single_order_amount": 100000.0,
                "max_daily_trade_amount": 1000000.0,
                "max_position_ratio": 0.8,
                "min_available_ratio": 0.2
            },
            "position_limits": {
                "max_single_position_ratio": 0.3,
                "max_sector_concentration": 0.5,
                "max_leverage_ratio": 3.0
            },
            "frequency_limits": {
                "max_orders_per_minute": 60,
                "max_orders_per_hour": 1000,
                "max_orders_per_day": 10000
            },
            "loss_limits": {
                "max_daily_loss": 50000.0,
                "max_weekly_loss": 200000.0,
                "max_monthly_loss": 500000.0,
                "max_drawdown_ratio": 0.15
            }
        }

        return {
            "success": True,
            "data": risk_limits
        }
    except Exception as e:
        logger.error(f"获取风险限制失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@app.post("/api/v1/risk/limits")
async def update_risk_limits(limits: dict):
    """更新风险限制配置"""
    try:
        # 在实际应用中，这里会更新数据库中的风险限制配置
        logger.info(f"风险限制配置更新: {limits}")

        return {
            "success": True,
            "message": "风险限制配置更新成功",
            "data": limits
        }
    except Exception as e:
        logger.error(f"更新风险限制失败: {e}")
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")

@app.get("/api/v1/risk/alerts")
async def get_risk_alerts():
    """获取风险告警"""
    try:
        # 模拟风险告警数据
        alerts = [
            {
                "id": 1,
                "level": "warning",
                "type": "position",
                "message": "rb2501持仓比例达到25%，接近30%限制",
                "timestamp": datetime.now().isoformat(),
                "resolved": False
            },
            {
                "id": 2,
                "level": "info",
                "type": "frequency",
                "message": "1分钟内提交订单45笔，接近60笔限制",
                "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
                "resolved": True
            }
        ]

        return {
            "success": True,
            "data": alerts,
            "total": len(alerts)
        }
    except Exception as e:
        logger.error(f"获取风险告警失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取失败: {str(e)}")

@app.post("/api/v1/risk/check")
async def check_order_risk(order_data: dict):
    """检查订单风险"""
    try:
        # 模拟风险检查
        order_amount = order_data.get("price", 0) * order_data.get("volume", 0)
        available_funds = ctp_account.get("available", 0)

        # 基础风险检查
        risk_checks = []

        # 资金检查
        if order_data.get("direction") == "BUY" and order_amount > available_funds:
            risk_checks.append({
                "type": "capital",
                "level": "critical",
                "passed": False,
                "message": f"可用资金不足，需要{order_amount:.2f}，可用{available_funds:.2f}"
            })
        else:
            risk_checks.append({
                "type": "capital",
                "level": "low",
                "passed": True,
                "message": "资金检查通过"
            })

        # 单笔限额检查
        max_single_amount = 100000.0
        if order_amount > max_single_amount:
            risk_checks.append({
                "type": "amount",
                "level": "high",
                "passed": False,
                "message": f"单笔金额{order_amount:.2f}超过限额{max_single_amount:.2f}"
            })
        else:
            risk_checks.append({
                "type": "amount",
                "level": "low",
                "passed": True,
                "message": "金额检查通过"
            })

        # 综合结果
        all_passed = all(check["passed"] for check in risk_checks)
        highest_level = max(
            (check["level"] for check in risk_checks),
            key=lambda x: {"low": 1, "medium": 2, "high": 3, "critical": 4}.get(x, 0)
        )

        return {
            "success": True,
            "data": {
                "passed": all_passed,
                "risk_level": highest_level,
                "checks": risk_checks,
                "suggestions": [
                    "建议分散投资降低风险",
                    "关注市场动态调整策略"
                ] if not all_passed else []
            }
        }
    except Exception as e:
        logger.error(f"风险检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"检查失败: {str(e)}")

# ============ 用户管理API ============

@app.get("/api/v1/users/profile", tags=["用户管理"])
async def get_user_profile():
    """获取用户资料"""
    return {
        "success": True,
        "data": {
            "id": "user_001",
            "username": "demo_user",
            "email": "<EMAIL>",
            "nickname": "演示用户",
            "avatar": "/avatars/default.png",
            "phone": "138****0000",
            "role": "user",
            "vip_level": 1,
            "created_at": "2024-01-01T00:00:00Z",
            "last_login": "2025-08-02T10:00:00Z"
        }
    }

@app.put("/api/v1/users/profile", tags=["用户管理"])
async def update_user_profile(request: Request):
    """更新用户资料"""
    data = await request.json()
    return {
        "success": True,
        "data": {
            "id": "user_001",
            "username": "demo_user",
            "nickname": data.get("nickname", "演示用户"),
            "email": data.get("email", "<EMAIL>"),
            "phone": data.get("phone", "138****0000")
        },
        "message": "用户资料更新成功"
    }

@app.get("/api/v1/users/settings", tags=["用户管理"])
async def get_user_settings():
    """获取用户设置"""
    return {
        "success": True,
        "data": {
            "notifications": {
                "email": True,
                "sms": False,
                "push": True
            },
            "trading": {
                "auto_confirm": False,
                "risk_level": "medium",
                "default_amount": 10000
            },
            "display": {
                "theme": "light",
                "language": "zh-CN",
                "timezone": "Asia/Shanghai"
            }
        }
    }

# ============ 策略管理API ============

@app.get("/api/v1/strategies", tags=["策略管理"])
async def get_strategies(page: int = 1, limit: int = 20, strategy_type: str = None):
    """获取策略列表"""
    strategies = [
        {
            "id": "strategy_001",
            "name": "双均线策略",
            "description": "基于快慢均线交叉的趋势跟踪策略",
            "type": "trend_following",
            "status": "running",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2025-08-02T10:00:00Z",
            "performance": {
                "total_return": 0.15,
                "annual_return": 0.12,
                "sharpe_ratio": 1.2,
                "max_drawdown": -0.08,
                "win_rate": 0.65,
                "profit_factor": 1.8
            },
            "parameters": {
                "fast_period": 5,
                "slow_period": 20,
                "stop_loss": 0.05,
                "take_profit": 0.10
            }
        },
        {
            "id": "strategy_002",
            "name": "RSI反转策略",
            "description": "基于RSI指标的超买超卖反转策略",
            "type": "mean_reversion",
            "status": "paused",
            "created_at": "2024-02-01T00:00:00Z",
            "updated_at": "2025-08-01T15:30:00Z",
            "performance": {
                "total_return": 0.08,
                "annual_return": 0.06,
                "sharpe_ratio": 0.9,
                "max_drawdown": -0.12,
                "win_rate": 0.58,
                "profit_factor": 1.4
            },
            "parameters": {
                "rsi_period": 14,
                "oversold_level": 30,
                "overbought_level": 70,
                "position_size": 0.1
            }
        }
    ]

    # 根据类型过滤
    if strategy_type:
        strategies = [s for s in strategies if s["type"] == strategy_type]

    return {
        "success": True,
        "data": {
            "strategies": strategies,
            "total": len(strategies),
            "page": page,
            "limit": limit
        }
    }

@app.post("/api/v1/strategies", tags=["策略管理"])
async def create_strategy(request: Request):
    """创建新策略"""
    data = await request.json()
    return {
        "success": True,
        "data": {
            "id": f"strategy_{int(time.time())}",
            "name": data.get("name", "新策略"),
            "description": data.get("description", ""),
            "type": data.get("type", "custom"),
            "status": "created",
            "created_at": datetime.now().isoformat(),
            "parameters": data.get("parameters", {})
        },
        "message": "策略创建成功"
    }

@app.get("/api/v1/strategies/{strategy_id}", tags=["策略管理"])
async def get_strategy_detail(strategy_id: str):
    """获取策略详情"""
    return {
        "success": True,
        "data": {
            "id": strategy_id,
            "name": "双均线策略",
            "description": "基于快慢均线交叉的趋势跟踪策略",
            "type": "trend_following",
            "status": "running",
            "code": """
def strategy_logic(data):
    # 计算快慢均线
    fast_ma = data['close'].rolling(5).mean()
    slow_ma = data['close'].rolling(20).mean()

    # 生成信号
    signals = []
    for i in range(len(data)):
        if fast_ma[i] > slow_ma[i] and fast_ma[i-1] <= slow_ma[i-1]:
            signals.append('BUY')
        elif fast_ma[i] < slow_ma[i] and fast_ma[i-1] >= slow_ma[i-1]:
            signals.append('SELL')
        else:
            signals.append('HOLD')

    return signals
            """,
            "performance": {
                "total_return": 0.15,
                "annual_return": 0.12,
                "sharpe_ratio": 1.2,
                "max_drawdown": -0.08,
                "win_rate": 0.65,
                "profit_factor": 1.8,
                "total_trades": 45,
                "winning_trades": 29,
                "losing_trades": 16
            },
            "parameters": {
                "fast_period": 5,
                "slow_period": 20,
                "stop_loss": 0.05,
                "take_profit": 0.10
            }
        }
    }

# ============ 回测管理API ============

@app.get("/api/v1/backtest/results", tags=["回测管理"])
async def get_backtest_results(page: int = 1, limit: int = 20):
    """获取回测结果列表"""
    results = [
        {
            "id": "backtest_001",
            "strategy_id": "strategy_001",
            "strategy_name": "双均线策略",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 100000,
            "final_capital": 115000,
            "total_return": 0.15,
            "annual_return": 0.15,
            "sharpe_ratio": 1.2,
            "max_drawdown": -0.08,
            "win_rate": 0.65,
            "total_trades": 45,
            "status": "completed",
            "created_at": "2025-01-01T10:00:00Z"
        },
        {
            "id": "backtest_002",
            "strategy_id": "strategy_002",
            "strategy_name": "RSI反转策略",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 100000,
            "final_capital": 108000,
            "total_return": 0.08,
            "annual_return": 0.08,
            "sharpe_ratio": 0.9,
            "max_drawdown": -0.12,
            "win_rate": 0.58,
            "total_trades": 62,
            "status": "completed",
            "created_at": "2025-01-02T14:30:00Z"
        }
    ]

    return {
        "success": True,
        "data": {
            "results": results,
            "total": len(results),
            "page": page,
            "limit": limit
        }
    }

@app.post("/api/v1/backtest/run", tags=["回测管理"])
async def run_backtest(request: Request):
    """运行回测"""
    data = await request.json()
    backtest_id = f"backtest_{int(time.time())}"

    return {
        "success": True,
        "data": {
            "id": backtest_id,
            "strategy_id": data.get("strategy_id"),
            "start_date": data.get("start_date"),
            "end_date": data.get("end_date"),
            "initial_capital": data.get("initial_capital", 100000),
            "status": "running",
            "created_at": datetime.now().isoformat(),
            "estimated_completion": (datetime.now() + timedelta(minutes=5)).isoformat()
        },
        "message": "回测任务已启动"
    }

@app.get("/api/v1/backtest/{backtest_id}", tags=["回测管理"])
async def get_backtest_detail(backtest_id: str):
    """获取回测详情"""
    return {
        "success": True,
        "data": {
            "id": backtest_id,
            "strategy_id": "strategy_001",
            "strategy_name": "双均线策略",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "initial_capital": 100000,
            "final_capital": 115000,
            "status": "completed",
            "performance": {
                "total_return": 0.15,
                "annual_return": 0.15,
                "sharpe_ratio": 1.2,
                "sortino_ratio": 1.5,
                "max_drawdown": -0.08,
                "calmar_ratio": 1.875,
                "win_rate": 0.65,
                "profit_factor": 1.8,
                "total_trades": 45,
                "winning_trades": 29,
                "losing_trades": 16,
                "avg_win": 0.025,
                "avg_loss": -0.015,
                "largest_win": 0.08,
                "largest_loss": -0.04
            },
            "equity_curve": [
                {"date": "2024-01-01", "value": 100000},
                {"date": "2024-02-01", "value": 102000},
                {"date": "2024-03-01", "value": 105000},
                {"date": "2024-04-01", "value": 103000},
                {"date": "2024-05-01", "value": 108000},
                {"date": "2024-06-01", "value": 110000},
                {"date": "2024-07-01", "value": 112000},
                {"date": "2024-08-01", "value": 109000},
                {"date": "2024-09-01", "value": 113000},
                {"date": "2024-10-01", "value": 115000},
                {"date": "2024-11-01", "value": 114000},
                {"date": "2024-12-31", "value": 115000}
            ],
            "trades": [
                {
                    "id": "trade_001",
                    "symbol": "000001.SZ",
                    "side": "BUY",
                    "quantity": 1000,
                    "price": 10.50,
                    "timestamp": "2024-01-15T09:30:00Z",
                    "pnl": 250.0
                },
                {
                    "id": "trade_002",
                    "symbol": "000001.SZ",
                    "side": "SELL",
                    "quantity": 1000,
                    "price": 10.75,
                    "timestamp": "2024-01-20T14:30:00Z",
                    "pnl": 250.0
                }
            ]
        }
    }

# ============ 风险管理API ============

@app.get("/api/v1/risk/limits", tags=["风险管理"])
async def get_risk_limits():
    """获取风险限制设置"""
    return {
        "success": True,
        "data": {
            "position_limits": {
                "max_position_size": 0.1,  # 最大仓位比例
                "max_single_stock": 0.05,  # 单只股票最大仓位
                "max_sector_exposure": 0.3  # 单个行业最大敞口
            },
            "loss_limits": {
                "daily_loss_limit": 0.02,  # 日损失限制
                "monthly_loss_limit": 0.05,  # 月损失限制
                "max_drawdown_limit": 0.15  # 最大回撤限制
            },
            "trading_limits": {
                "max_orders_per_day": 100,  # 每日最大订单数
                "max_turnover_rate": 2.0,  # 最大换手率
                "min_holding_period": 1  # 最小持仓天数
            }
        }
    }

@app.put("/api/v1/risk/limits", tags=["风险管理"])
async def update_risk_limits(request: Request):
    """更新风险限制设置"""
    data = await request.json()
    return {
        "success": True,
        "data": data,
        "message": "风险限制设置已更新"
    }

@app.get("/api/v1/risk/monitor", tags=["风险管理"])
async def get_risk_monitor():
    """获取风险监控状态"""
    return {
        "success": True,
        "data": {
            "overall_risk": "medium",
            "risk_score": 65,
            "alerts": [
                {
                    "id": "alert_001",
                    "type": "position_concentration",
                    "level": "warning",
                    "message": "单只股票仓位接近限制",
                    "symbol": "000001.SZ",
                    "current_position": 0.048,
                    "limit": 0.05,
                    "timestamp": "2025-08-02T10:30:00Z"
                }
            ],
            "metrics": {
                "portfolio_var": 0.025,  # 投资组合VaR
                "beta": 1.2,  # 组合Beta
                "correlation_risk": 0.3,  # 相关性风险
                "liquidity_risk": 0.1  # 流动性风险
            }
        }
    }

# ============ 交易管理API ============

@app.get("/api/v1/trading/orders", tags=["交易管理"])
async def get_orders(status: str = None, page: int = 1, limit: int = 20):
    """获取订单列表"""
    orders = [
        {
            "id": "order_001",
            "symbol": "000001.SZ",
            "symbol_name": "平安银行",
            "side": "BUY",
            "type": "LIMIT",
            "quantity": 1000,
            "price": 10.50,
            "filled_quantity": 1000,
            "filled_price": 10.48,
            "status": "FILLED",
            "created_at": "2025-08-02T09:30:00Z",
            "updated_at": "2025-08-02T09:30:15Z"
        },
        {
            "id": "order_002",
            "symbol": "000002.SZ",
            "symbol_name": "万科A",
            "side": "SELL",
            "type": "MARKET",
            "quantity": 500,
            "price": 0,
            "filled_quantity": 0,
            "filled_price": 0,
            "status": "PENDING",
            "created_at": "2025-08-02T10:15:00Z",
            "updated_at": "2025-08-02T10:15:00Z"
        }
    ]

    # 根据状态过滤
    if status:
        orders = [o for o in orders if o["status"] == status.upper()]

    return {
        "success": True,
        "data": {
            "orders": orders,
            "total": len(orders),
            "page": page,
            "limit": limit
        }
    }

@app.post("/api/v1/trading/orders", tags=["交易管理"])
async def create_order(request: Request):
    """创建订单"""
    data = await request.json()
    order_id = f"order_{int(time.time())}"

    return {
        "success": True,
        "data": {
            "id": order_id,
            "symbol": data.get("symbol"),
            "side": data.get("side"),
            "type": data.get("type", "LIMIT"),
            "quantity": data.get("quantity"),
            "price": data.get("price"),
            "status": "PENDING",
            "created_at": datetime.now().isoformat()
        },
        "message": "订单创建成功"
    }

@app.delete("/api/v1/trading/orders/{order_id}", tags=["交易管理"])
async def cancel_order(order_id: str):
    """取消订单"""
    return {
        "success": True,
        "data": {
            "id": order_id,
            "status": "CANCELLED",
            "cancelled_at": datetime.now().isoformat()
        },
        "message": "订单已取消"
    }

@app.get("/api/v1/trading/positions", tags=["交易管理"])
async def get_positions():
    """获取持仓信息"""
    return {
        "success": True,
        "data": {
            "positions": [
                {
                    "symbol": "000001.SZ",
                    "symbol_name": "平安银行",
                    "quantity": 2000,
                    "avg_price": 10.25,
                    "current_price": 10.48,
                    "market_value": 20960,
                    "unrealized_pnl": 460,
                    "unrealized_pnl_ratio": 0.022,
                    "weight": 0.21
                },
                {
                    "symbol": "000002.SZ",
                    "symbol_name": "万科A",
                    "quantity": 1500,
                    "avg_price": 8.80,
                    "current_price": 8.95,
                    "market_value": 13425,
                    "unrealized_pnl": 225,
                    "unrealized_pnl_ratio": 0.017,
                    "weight": 0.13
                }
            ],
            "summary": {
                "total_market_value": 34385,
                "total_cost": 33675,
                "total_pnl": 710,
                "total_pnl_ratio": 0.021,
                "cash": 65615,
                "total_assets": 100000
            }
        }
    }

# ============ 投资组合API ============

@app.get("/api/v1/portfolio/overview", tags=["投资组合"])
async def get_portfolio_overview():
    """获取投资组合概览"""
    return {
        "success": True,
        "data": {
            "total_assets": 100000,
            "cash": 65615,
            "positions_value": 34385,
            "today_pnl": 285,
            "today_pnl_ratio": 0.0029,
            "total_pnl": 710,
            "total_pnl_ratio": 0.0071,
            "allocation": {
                "stocks": 0.344,
                "cash": 0.656,
                "others": 0.0
            },
            "sector_allocation": [
                {"sector": "金融", "weight": 0.21, "pnl": 460},
                {"sector": "房地产", "weight": 0.13, "pnl": 225},
                {"sector": "现金", "weight": 0.66, "pnl": 0}
            ]
        }
    }

@app.get("/api/v1/portfolio/performance", tags=["投资组合"])
async def get_portfolio_performance():
    """获取投资组合业绩"""
    return {
        "success": True,
        "data": {
            "period_returns": {
                "1d": 0.0029,
                "1w": 0.0085,
                "1m": 0.0156,
                "3m": 0.0423,
                "6m": 0.0671,
                "1y": 0.1245,
                "ytd": 0.0671
            },
            "risk_metrics": {
                "volatility": 0.18,
                "sharpe_ratio": 0.85,
                "max_drawdown": -0.065,
                "beta": 1.15,
                "alpha": 0.025
            },
            "benchmark_comparison": {
                "benchmark": "沪深300",
                "portfolio_return": 0.1245,
                "benchmark_return": 0.0892,
                "excess_return": 0.0353,
                "tracking_error": 0.045
            }
        }
    }

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"Unexpected error: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_error",
            "message": str(exc)
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main_simple:app", host="0.0.0.0", port=8000, reload=True)