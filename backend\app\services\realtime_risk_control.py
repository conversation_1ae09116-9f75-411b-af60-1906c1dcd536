"""
实时风控系统
提供交易前、交易中、交易后的全流程风险控制
"""
import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

import numpy as np
import pandas as pd
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.models.trading import Order, Position, Trade
from app.db.models.user import User
from app.services.market_service import MarketService
from app.services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskType(Enum):
    """风险类型"""
    POSITION_LIMIT = "position_limit"          # 持仓限制
    LOSS_LIMIT = "loss_limit"                  # 亏损限制
    LEVERAGE_LIMIT = "leverage_limit"          # 杠杆限制
    CONCENTRATION = "concentration"            # 集中度
    LIQUIDITY = "liquidity"                    # 流动性
    VOLATILITY = "volatility"                  # 波动率
    CORRELATION = "correlation"                # 相关性
    MARGIN_CALL = "margin_call"               # 保证金
    MARKET_IMPACT = "market_impact"           # 市场冲击


@dataclass
class RiskRule:
    """风险规则"""
    rule_id: str
    rule_type: RiskType
    level: RiskLevel
    threshold: float
    action: str  # reject, warn, reduce
    description: str
    enabled: bool = True


@dataclass
class RiskCheckResult:
    """风控检查结果"""
    passed: bool
    level: RiskLevel
    violated_rules: List[RiskRule]
    messages: List[str]
    suggestions: List[str]
    metrics: Dict[str, float]


class RealtimeRiskControl:
    """实时风控系统"""
    
    def __init__(
        self,
        db: AsyncSession,
        market_service: MarketService,
        notification_service: NotificationService
    ):
        self.db = db
        self.market_service = market_service
        self.notification_service = notification_service
        
        # 风控规则配置
        self.rules = self._init_risk_rules()
        
        # 缓存
        self._position_cache = {}
        self._market_data_cache = {}
        self._risk_metrics_cache = {}
        
    def _init_risk_rules(self) -> List[RiskRule]:
        """初始化风控规则"""
        return [
            # 持仓限制规则
            RiskRule(
                rule_id="POS001",
                rule_type=RiskType.POSITION_LIMIT,
                level=RiskLevel.HIGH,
                threshold=settings.MAX_POSITION_RATIO,
                action="reject",
                description="单一品种持仓比例超限"
            ),
            RiskRule(
                rule_id="POS002",
                rule_type=RiskType.POSITION_LIMIT,
                level=RiskLevel.MEDIUM,
                threshold=0.8 * settings.MAX_POSITION_RATIO,
                action="warn",
                description="单一品种持仓比例接近上限"
            ),
            
            # 亏损限制规则
            RiskRule(
                rule_id="LOSS001",
                rule_type=RiskType.LOSS_LIMIT,
                level=RiskLevel.CRITICAL,
                threshold=settings.MAX_DAILY_LOSS_RATIO,
                action="reject",
                description="日内亏损超限"
            ),
            RiskRule(
                rule_id="LOSS002",
                rule_type=RiskType.LOSS_LIMIT,
                level=RiskLevel.HIGH,
                threshold=settings.MAX_DRAWDOWN_RATIO,
                action="reject",
                description="最大回撤超限"
            ),
            
            # 杠杆限制规则
            RiskRule(
                rule_id="LEV001",
                rule_type=RiskType.LEVERAGE_LIMIT,
                level=RiskLevel.HIGH,
                threshold=10.0,
                action="reject",
                description="杠杆率超限"
            ),
            
            # 集中度规则
            RiskRule(
                rule_id="CONC001",
                rule_type=RiskType.CONCENTRATION,
                level=RiskLevel.MEDIUM,
                threshold=0.5,
                action="warn",
                description="行业集中度过高"
            ),
            
            # 流动性规则
            RiskRule(
                rule_id="LIQ001",
                rule_type=RiskType.LIQUIDITY,
                level=RiskLevel.HIGH,
                threshold=0.1,  # 订单量占日均成交量比例
                action="reduce",
                description="订单量过大，可能影响市场"
            ),
            
            # 波动率规则
            RiskRule(
                rule_id="VOL001",
                rule_type=RiskType.VOLATILITY,
                level=RiskLevel.MEDIUM,
                threshold=0.5,  # 50%年化波动率
                action="warn",
                description="标的波动率过高"
            ),
        ]
    
    async def check_order_risk(
        self,
        user: User,
        order: Order,
        positions: List[Position]
    ) -> RiskCheckResult:
        """
        交易前风控检查
        
        Args:
            user: 用户
            order: 订单
            positions: 当前持仓
            
        Returns:
            风控检查结果
        """
        violated_rules = []
        messages = []
        suggestions = []
        metrics = {}
        
        # 1. 检查持仓限制
        position_check = await self._check_position_limit(user, order, positions)
        if not position_check['passed']:
            violated_rules.extend(position_check['violated_rules'])
            messages.extend(position_check['messages'])
        metrics.update(position_check['metrics'])
        
        # 2. 检查亏损限制
        loss_check = await self._check_loss_limit(user, positions)
        if not loss_check['passed']:
            violated_rules.extend(loss_check['violated_rules'])
            messages.extend(loss_check['messages'])
        metrics.update(loss_check['metrics'])
        
        # 3. 检查杠杆限制
        leverage_check = await self._check_leverage_limit(user, order, positions)
        if not leverage_check['passed']:
            violated_rules.extend(leverage_check['violated_rules'])
            messages.extend(leverage_check['messages'])
        metrics.update(leverage_check['metrics'])
        
        # 4. 检查流动性
        liquidity_check = await self._check_liquidity(order)
        if not liquidity_check['passed']:
            violated_rules.extend(liquidity_check['violated_rules'])
            messages.extend(liquidity_check['messages'])
            suggestions.extend(liquidity_check['suggestions'])
        metrics.update(liquidity_check['metrics'])
        
        # 5. 检查波动率
        volatility_check = await self._check_volatility(order.symbol)
        if not volatility_check['passed']:
            violated_rules.extend(volatility_check['violated_rules'])
            messages.extend(volatility_check['messages'])
            suggestions.extend(volatility_check['suggestions'])
        metrics.update(volatility_check['metrics'])
        
        # 判断是否通过
        reject_rules = [r for r in violated_rules if r.action == "reject"]
        passed = len(reject_rules) == 0
        
        # 确定风险级别
        if violated_rules:
            level = max(r.level for r in violated_rules)
        else:
            level = RiskLevel.LOW
            
        # 生成建议
        if not passed:
            suggestions.append("建议减少订单数量或分批下单")
            suggestions.append("请检查账户风险状况后再试")
            
        return RiskCheckResult(
            passed=passed,
            level=level,
            violated_rules=violated_rules,
            messages=messages,
            suggestions=suggestions,
            metrics=metrics
        )
    
    async def monitor_positions(
        self,
        user: User,
        positions: List[Position]
    ) -> Dict[str, Any]:
        """
        持仓风险监控
        
        Args:
            user: 用户
            positions: 持仓列表
            
        Returns:
            监控结果
        """
        alerts = []
        risk_metrics = {}
        
        # 1. 计算整体风险指标
        total_value = sum(p.market_value for p in positions)
        total_pnl = sum(p.unrealized_pnl for p in positions)
        
        risk_metrics['total_value'] = total_value
        risk_metrics['total_pnl'] = total_pnl
        risk_metrics['pnl_ratio'] = total_pnl / total_value if total_value > 0 else 0
        
        # 2. 检查单个持仓风险
        for position in positions:
            # 检查亏损
            if position.unrealized_pnl < 0:
                loss_ratio = abs(position.unrealized_pnl) / position.cost_basis
                if loss_ratio > 0.1:  # 亏损超过10%
                    alerts.append({
                        'type': 'position_loss',
                        'level': RiskLevel.HIGH if loss_ratio > 0.2 else RiskLevel.MEDIUM,
                        'symbol': position.symbol,
                        'message': f"{position.symbol} 亏损 {loss_ratio:.2%}",
                        'suggestion': "考虑止损或减仓"
                    })
                    
            # 检查集中度
            concentration = position.market_value / total_value if total_value > 0 else 0
            if concentration > 0.3:  # 单一持仓超过30%
                alerts.append({
                    'type': 'concentration',
                    'level': RiskLevel.MEDIUM,
                    'symbol': position.symbol,
                    'message': f"{position.symbol} 持仓占比 {concentration:.2%}",
                    'suggestion': "建议分散投资降低风险"
                })
                
        # 3. 检查整体回撤
        drawdown = await self._calculate_drawdown(user)
        risk_metrics['max_drawdown'] = drawdown
        
        if drawdown > settings.MAX_DRAWDOWN_RATIO * 0.8:
            alerts.append({
                'type': 'drawdown',
                'level': RiskLevel.HIGH,
                'message': f"当前回撤 {drawdown:.2%}，接近最大限制",
                'suggestion': "建议降低仓位，控制风险"
            })
            
        # 4. 计算VaR (Value at Risk)
        var_95 = await self._calculate_var(positions, confidence=0.95)
        risk_metrics['var_95'] = var_95
        
        # 5. 发送风险警报
        if alerts:
            critical_alerts = [a for a in alerts if a['level'] == RiskLevel.CRITICAL]
            if critical_alerts:
                await self._send_risk_alert(user, critical_alerts)
                
        return {
            'alerts': alerts,
            'metrics': risk_metrics,
            'timestamp': datetime.now()
        }
    
    async def _check_position_limit(
        self,
        user: User,
        order: Order,
        positions: List[Position]
    ) -> Dict[str, Any]:
        """检查持仓限制"""
        violated_rules = []
        messages = []
        metrics = {}
        
        # 计算当前持仓
        total_value = sum(p.market_value for p in positions)
        symbol_positions = [p for p in positions if p.symbol == order.symbol]
        current_position_value = sum(p.market_value for p in symbol_positions)
        
        # 计算订单后的持仓
        order_value = order.quantity * order.price
        new_position_value = current_position_value + order_value
        new_total_value = total_value + order_value
        
        # 计算持仓比例
        position_ratio = new_position_value / new_total_value if new_total_value > 0 else 0
        metrics['position_ratio'] = position_ratio
        
        # 检查规则
        for rule in self.rules:
            if rule.rule_type == RiskType.POSITION_LIMIT and rule.enabled:
                if position_ratio > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"{order.symbol} 持仓比例 {position_ratio:.2%} "
                        f"超过限制 {rule.threshold:.2%}"
                    )
                    
        return {
            'passed': len([r for r in violated_rules if r.action == "reject"]) == 0,
            'violated_rules': violated_rules,
            'messages': messages,
            'metrics': metrics
        }
    
    async def _check_loss_limit(
        self,
        user: User,
        positions: List[Position]
    ) -> Dict[str, Any]:
        """检查亏损限制"""
        violated_rules = []
        messages = []
        metrics = {}
        
        # 计算日内亏损
        daily_pnl = await self._calculate_daily_pnl(user)
        daily_loss_ratio = abs(daily_pnl) / user.account_balance if daily_pnl < 0 else 0
        metrics['daily_loss_ratio'] = daily_loss_ratio
        
        # 计算最大回撤
        drawdown = await self._calculate_drawdown(user)
        metrics['drawdown'] = drawdown
        
        # 检查规则
        for rule in self.rules:
            if rule.rule_type == RiskType.LOSS_LIMIT and rule.enabled:
                if rule.rule_id == "LOSS001" and daily_loss_ratio > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"日内亏损 {daily_loss_ratio:.2%} "
                        f"超过限制 {rule.threshold:.2%}"
                    )
                elif rule.rule_id == "LOSS002" and drawdown > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"最大回撤 {drawdown:.2%} "
                        f"超过限制 {rule.threshold:.2%}"
                    )
                    
        return {
            'passed': len([r for r in violated_rules if r.action == "reject"]) == 0,
            'violated_rules': violated_rules,
            'messages': messages,
            'metrics': metrics
        }
    
    async def _check_leverage_limit(
        self,
        user: User,
        order: Order,
        positions: List[Position]
    ) -> Dict[str, Any]:
        """检查杠杆限制"""
        violated_rules = []
        messages = []
        metrics = {}
        
        # 计算当前杠杆
        total_market_value = sum(p.market_value for p in positions)
        current_leverage = total_market_value / user.account_balance if user.account_balance > 0 else 0
        
        # 计算订单后的杠杆
        order_value = order.quantity * order.price
        new_market_value = total_market_value + order_value
        new_leverage = new_market_value / user.account_balance if user.account_balance > 0 else 0
        
        metrics['current_leverage'] = current_leverage
        metrics['new_leverage'] = new_leverage
        
        # 检查规则
        for rule in self.rules:
            if rule.rule_type == RiskType.LEVERAGE_LIMIT and rule.enabled:
                if new_leverage > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"杠杆率 {new_leverage:.1f} "
                        f"超过限制 {rule.threshold:.1f}"
                    )
                    
        return {
            'passed': len([r for r in violated_rules if r.action == "reject"]) == 0,
            'violated_rules': violated_rules,
            'messages': messages,
            'metrics': metrics
        }
    
    async def _check_liquidity(self, order: Order) -> Dict[str, Any]:
        """检查流动性"""
        violated_rules = []
        messages = []
        suggestions = []
        metrics = {}
        
        # 获取市场数据
        market_data = await self.market_service.get_market_depth(order.symbol)
        if not market_data:
            return {
                'passed': True,
                'violated_rules': [],
                'messages': [],
                'suggestions': [],
                'metrics': {}
            }
            
        # 计算日均成交量
        avg_volume = market_data.get('avg_daily_volume', 0)
        if avg_volume == 0:
            return {
                'passed': True,
                'violated_rules': [],
                'messages': [],
                'suggestions': [],
                'metrics': {}
            }
            
        # 计算订单占比
        order_ratio = order.quantity / avg_volume
        metrics['order_volume_ratio'] = order_ratio
        
        # 计算市场冲击成本
        impact_cost = self._estimate_market_impact(order, market_data)
        metrics['market_impact_cost'] = impact_cost
        
        # 检查规则
        for rule in self.rules:
            if rule.rule_type == RiskType.LIQUIDITY and rule.enabled:
                if order_ratio > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"订单量占日均成交量 {order_ratio:.2%}，"
                        f"可能影响市场价格"
                    )
                    suggestions.append(
                        f"建议将订单拆分为 {int(order_ratio / rule.threshold) + 1} 批执行"
                    )
                    
        return {
            'passed': len([r for r in violated_rules if r.action == "reject"]) == 0,
            'violated_rules': violated_rules,
            'messages': messages,
            'suggestions': suggestions,
            'metrics': metrics
        }
    
    async def _check_volatility(self, symbol: str) -> Dict[str, Any]:
        """检查波动率"""
        violated_rules = []
        messages = []
        suggestions = []
        metrics = {}
        
        # 计算历史波动率
        volatility = await self._calculate_volatility(symbol)
        metrics['volatility'] = volatility
        
        # 检查规则
        for rule in self.rules:
            if rule.rule_type == RiskType.VOLATILITY and rule.enabled:
                if volatility > rule.threshold:
                    violated_rules.append(rule)
                    messages.append(
                        f"{symbol} 年化波动率 {volatility:.2%}，"
                        f"超过阈值 {rule.threshold:.2%}"
                    )
                    suggestions.append("建议减少仓位或使用止损单")
                    
        return {
            'passed': len([r for r in violated_rules if r.action == "reject"]) == 0,
            'violated_rules': violated_rules,
            'messages': messages,
            'suggestions': suggestions,
            'metrics': metrics
        }
    
    async def _calculate_daily_pnl(self, user: User) -> float:
        """计算日内盈亏"""
        # 这里应该从数据库查询今日的交易记录
        # 简化实现，返回模拟值
        return -1000.0
    
    async def _calculate_drawdown(self, user: User) -> float:
        """计算最大回撤"""
        # 这里应该从历史净值数据计算
        # 简化实现，返回模拟值
        return 0.12
    
    async def _calculate_volatility(self, symbol: str, days: int = 30) -> float:
        """计算历史波动率"""
        # 获取历史价格数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        prices = await self.market_service.get_historical_prices(
            symbol, start_date, end_date
        )
        
        if len(prices) < 2:
            return 0.0
            
        # 计算日收益率
        returns = pd.Series(prices).pct_change().dropna()
        
        # 计算年化波动率
        volatility = returns.std() * np.sqrt(252)
        
        return volatility
    
    async def _calculate_var(
        self,
        positions: List[Position],
        confidence: float = 0.95,
        days: int = 1
    ) -> float:
        """计算VaR (Value at Risk)"""
        if not positions:
            return 0.0
            
        # 获取持仓权重
        total_value = sum(p.market_value for p in positions)
        weights = np.array([p.market_value / total_value for p in positions])
        
        # 获取历史收益率
        returns_list = []
        for position in positions:
            prices = await self.market_service.get_historical_prices(
                position.symbol,
                datetime.now() - timedelta(days=252),
                datetime.now()
            )
            if prices:
                returns = pd.Series(prices).pct_change().dropna()
                returns_list.append(returns)
                
        if not returns_list:
            return 0.0
            
        # 计算组合收益率
        portfolio_returns = sum(
            w * r for w, r in zip(weights, returns_list)
        )
        
        # 计算VaR
        var = np.percentile(portfolio_returns, (1 - confidence) * 100)
        var *= np.sqrt(days)  # 调整到指定天数
        var *= total_value    # 转换为金额
        
        return abs(var)
    
    def _estimate_market_impact(
        self,
        order: Order,
        market_data: Dict[str, Any]
    ) -> float:
        """估算市场冲击成本"""
        # 简化的市场冲击模型
        # 实际应该使用更复杂的模型，如Almgren-Chriss模型
        avg_spread = market_data.get('avg_spread', 0.001)
        order_size = order.quantity * order.price
        avg_daily_value = market_data.get('avg_daily_value', 1000000)
        
        # 线性冲击模型
        impact_ratio = order_size / avg_daily_value
        impact_cost = avg_spread * (1 + 10 * impact_ratio)
        
        return impact_cost
    
    async def _send_risk_alert(
        self,
        user: User,
        alerts: List[Dict[str, Any]]
    ) -> None:
        """发送风险警报"""
        for alert in alerts:
            await self.notification_service.send_notification(
                user_id=user.id,
                title="风险警报",
                message=alert['message'],
                level=alert['level'].value,
                data=alert
            )