<template>
  <div class="enhanced-kline-chart">
    <!-- 图表工具栏 -->
    <div class="chart-toolbar">
      <div class="toolbar-left">
        <div class="symbol-info">
          <span class="symbol">{{ symbol }}</span>
          <span class="symbol-name">{{ symbolName }}</span>
        </div>

        <!-- 时间周期选择 -->
        <el-radio-group v-model="currentPeriod" size="small" @change="onPeriodChange">
          <el-radio-button label="1m">分时</el-radio-button>
          <el-radio-button label="5m">5分</el-radio-button>
          <el-radio-button label="15m">15分</el-radio-button>
          <el-radio-button label="30m">30分</el-radio-button>
          <el-radio-button label="1h">1小时</el-radio-button>
          <el-radio-button label="1d">日K</el-radio-button>
          <el-radio-button label="1w">周K</el-radio-button>
          <el-radio-button label="1M">月K</el-radio-button>
        </el-radio-group>
      </div>

      <div class="toolbar-right">
        <!-- 技术指标 -->
        <el-dropdown @command="handleIndicatorCommand">
          <el-button size="small" type="primary" plain>
            技术指标 <el-icon><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="MA">移动平均线(MA)</el-dropdown-item>
              <el-dropdown-item command="MACD">MACD</el-dropdown-item>
              <el-dropdown-item command="RSI">RSI</el-dropdown-item>
              <el-dropdown-item command="KDJ">KDJ</el-dropdown-item>
              <el-dropdown-item command="BOLL">布林带(BOLL)</el-dropdown-item>
              <el-dropdown-item command="VOL">成交量</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 画线工具 -->
        <el-dropdown @command="handleDrawingCommand">
          <el-button size="small" type="success" plain>
            画线工具 <el-icon><Edit /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="line">趋势线</el-dropdown-item>
              <el-dropdown-item command="horizontal">水平线</el-dropdown-item>
              <el-dropdown-item command="vertical">垂直线</el-dropdown-item>
              <el-dropdown-item command="rectangle">矩形</el-dropdown-item>
              <el-dropdown-item command="fibonacci">斐波那契回调</el-dropdown-item>
              <el-dropdown-item command="clear">清除所有</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <!-- 图表设置 -->
        <el-button size="small" @click="showSettings = true" :icon="Setting">
          设置
        </el-button>

        <!-- 全屏 -->
        <el-button size="small" @click="toggleFullscreen" :icon="FullScreen">
          全屏
        </el-button>
      </div>
    </div>

    <!-- 主图表区域 -->
    <div class="chart-main" :class="{ 'fullscreen': isFullscreen }">
      <div
        ref="chartContainer"
        class="chart-container"
        :style="{ height: chartHeight + 'px' }"
      />

      <!-- 十字光标信息 -->
      <div v-if="crosshairData" class="crosshair-info">
        <div class="info-item">
          <span class="label">时间:</span>
          <span class="value">{{ crosshairData.time }}</span>
        </div>
        <div class="info-item">
          <span class="label">开:</span>
          <span class="value">{{ crosshairData.open }}</span>
        </div>
        <div class="info-item">
          <span class="label">高:</span>
          <span class="value">{{ crosshairData.high }}</span>
        </div>
        <div class="info-item">
          <span class="label">低:</span>
          <span class="value">{{ crosshairData.low }}</span>
        </div>
        <div class="info-item">
          <span class="label">收:</span>
          <span class="value">{{ crosshairData.close }}</span>
        </div>
        <div class="info-item">
          <span class="label">量:</span>
          <span class="value">{{ crosshairData.volume }}</span>
        </div>
      </div>
    </div>

    <!-- 指标面板 -->
    <div v-if="activeIndicators.length > 0" class="indicators-panel">
      <div
        v-for="indicator in activeIndicators"
        :key="indicator.type"
        class="indicator-chart"
        :style="{ height: indicatorHeight + 'px' }"
      >
        <div class="indicator-header">
          <span class="indicator-name">{{ indicator.name }}</span>
          <el-button
            size="small"
            text
            type="danger"
            @click="removeIndicator(indicator.type)"
          >
            删除
          </el-button>
        </div>
        <div
          :ref="el => setIndicatorRef(indicator.type, el)"
          class="indicator-container"
        />
      </div>
    </div>

    <!-- 设置对话框 -->
    <el-dialog v-model="showSettings" title="图表设置" width="500px">
      <el-form :model="chartSettings" label-width="100px">
        <el-form-item label="图表主题">
          <el-select v-model="chartSettings.theme">
            <el-option label="浅色主题" value="light" />
            <el-option label="深色主题" value="dark" />
          </el-select>
        </el-form-item>

        <el-form-item label="K线样式">
          <el-select v-model="chartSettings.candleStyle">
            <el-option label="蜡烛图" value="candle" />
            <el-option label="美国线" value="ohlc" />
            <el-option label="收盘价线" value="line" />
          </el-select>
        </el-form-item>

        <el-form-item label="涨跌颜色">
          <el-radio-group v-model="chartSettings.colorScheme">
            <el-radio label="red-green">红涨绿跌</el-radio>
            <el-radio label="green-red">绿涨红跌</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="显示网格">
          <el-switch v-model="chartSettings.showGrid" />
        </el-form-item>

        <el-form-item label="显示成交量">
          <el-switch v-model="chartSettings.showVolume" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showSettings = false">取消</el-button>
        <el-button type="primary" @click="applySettings">应用</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ArrowDown, Edit, Setting, FullScreen } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'

interface Props {
  symbol: string
  symbolName?: string
  period?: string
  height?: number
  data?: any[]
}

const props = withDefaults(defineProps<Props>(), {
  symbolName: '',
  period: '1d',
  height: 400,
  data: () => []
})

const emit = defineEmits<{
  'period-change': [period: string]
  'indicator-add': [indicator: string]
  'indicator-remove': [indicator: string]
}>()

// 响应式数据
const chartContainer = ref<HTMLElement>()
const chart = ref<ECharts>()
const currentPeriod = ref(props.period)
const isFullscreen = ref(false)
const showSettings = ref(false)
const crosshairData = ref<any>(null)
const activeIndicators = ref<any[]>([])
const indicatorRefs = ref<Map<string, HTMLElement>>(new Map())

// 图表设置
const chartSettings = ref({
  theme: 'light',
  candleStyle: 'candle',
  colorScheme: 'red-green',
  showGrid: true,
  showVolume: true
})

// 计算属性
const chartHeight = computed(() => {
  if (isFullscreen.value) return window.innerHeight - 200
  return props.height
})

const indicatorHeight = computed(() => {
  return Math.max(120, chartHeight.value / 4)
})

// 方法
const onPeriodChange = (period: string) => {
  currentPeriod.value = period
  emit('period-change', period)
}

const handleIndicatorCommand = (command: string) => {
  addIndicator(command)
}

const handleDrawingCommand = (command: string) => {
  if (command === 'clear') {
    clearAllDrawings()
  } else {
    enableDrawingMode(command)
  }
}

const addIndicator = (type: string) => {
  const indicator = {
    type,
    name: getIndicatorName(type),
    params: getDefaultParams(type)
  }
  activeIndicators.value.push(indicator)
  emit('indicator-add', type)
  nextTick(() => {
    initIndicatorChart(indicator)
  })
}

const removeIndicator = (type: string) => {
  const index = activeIndicators.value.findIndex(ind => ind.type === type)
  if (index > -1) {
    activeIndicators.value.splice(index, 1)
    emit('indicator-remove', type)
  }
}

const getIndicatorName = (type: string): string => {
  const names: Record<string, string> = {
    'MA': '移动平均线',
    'MACD': 'MACD',
    'RSI': 'RSI',
    'KDJ': 'KDJ',
    'BOLL': '布林带',
    'VOL': '成交量'
  }
  return names[type] || type
}

const getDefaultParams = (type: string): any => {
  const params: Record<string, any> = {
    'MA': { periods: [5, 10, 20, 60] },
    'MACD': { fast: 12, slow: 26, signal: 9 },
    'RSI': { period: 14 },
    'KDJ': { k: 9, d: 3, j: 3 },
    'BOLL': { period: 20, multiplier: 2 },
    'VOL': {}
  }
  return params[type] || {}
}

const setIndicatorRef = (type: string, el: HTMLElement | null) => {
  if (el) {
    indicatorRefs.value.set(type, el)
  }
}

const initChart = () => {
  if (!chartContainer.value) return

  chart.value = echarts.init(chartContainer.value, chartSettings.value.theme)
  updateChart()

  // 添加事件监听
  chart.value.on('mousemove', handleMouseMove)
  chart.value.on('click', handleChartClick)
}

const updateChart = () => {
  if (!chart.value || !props.data.length) return

  const option = generateChartOption()
  chart.value.setOption(option, true)
}

const generateChartOption = () => {
  const upColor = chartSettings.value.colorScheme === 'red-green' ? '#ec0000' : '#00da3c'
  const downColor = chartSettings.value.colorScheme === 'red-green' ? '#00da3c' : '#ec0000'

  const klineData = props.data.map(item => [
    item.open, item.close, item.low, item.high
  ])

  const volumeData = props.data.map((item, index) => ({
    value: item.volume,
    itemStyle: {
      color: item.close >= item.open ? upColor : downColor
    }
  }))

  return {
    animation: false,
    backgroundColor: chartSettings.value.theme === 'dark' ? '#1e1e1e' : '#ffffff',
    textStyle: {
      color: chartSettings.value.theme === 'dark' ? '#ffffff' : '#333333'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      backgroundColor: chartSettings.value.theme === 'dark' ? 'rgba(50, 50, 50, 0.9)' : 'rgba(255, 255, 255, 0.9)',
      borderColor: '#ccc',
      textStyle: {
        color: chartSettings.value.theme === 'dark' ? '#ffffff' : '#333333'
      },
      formatter: function (params: any) {
        const data = params[0]
        const kline = props.data[data.dataIndex]
        return `
          <div>
            <div>时间: ${new Date(kline.timestamp).toLocaleString()}</div>
            <div>开盘: ${kline.open.toFixed(2)}</div>
            <div>最高: ${kline.high.toFixed(2)}</div>
            <div>最低: ${kline.low.toFixed(2)}</div>
            <div>收盘: ${kline.close.toFixed(2)}</div>
            <div>成交量: ${kline.volume}</div>
          </div>
        `
      }
    },
    grid: [
      {
        left: '10%',
        right: '8%',
        height: chartSettings.value.showVolume ? '60%' : '80%',
        top: '10%'
      },
      {
        left: '10%',
        right: '8%',
        top: '75%',
        height: '15%'
      }
    ],
    xAxis: [
      {
        type: 'category',
        data: props.data.map(item => new Date(item.timestamp).toLocaleString()),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        splitLine: { show: chartSettings.value.showGrid },
        min: 'dataMin',
        max: 'dataMax'
      },
      {
        type: 'category',
        gridIndex: 1,
        data: props.data.map(item => new Date(item.timestamp).toLocaleString()),
        scale: true,
        boundaryGap: false,
        axisLine: { onZero: false },
        axisTick: { show: false },
        splitLine: { show: false },
        axisLabel: { show: false },
        min: 'dataMin',
        max: 'dataMax'
      }
    ],
    yAxis: [
      {
        scale: true,
        splitArea: {
          show: true
        }
      },
      {
        scale: true,
        gridIndex: 1,
        splitNumber: 2,
        axisLabel: { show: false },
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: false }
      }
    ],
    dataZoom: [
      {
        type: 'inside',
        xAxisIndex: [0, 1],
        start: 80,
        end: 100
      },
      {
        show: true,
        xAxisIndex: [0, 1],
        type: 'slider',
        top: '90%',
        start: 80,
        end: 100
      }
    ],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: klineData,
        itemStyle: {
          color: upColor,
          color0: downColor,
          borderColor: upColor,
          borderColor0: downColor
        },
        markPoint: {
          label: {
            formatter: function (param: any) {
              return param != null ? Math.round(param.value) + '' : ''
            }
          },
          data: [
            {
              name: 'Mark',
              coord: ['2013/5/31', 2300],
              value: 2300,
              itemStyle: {
                color: 'rgb(41,60,85)'
              }
            },
            {
              name: 'highest value',
              type: 'max',
              valueDim: 'highest'
            },
            {
              name: 'lowest value',
              type: 'min',
              valueDim: 'lowest'
            }
          ],
          tooltip: {
            formatter: function (param: any) {
              return param.name + '<br>' + (param.data.coord || '')
            }
          }
        }
      }
    ]
  }
}

const initIndicatorChart = (indicator: any) => {
  // 初始化指标图表
}

const handleMouseMove = (params: any) => {
  // 处理鼠标移动，更新十字光标数据
}

const handleChartClick = (params: any) => {
  // 处理图表点击事件
}

const enableDrawingMode = (mode: string) => {
  // 启用画线模式
}

const clearAllDrawings = () => {
  // 清除所有画线
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

const applySettings = () => {
  showSettings.value = false
  // 应用设置并重新渲染图表
  if (chart.value) {
    chart.value.dispose()
    initChart()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose()
  }
})
</script>

<style scoped>
.enhanced-kline-chart {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 工具栏样式 */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.symbol {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.symbol-name {
  font-size: 14px;
  color: #909399;
}

/* 图表主区域 */
.chart-main {
  position: relative;
  background: #fff;
}

.chart-main.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: #fff;
}

.chart-container {
  width: 100%;
}

/* 十字光标信息 */
.crosshair-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  gap: 16px;
  pointer-events: none;
}

.info-item {
  display: flex;
  gap: 4px;
}

.info-item .label {
  color: #ccc;
}

.info-item .value {
  color: #fff;
  font-weight: 500;
}

/* 指标面板 */
.indicators-panel {
  border-top: 1px solid #e9ecef;
}

.indicator-chart {
  border-bottom: 1px solid #f0f0f0;
}

.indicator-chart:last-child {
  border-bottom: none;
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.indicator-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.indicator-container {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .crosshair-info {
    flex-direction: column;
    gap: 4px;
  }

  .info-item {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .chart-toolbar {
    padding: 8px 12px;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 8px;
  }

  .toolbar-right {
    flex-wrap: wrap;
    gap: 4px;
  }

  .crosshair-info {
    font-size: 11px;
    padding: 6px 8px;
  }
}

/* 深色主题 */
.enhanced-kline-chart.dark {
  background: #1e1e1e;
  color: #fff;
}

.enhanced-kline-chart.dark .chart-toolbar {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.enhanced-kline-chart.dark .chart-main {
  background: #1e1e1e;
}

.enhanced-kline-chart.dark .indicator-header {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.enhanced-kline-chart.dark .indicators-panel {
  border-top-color: #404040;
}

.enhanced-kline-chart.dark .indicator-chart {
  border-bottom-color: #404040;
}
</style>
