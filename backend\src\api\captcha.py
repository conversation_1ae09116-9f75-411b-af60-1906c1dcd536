import random
import time
import base64
from io import BytesIO
from typing import Dict, Any

from fastapi import APIRouter, Request, Depends, HTTPException
from starlette.responses import J<PERSON><PERSON>esponse
from PIL import Image, ImageDraw, ImageFont

from app.core.cache import Cache, get_cache
from app.core.config import settings

router = APIRouter()

CAPTCHA_EXPIRATION = 180  # 验证码过期时间（秒）
VERIFICATION_TOKEN_EXPIRATION = 300  # 验证成功后的token过期时间（秒）
TOLERANCE = 5  # 容忍的像素误差

# 简单的内存缓存，用于存储字体等资源，避免重复加载
_cache: Dict[str, Any] = {}


def get_font(font_path: str, font_size: int) -> ImageFont.FreeTypeFont:
    """加载字体文件，并缓存"""
    key = f"{font_path}:{font_size}"
    if key not in _cache:
        try:
            _cache[key] = ImageFont.truetype(font_path, font_size)
        except IOError:
            # 在某些环境下可能找不到字体文件，使用默认字体
            _cache[key] = ImageFont.load_default()
    return _cache[key]


def generate_captcha_images():
    """生成滑块验证码背景和滑块图片"""
    # 背景图尺寸
    bg_width, bg_height = 280, 160
    # 滑块图尺寸
    slider_width, slider_height = 50, 50

    # 创建背景图
    background = Image.new(
        "RGB",
        (bg_width, bg_height),
        (random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)),
    )
    draw = ImageDraw.Draw(background)

    # 在背景图上绘制一些干扰线
    for _ in range(5):
        draw.line(
            (
                random.randint(0, bg_width),
                random.randint(0, bg_height),
                random.randint(0, bg_width),
                random.randint(0, bg_height),
            ),
            fill=(
                random.randint(0, 255),
                random.randint(0, 255),
                random.randint(0, 255),
            ),
            width=random.randint(1, 2),
        )

    # 在背景图上绘制一些文字
    try:
        font = get_font("arial.ttf", 20)
    except Exception:
        font = ImageFont.load_default()

    for i in range(4):
        draw.text(
            (random.randint(0, bg_width - 20), random.randint(0, bg_height - 20)),
            random.choice(
                "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
            ),
            font=font,
            fill=(
                random.randint(0, 255),
                random.randint(0, 255),
                random.randint(0, 255),
            ),
        )

    # 随机生成滑块的x, y坐标
    # x坐标需要留出左边距，防止滑块跑到图片外面
    slider_x = random.randint(slider_width + 10, bg_width - slider_width - 10)
    slider_y = random.randint(10, bg_height - slider_height - 10)

    # 从背景图上裁剪出滑块区域
    slider_image = background.crop(
        (slider_x, slider_y, slider_x + slider_width, slider_y + slider_height)
    )

    # 在原背景图上挖掉滑块区域，用灰色填充
    draw.rectangle(
        (slider_x, slider_y, slider_x + slider_width, slider_y + slider_height),
        fill=(128, 128, 128),
    )

    # 将图片转换为base64
    bg_io = BytesIO()
    background.save(bg_io, "PNG")
    bg_base64 = "data:image/png;base64," + base64.b64encode(bg_io.getvalue()).decode(
        "utf-8"
    )

    slider_io = BytesIO()
    slider_image.save(slider_io, "PNG")
    slider_base64 = "data:image/png;base64," + base64.b64encode(
        slider_io.getvalue()
    ).decode("utf-8")

    return {
        "background_image": bg_base64,
        "slider_image": slider_base64,
        "slider_x": slider_x,
        "slider_y": slider_y,
        "background_width": bg_width,
        "background_height": bg_height,
    }


@router.get("/slider", summary="获取滑块验证码")
async def get_slider_captcha(request: Request, cache: Cache = Depends(get_cache)):
    """
    生成并返回一个新的滑块验证码。

    - **challenge_id**: 验证码的唯一ID。
    - **background_image**: Base64编码的背景图。
    - **slider_image**: Base64编码的滑块图。
    - **slider_y**: 滑块在Y轴上的位置。
    - ...
    """
    challenge_id = f"captcha:{request.client.host}:{int(time.time() * 1000)}"
    captcha_data = generate_captcha_images()

    # 将正确的滑动位置（x坐标）存入缓存
    await cache.set(
        f"captcha:x:{challenge_id}", captcha_data["slider_x"], expire=CAPTCHA_EXPIRATION
    )

    response_data = {
        "challenge_id": challenge_id,
        "background_image": captcha_data["background_image"],
        "slider_image": captcha_data["slider_image"],
        "slider_y": captcha_data["slider_y"],
        "background_width": captcha_data["background_width"],
        "background_height": captcha_data["background_height"],
        "expires_in": CAPTCHA_EXPIRATION,
    }

    return JSONResponse(
        content={
            "success": True,
            "data": response_data,
            "message": "Captcha generated successfully.",
            "timestamp": time.time(),
        }
    )


@router.post("/slider/verify", summary="校验滑块验证码")
async def verify_slider_captcha(
    request: Request,
    cache: Cache = Depends(get_cache),
    challenge_id: str = None,
    slider_x: int = None,
):
    """
    验证用户滑动的结果。
    """
    if not all([challenge_id, slider_x is not None]):
        raise HTTPException(status_code=400, detail="Missing required parameters.")

    correct_x_str = await cache.get(f"captcha:x:{challenge_id}")
    if not correct_x_str:
        return JSONResponse(
            status_code=400,
            content={
                "success": False,
                "data": None,
                "message": "Captcha expired or invalid.",
            },
        )

    correct_x = int(correct_x_str)

    # 删除已使用的验证码，防止重放攻击
    await cache.delete(f"captcha:x:{challenge_id}")

    if abs(slider_x - correct_x) <= TOLERANCE:
        # 验证成功，生成一个临时的验证token
        verification_token = f"verified:{request.client.host}:{int(time.time() * 1000)}"
        await cache.set(
            f"captcha:token:{verification_token}",
            "true",
            expire=VERIFICATION_TOKEN_EXPIRATION,
        )

        return JSONResponse(
            content={
                "success": True,
                "data": {"verification_token": verification_token},
                "message": "Verification successful.",
                "timestamp": time.time(),
                "verification_token": verification_token,  # For compatibility
            }
        )
    else:
        return JSONResponse(
            status_code=400,
            content={"success": False, "data": None, "message": "Verification failed."},
        )


@router.post("/slider/validate-token", summary="校验验证码成功后的token")
async def validate_verification_token(token: str, cache: Cache = Depends(get_cache)):
    """
    在执行敏感操作（如登录、注册）前，校验前端提交的verification_token是否有效。
    """
    is_valid = await cache.get(f"captcha:token:{token}")
    if is_valid:
        # Token有效，消耗掉它，防止重复使用
        await cache.delete(f"captcha:token:{token}")
        return {"success": True, "message": "Token is valid."}
    else:
        return {"success": False, "message": "Token is invalid or expired."}
