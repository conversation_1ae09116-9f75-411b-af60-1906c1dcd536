#!/usr/bin/env python3
"""最小化版本的量化投资平台API - 不依赖外部数据库和库"""

import logging
import os
import secrets
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from fastapi import FastAPI, HTTPException, Request, Response, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI应用
app = FastAPI(
    title="量化投资平台 API (Minimal)",
    description="最小化版本 - 所有功能都使用模拟数据",
    version="1.0.0"
)

# 导入API扩展路由
try:
    from .api_extensions import router as api_extensions_router
    app.include_router(api_extensions_router)
    logger.info("API扩展路由已加载")
except ImportError:
    logger.warning("无法加载API扩展路由")

# 导入API修复路由
try:
    from .api_fix import router as api_fix_router
    app.include_router(api_fix_router)
    logger.info("API修复路由已加载")
except ImportError:
    logger.warning("无法加载API修复路由")

# 导入交易系统路由
try:
    from .trading_system import router as trading_router
    app.include_router(trading_router)
    logger.info("交易系统路由已加载")
except ImportError:
    logger.warning("无法加载交易系统路由")

# 导入数据操作路由
try:
    from data_operations import router as data_router
    app.include_router(data_router)
    logger.info("数据操作路由已加载")
except ImportError:
    logger.warning("无法加载数据操作路由")

# 安全头中间件
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)

    # 添加安全头
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "SAMEORIGIN"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

    # CSP头 - 允许本地开发
    csp_policy = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: blob:; "
        "connect-src 'self' ws://localhost:8000 http://localhost:8000; "
        "font-src 'self' data:; "
        "object-src 'none'; "
        "base-uri 'self';"
    )
    response.headers["Content-Security-Policy"] = csp_policy

    return response

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ============ 数据模型 ============

class UserLoginRequest(BaseModel):
    username: str
    password: str

class UserRegisterRequest(BaseModel):
    username: str
    email: str
    password: str
    confirmPassword: str = None

class StrategyCreateRequest(BaseModel):
    name: str
    description: str = ""
    code: str
    parameters: dict = {}

class StrategyControlRequest(BaseModel):
    action: str  # start, stop, pause

class OptimizationRequest(BaseModel):
    parameters: dict
    optimization_target: str = "sharpe_ratio"
    method: str = "grid_search"

# ============ 模拟数据存储 ============

# 用户数据
USERS_DB = {
    "admin": {
        "id": "admin_user",
        "username": "admin",
        "email": "<EMAIL>",
        "password": "admin123",
        "created_at": "2025-07-26T19:00:00"
    }
}

# 订单数据
ORDERS_DB = {}

# 策略数据
STRATEGIES_DB = {
    "strategy_1": {
        "id": "strategy_1",
        "name": "双均线策略",
        "description": "基于快慢均线交叉的趋势跟踪策略",
        "code": "def strategy_logic(data): return 1",
        "parameters": {"fast_period": 5, "slow_period": 20},
        "status": "stopped",
        "created_at": "2025-07-26T19:00:00",
        "performance": {"total_return": 0.15, "sharpe_ratio": 1.2, "max_drawdown": -0.08, "win_rate": 0.65}
    }
}

# 风控规则
RISK_RULES = {
    "position_limit": {
        "id": "position_limit",
        "name": "持仓限制规则",
        "description": "单只股票持仓不得超过总资产的10%",
        "type": "position",
        "parameters": {"max_weight": 0.10},
        "enabled": True,
        "severity": "high"
    }
}

# 缓存和任务队列
CACHE_STORAGE = {}
TASK_QUEUE = {}

# ============ 基础路由 ============

@app.get("/")
async def root():
    return {
        "message": "Welcome to Quant Platform API (Minimal)",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health():
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

# ============ 市场数据API ============

@app.get("/api/v1/market/stocks")
async def get_stocks(market: str = None, industry: str = None, pageSize: int = 100, page: int = 1):
    """获取股票列表"""
    try:
        mock_stocks = [
            {"symbol": "000001", "name": "平安银行", "currentPrice": 13.45, "changePercent": 1.2, "volume": 1000000, "market": "SZ"},
            {"symbol": "000002", "name": "万科A", "currentPrice": 8.95, "changePercent": -0.8, "volume": 800000, "market": "SZ"},
            {"symbol": "600036", "name": "招商银行", "currentPrice": 37.2, "changePercent": 0.5, "volume": 1200000, "market": "SH"},
            {"symbol": "600519", "name": "贵州茅台", "currentPrice": 1680.5, "changePercent": 2.1, "volume": 500000, "market": "SH"},
            {"symbol": "000858", "name": "五粮液", "currentPrice": 168.8, "changePercent": 1.8, "volume": 900000, "market": "SZ"},
        ]
        
        return {
            "success": True,
            "data": {
                "items": mock_stocks,
                "total": len(mock_stocks),
                "page": page,
                "pageSize": pageSize
            }
        }
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        return {"success": False, "message": "获取股票列表失败"}

@app.get("/api/v1/market/news")
async def get_market_news(limit: int = 10, category: str = None):
    """获取市场新闻"""
    try:
        mock_news = [
            {
                "id": "news_1",
                "title": "央行宣布降准0.5个百分点",
                "content": "央行今日宣布下调存款准备金率0.5个百分点，释放流动性约1万亿元。",
                "source": "央视新闻",
                "publishTime": "2025-07-30T10:00:00",
                "category": "政策"
            },
            {
                "id": "news_2", 
                "title": "科技股普涨，AI概念股领涨",
                "content": "今日科技股普遍上涨，AI概念股表现强势，多只个股涨停。",
                "source": "财经网",
                "publishTime": "2025-07-30T09:30:00",
                "category": "市场"
            },
            {
                "id": "news_3",
                "title": "新能源汽车销量创新高",
                "content": "7月新能源汽车销量同比增长50%，产业链相关股票受益。",
                "source": "汽车之家",
                "publishTime": "2025-07-30T08:00:00",
                "category": "行业"
            }
        ]
        
        filtered_news = mock_news[:limit]
        if category:
            filtered_news = [news for news in filtered_news if news["category"] == category]
            
        return {
            "success": True,
            "data": {
                "news": filtered_news,
                "total": len(filtered_news)
            }
        }
    except Exception as e:
        logger.error(f"获取市场新闻失败: {e}")
        return {"success": False, "message": "获取市场新闻失败"}

@app.get("/api/v1/market/rankings")
async def get_market_rankings(type: str = "change_percent", limit: int = 50):
    """获取市场排行榜"""
    try:
        import random

        # 模拟股票数据
        stock_symbols = [
            "000001", "000002", "000858", "600036", "600519", "000063", "002415",
            "300059", "600000", "000166", "002304", "000725", "600276", "002142",
            "000568", "600887", "002230", "000876", "600031", "000977"
        ]

        stock_names = [
            "平安银行", "万科A", "五粮液", "招商银行", "贵州茅台", "中兴通讯", "海康威视",
            "东方财富", "浦发银行", "申万宏源", "洋河股份", "宁德时代", "恒瑞医药", "宁波银行",
            "泸州老窖", "伊利股份", "科大讯飞", "新希望", "三一重工", "浪潮信息"
        ]

        rankings = []
        for i in range(min(limit, len(stock_symbols))):
            symbol = stock_symbols[i]
            name = stock_names[i]

            if type == "change_percent":
                # 涨跌幅排行
                change_percent = random.uniform(-10, 10)
                current_price = random.uniform(10, 100)
                change = current_price * change_percent / 100

                rankings.append({
                    "symbol": symbol,
                    "name": name,
                    "currentPrice": round(current_price, 2),
                    "change": round(change, 2),
                    "changePercent": round(change_percent, 2),
                    "volume": random.randint(1000000, ********0),
                    "turnover": random.randint(********0, ********000)
                })
            elif type == "turnover":
                # 成交额排行
                turnover = random.randint(********00, 50000000000)
                current_price = random.uniform(10, 100)
                change_percent = random.uniform(-5, 5)
                change = current_price * change_percent / 100

                rankings.append({
                    "symbol": symbol,
                    "name": name,
                    "currentPrice": round(current_price, 2),
                    "change": round(change, 2),
                    "changePercent": round(change_percent, 2),
                    "volume": random.randint(********, 500000000),
                    "turnover": turnover
                })

        # 按类型排序
        if type == "change_percent":
            rankings.sort(key=lambda x: x["changePercent"], reverse=True)
        elif type == "turnover":
            rankings.sort(key=lambda x: x["turnover"], reverse=True)

        return rankings

    except Exception as e:
        logger.error(f"获取排行榜失败: {e}")
        return {"success": False, "message": "获取排行榜失败"}

@app.get("/api/v1/market/watchlist")
async def get_watchlist():
    """获取自选股列表"""
    try:
        mock_watchlist = [
            {"symbol": "000001", "name": "平安银行", "addTime": "2025-07-30T09:00:00"},
            {"symbol": "600036", "name": "招商银行", "addTime": "2025-07-29T15:30:00"},
            {"symbol": "600519", "name": "贵州茅台", "addTime": "2025-07-28T10:20:00"}
        ]
        
        return {
            "success": True,
            "data": {
                "watchlist": mock_watchlist,
                "total": len(mock_watchlist)
            }
        }
    except Exception as e:
        logger.error(f"获取自选股列表失败: {e}")
        return {"success": False, "message": "获取自选股列表失败"}

@app.post("/api/v1/market/watchlist")
async def add_to_watchlist(request: dict):
    """添加股票到自选股"""
    try:
        symbol = request.get("symbol")
        if not symbol:
            return {"success": False, "message": "股票代码不能为空"}
            
        return {"success": True, "message": f"已添加 {symbol} 到自选股"}
    except Exception as e:
        logger.error(f"添加自选股失败: {e}")
        return {"success": False, "message": "添加自选股失败"}

@app.delete("/api/v1/market/watchlist/{symbol}")
async def remove_from_watchlist(symbol: str):
    """从自选股移除股票"""
    try:
        return {"success": True, "message": f"已从自选股移除 {symbol}"}
    except Exception as e:
        logger.error(f"移除自选股失败: {e}")
        return {"success": False, "message": "移除自选股失败"}

@app.get("/api/v1/market/quote")
async def get_stock_quote(symbols: str = None):
    """获取股票行情"""
    try:
        if not symbols:
            return {"success": False, "message": "股票代码不能为空"}
            
        symbol_list = symbols.split(",")
        mock_quotes = []
        
        for symbol in symbol_list:
            quote = {
                "symbol": symbol.strip(),
                "name": f"股票{symbol}",
                "currentPrice": 10.0 + (hash(symbol) % 100),
                "changePercent": (hash(symbol) % 200 - 100) / 100,
                "volume": 1000000 + (hash(symbol) % 500000),
                "high": 10.5 + (hash(symbol) % 100),
                "low": 9.8 + (hash(symbol) % 100),
                "open": 10.2 + (hash(symbol) % 100),
                "updateTime": datetime.now().isoformat()
            }
            mock_quotes.append(quote)
            
        return {
            "success": True,
            "data": mock_quotes
        }
    except Exception as e:
        logger.error(f"获取股票行情失败: {e}")
        return {"success": False, "message": "获取股票行情失败"}

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览"""
    try:
        return {
            "success": True,
            "data": {
                "indices": [
                    {"symbol": "000001.SH", "name": "上证指数", "value": 3245.67, "change": 1.2},
                    {"symbol": "399001.SZ", "name": "深证成指", "value": 10567.89, "change": 0.8},
                    {"symbol": "399006.SZ", "name": "创业板指", "value": 2145.32, "change": -0.5}
                ],
                "marketStats": {
                    "totalStocks": 4500,
                    "risers": 2100,
                    "fallers": 1800,
                    "unchanged": 600,
                    "totalVolume": 520000000000,
                    "totalValue": 12000000000000
                }
            }
        }
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        return {"success": False, "message": "获取市场概览失败"}

@app.get("/api/v1/market/sectors")
async def get_market_sectors():
    """获取市场板块数据"""
    try:
        import random

        # 模拟板块数据
        sectors = [
            {
                "code": "BK001",
                "name": "科技",
                "changePercent": round(random.uniform(-3, 5), 2),
                "stocksCount": 156,
                "marketCap": 2500000000000,
                "leadingStock": "000063",
                "leadingStockName": "中兴通讯",
                "volume": random.randint(********00, 5000000000),
                "turnover": random.randint(50000000000, 200000000000)
            },
            {
                "code": "BK002",
                "name": "金融",
                "changePercent": round(random.uniform(-2, 3), 2),
                "stocksCount": 89,
                "marketCap": 1800000000000,
                "leadingStock": "600036",
                "leadingStockName": "招商银行",
                "volume": random.randint(800000000, 3000000000),
                "turnover": random.randint(40000000000, 150000000000)
            },
            {
                "code": "BK003",
                "name": "医药",
                "changePercent": round(random.uniform(-1, 4), 2),
                "stocksCount": 234,
                "marketCap": 1200000000000,
                "leadingStock": "600276",
                "leadingStockName": "恒瑞医药",
                "volume": random.randint(600000000, 2500000000),
                "turnover": random.randint(30000000000, 120000000000)
            },
            {
                "code": "BK004",
                "name": "消费",
                "changePercent": round(random.uniform(-2, 3), 2),
                "stocksCount": 178,
                "marketCap": 980000000000,
                "leadingStock": "600519",
                "leadingStockName": "贵州茅台",
                "volume": random.randint(400000000, 2000000000),
                "turnover": random.randint(25000000000, ********0000)
            },
            {
                "code": "BK005",
                "name": "新能源",
                "changePercent": round(random.uniform(-1, 6), 2),
                "stocksCount": 67,
                "marketCap": 750000000000,
                "leadingStock": "300750",
                "leadingStockName": "宁德时代",
                "volume": random.randint(500000000, 3000000000),
                "turnover": random.randint(35000000000, 180000000000)
            },
            {
                "code": "BK006",
                "name": "军工",
                "changePercent": round(random.uniform(-2, 4), 2),
                "stocksCount": 45,
                "marketCap": 450000000000,
                "leadingStock": "002415",
                "leadingStockName": "海康威视",
                "volume": random.randint(300000000, 1500000000),
                "turnover": random.randint(20000000000, 80000000000)
            },
            {
                "code": "BK007",
                "name": "房地产",
                "changePercent": round(random.uniform(-4, 2), 2),
                "stocksCount": 123,
                "marketCap": 650000000000,
                "leadingStock": "000002",
                "leadingStockName": "万科A",
                "volume": random.randint(400000000, 2000000000),
                "turnover": random.randint(15000000000, 70000000000)
            },
            {
                "code": "BK008",
                "name": "5G通信",
                "changePercent": round(random.uniform(-2, 5), 2),
                "stocksCount": 89,
                "marketCap": 580000000000,
                "leadingStock": "000063",
                "leadingStockName": "中兴通讯",
                "volume": random.randint(350000000, 1800000000),
                "turnover": random.randint(25000000000, 90000000000)
            }
        ]

        return {
            "success": True,
            "data": sectors,
            "timestamp": datetime.now().isoformat(),
            "total": len(sectors)
        }
    except Exception as e:
        logger.error(f"获取板块数据失败: {e}")
        return {"success": False, "message": "获取板块数据失败"}

# ============ 认证API ============

@app.post("/api/v1/auth/login")
async def login_user(request: UserLoginRequest):
    """用户登录"""
    try:
        if request.username not in USERS_DB:
            return {"success": False, "message": "用户名或密码错误"}
        
        user_data = USERS_DB[request.username]
        if user_data["password"] != request.password:
            return {"success": False, "message": "用户名或密码错误"}
        
        token = f"token_{secrets.token_hex(16)}"
        return {
            "success": True,
            "data": {
                "token": token,
                "user": {
                    "id": user_data["id"],
                    "username": user_data["username"],
                    "email": user_data["email"]
                }
            },
            "message": "登录成功"
        }
    except Exception as e:
        logger.error(f"登录失败: {e}")
        return {"success": False, "message": "登录失败"}

# 删除错误的 /api/auth/register 路径

# ============ 验证码API ============

@app.get("/api/v1/captcha/slider")
async def get_slider_captcha():
    """获取滑块验证码（模拟）"""
    captcha_id = f"captcha_{secrets.token_hex(8)}"
    return {
        "success": True,
        "data": {
            "id": captcha_id,
            "background_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "slider_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
            "y": 50,
            "h": 40
        },
        "message": "验证码生成成功"
    }

@app.post("/api/v1/captcha/slider/verify")
async def verify_slider_captcha(data: dict):
    """验证滑块验证码（模拟）"""
    return {
        "success": True,
        "data": {"token": f"verify_token_{secrets.token_hex(16)}"},
        "message": "验证成功"
    }

# ============ 市场数据API ============

@app.get("/api/v1/market/quote/{symbol}")
async def get_quote(symbol: str):
    """获取股票报价（模拟数据）"""
    import random
    base_price = 50.0
    change = random.uniform(-2.0, 2.0)
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "name": f"股票{symbol}",
            "price": round(base_price + change, 2),
            "change": round(change, 2),
            "change_percent": round(change / base_price * 100, 2),
            "volume": random.randint(100000, 1000000),
            "turnover": random.randint(1000000, ********),
            "high": round(base_price + change + 1, 2),
            "low": round(base_price + change - 1, 2),
            "open": round(base_price, 2),
            "prev_close": round(base_price, 2),
            "timestamp": datetime.now().isoformat()
        }
    }

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline(symbol: str, period: str = "1d", limit: int = 100):
    """获取K线数据（模拟数据）"""
    import random
    
    klines = []
    base_price = 50.0
    current_price = base_price
    
    for i in range(limit):
        date = datetime.now() - timedelta(days=limit-i)
        
        # 模拟价格波动
        change = random.uniform(-2.0, 2.0)
        current_price = max(10.0, current_price + change)
        
        high = current_price + random.uniform(0, 1)
        low = current_price - random.uniform(0, 1)
        close = current_price + random.uniform(-0.5, 0.5)
        
        klines.append({
            "timestamp": date.strftime("%Y-%m-%d %H:%M:%S"),
            "open": round(current_price, 2),
            "high": round(high, 2),
            "low": round(low, 2),
            "close": round(close, 2),
            "volume": random.randint(100000, 1000000),
            "turnover": random.randint(1000000, ********)
        })
    
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "period": period,
            "klines": klines
        }
    }

# ============ 交易API ============

@app.get("/api/v1/trading/account")
async def get_account_info():
    """获取账户信息（模拟数据）"""
    return {
        "success": True,
        "data": {
            "accountId": "10001",
            "totalAssets": 1000000.00,
            "availableCash": 800000.00,
            "marketValue": 150000.00,
            "frozenCash": 50000.00,
            "totalProfit": 50000.00,
            "totalProfitRate": 5.0,
            "dayProfit": 5000.00,
            "dayProfitRate": 0.5
        }
    }

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单列表（模拟数据）"""
    return {
        "success": True,
        "data": {
            "orders": [
                {
                    "id": "ORDER_001",
                    "symbol": "000001.SZ",
                    "side": "buy",
                    "price": 15.50,
                    "quantity": 1000,
                    "status": "filled",
                    "created_at": "2025-07-26T09:30:00"
                }
            ],
            "total": 1
        }
    }

@app.get("/api/v1/trading/positions")
async def get_positions():
    """获取持仓列表（模拟数据）"""
    positions = [
        {
            "symbol": "000001",
            "name": "平安银行",
                "quantity": 10000,
                "available_quantity": 8000,
                "frozen_quantity": 2000,
                "avg_cost": 12.5,
                "current_price": 13.45,
                "market_value": 134500.0,
                "cost_value": 125000.0,
                "unrealized_pnl": 9500.0,
                "unrealized_pnl_ratio": 0.076,
                "position_ratio": 0.089,
                "side": "long",
                "updated_at": datetime.now().isoformat()
            },
            {
                "symbol": "600519",
                "name": "贵州茅台",
                "quantity": 500,
                "available_quantity": 500,
                "frozen_quantity": 0,
                "avg_cost": 1680.0,
                "current_price": 1653.0,
                "market_value": 826500.0,
                "cost_value": 840000.0,
                "unrealized_pnl": -13500.0,
                "unrealized_pnl_ratio": -0.016,
                "position_ratio": 0.551,
                "side": "long",
                "updated_at": datetime.now().isoformat()
            },
            {
                "symbol": "300059",
                "name": "东方财富",
                "quantity": 5000,
                "available_quantity": 5000,
                "frozen_quantity": 0,
                "avg_cost": 24.8,
                "current_price": 26.15,
                "market_value": 130750.0,
                "cost_value": 124000.0,
                "unrealized_pnl": 6750.0,
                "unrealized_pnl_ratio": 0.054,
                "position_ratio": 0.087,
                "side": "long",
                "updated_at": datetime.now().isoformat()
            }
        ]
    
    # 转换格式以匹配前端期望
    frontend_positions = []
    for pos in positions:
        frontend_positions.append({
            "symbol": pos["symbol"],
            "name": pos["name"],
            "quantity": pos["quantity"],
            "availableQuantity": pos["available_quantity"],
            "avgPrice": pos["avg_cost"],
            "currentPrice": pos["current_price"],
            "marketValue": pos["market_value"],
            "profit": pos["unrealized_pnl"],
            "profitRate": pos["unrealized_pnl_ratio"] * 100,
            "dayProfit": pos["unrealized_pnl"] * 0.1,  # 模拟今日盈亏
            "dayProfitRate": pos["unrealized_pnl_ratio"] * 10  # 模拟今日盈亏率
        })
    
    return {
        "success": True,
        "data": frontend_positions
    }

# ============ 策略系统API ============

@app.get("/api/v1/strategies")
async def get_strategies(page: int = 1, limit: int = 20, status: str = None):
    """获取策略列表"""
    strategies = list(STRATEGIES_DB.values())
    if status:
        strategies = [s for s in strategies if s["status"] == status]
    
    return {
        "success": True,
        "data": {
            "strategies": strategies,
            "total": len(strategies),
            "page": page,
            "limit": limit
        }
    }

@app.get("/api/v1/strategies/{strategy_id}")
async def get_strategy(strategy_id: str):
    """获取策略详情"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    return {"success": True, "data": STRATEGIES_DB[strategy_id]}

@app.post("/api/v1/strategies")
async def create_strategy(request: StrategyCreateRequest):
    """创建新策略"""
    strategy_id = f"strategy_{len(STRATEGIES_DB) + 1}"
    new_strategy = {
        "id": strategy_id,
        "name": request.name,
        "description": request.description,
        "code": request.code,
        "parameters": request.parameters,
        "status": "stopped",
        "created_at": datetime.now().isoformat(),
        "performance": {"total_return": 0.0, "sharpe_ratio": 0.0, "max_drawdown": 0.0, "win_rate": 0.0}
    }
    STRATEGIES_DB[strategy_id] = new_strategy
    return {"success": True, "data": new_strategy, "message": "策略创建成功"}

@app.post("/api/v1/strategies/{strategy_id}/control")
async def control_strategy(strategy_id: str, request: StrategyControlRequest):
    """控制策略运行状态"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    strategy = STRATEGIES_DB[strategy_id]
    action = request.action.lower()
    
    if action == "start":
        strategy["status"] = "running"
        message = "策略启动成功"
    elif action == "stop":
        strategy["status"] = "stopped"
        message = "策略停止成功"
    elif action == "pause":
        strategy["status"] = "paused"
        message = "策略暂停成功"
    else:
        raise HTTPException(status_code=400, detail="无效的操作类型")
    
    return {"success": True, "data": strategy, "message": message}

@app.get("/api/v1/strategies/{strategy_id}/performance")
async def get_strategy_performance(strategy_id: str):
    """获取策略绩效分析"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    strategy = STRATEGIES_DB[strategy_id]
    return {
        "success": True,
        "data": {
            "strategy_id": strategy_id,
            "strategy_name": strategy["name"],
            "performance": strategy["performance"],
            "analysis_period": {"start_date": "2025-01-01", "end_date": "2025-07-26"},
            "risk_metrics": {
                "sharpe_ratio": strategy["performance"]["sharpe_ratio"],
                "max_drawdown": strategy["performance"]["max_drawdown"],
                "volatility": 0.15,
                "var_95": -0.025
            }
        }
    }

@app.post("/api/v1/strategies/{strategy_id}/optimize")
async def optimize_strategy_parameters(strategy_id: str, request: OptimizationRequest):
    """优化策略参数"""
    if strategy_id not in STRATEGIES_DB:
        raise HTTPException(status_code=404, detail="策略不存在")
    
    import random
    best_params = {}
    for param, values in request.parameters.items():
        if isinstance(values, list):
            best_params[param] = random.choice(values)
    
    return {
        "success": True,
        "data": {
            "strategy_id": strategy_id,
            "best_parameters": best_params,
            "best_score": 1.45,
            "optimization_time": "00:05:30"
        },
        "message": "参数优化完成"
    }

# ============ 风险管理API ============

@app.get("/api/v1/risk/report")
async def generate_risk_report():
    """生成风险报告"""
    return {
        "success": True,
        "data": {
            "report_meta": {
                "id": f"risk_report_{int(time.time())}",
                "generated_at": datetime.now().isoformat(),
                "period": {"start_date": "2025-01-01", "end_date": "2025-07-26"}
            },
            "portfolio_overview": {
                "total_value": 12500000.0,
                "asset_allocation": {"stocks": 0.65, "bonds": 0.25, "cash": 0.10}
            },
            "risk_metrics": {
                "var_1d_95": -125000,
                "max_drawdown": -0.08,
                "volatility": 0.15
            },
            "compliance_status": "合规"
        }
    }

@app.get("/api/v1/risk/rules")
async def get_risk_rules():
    """获取风控规则列表"""
    return {
        "success": True,
        "data": {"rules": list(RISK_RULES.values()), "total": len(RISK_RULES)}
    }

@app.post("/api/v1/risk/check")
async def check_risk_compliance():
    """实时风控检查"""
    return {
        "success": True,
        "data": {
            "compliance_status": "合规",
            "risk_score": 25,
            "violations": [],
            "warnings": [],
            "checked_rules": len(RISK_RULES),
            "check_time": datetime.now().isoformat()
        }
    }

# ============ 缓存和任务队列API ============

@app.get("/api/v1/cache/status")
async def get_cache_status():
    """获取缓存状态"""
    return {
        "success": True,
        "data": {
            "type": "memory_cache",
            "status": "active",
            "keys_count": len(CACHE_STORAGE),
            "hit_rate": 0.85
        }
    }

@app.post("/api/v1/tasks/submit")
async def submit_task(data: dict):
    """提交异步任务"""
    task_id = f"task_{int(time.time())}_{len(TASK_QUEUE)}"
    task = {
        "id": task_id,
        "type": data.get("task_type", "unknown"),
        "status": "running",
        "created_at": datetime.now().isoformat(),
        "result": None
    }
    TASK_QUEUE[task_id] = task
    return {"success": True, "data": {"task_id": task_id, "status": "running"}}

@app.get("/api/v1/tasks/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in TASK_QUEUE:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = TASK_QUEUE[task_id]
    # 模拟任务完成
    import random
    if task["status"] == "running" and random.random() > 0.5:
        task["status"] = "completed"
        task["result"] = {"total_return": 0.15, "sharpe_ratio": 1.2}
    
    return {"success": True, "data": task}

# ============ 回测API ============

@app.post("/api/v1/strategies/backtest")
async def run_backtest(data: dict):
    """运行策略回测"""
    strategy_id = data.get("strategy_id", "strategy_1")
    
    # 模拟回测结果
    import random
    result = {
        "strategy_id": strategy_id,
        "period": {"start_date": "2025-01-01", "end_date": "2025-07-26"},
        "performance": {
            "total_return": round(random.uniform(0.05, 0.25), 3),
            "annual_return": round(random.uniform(0.10, 0.50), 3),
            "sharpe_ratio": round(random.uniform(0.8, 2.0), 2),
            "max_drawdown": round(random.uniform(-0.15, -0.05), 3),
            "win_rate": round(random.uniform(0.45, 0.75), 2)
        },
        "trades": {
            "total_trades": random.randint(50, 200),
            "profitable_trades": random.randint(30, 120),
            "avg_profit": round(random.uniform(0.01, 0.05), 3)
        }
    }
    
    return {"success": True, "data": result, "message": "回测完成"}

# ============ WebSocket ============

# WebSocket连接管理
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending message: {e}")
            self.disconnect(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections.copy():
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")
                self.disconnect(connection)

manager = ConnectionManager()

@app.websocket("/api/v1/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket主端点"""
    await manager.connect(websocket)
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            message_data = json.loads(data)
            
            # 处理不同类型的消息
            if message_data.get("type") == "subscribe":
                # 订阅实时数据
                symbols = message_data.get("symbols", [])
                response = {
                    "type": "subscription_confirmed",
                    "symbols": symbols,
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(response), websocket)
                
            elif message_data.get("type") == "ping":
                # 心跳检测
                response = {
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(response), websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

@app.websocket("/api/v1/ws/market")
async def market_websocket(websocket: WebSocket):
    """市场数据WebSocket"""
    await manager.connect(websocket)
    try:
        # 模拟发送实时行情数据
        import asyncio
        while True:
            mock_quote = {
                "type": "quote",
                "symbol": "000001",
                "name": "平安银行",
                "price": 13.45 + (time.time() % 10 - 5) * 0.01,
                "change": 0.12,
                "changePercent": 0.89,
                "volume": 1000000,
                "timestamp": datetime.now().isoformat()
            }
            await manager.send_personal_message(json.dumps(mock_quote), websocket)
            await asyncio.sleep(5)  # 每5秒发送一次数据
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"Market WebSocket error: {e}")
        manager.disconnect(websocket)

@app.websocket("/api/v1/ctp/ws")
async def ctp_websocket(websocket: WebSocket):
    """CTP交易WebSocket"""
    await manager.connect(websocket)
    try:
        # 发送连接确认
        welcome_message = {
            "type": "connection_established",
            "message": "CTP WebSocket连接成功",
            "timestamp": datetime.now().isoformat(),
            "status": "connected"
        }
        await manager.send_personal_message(json.dumps(welcome_message), websocket)

        import asyncio
        while True:
            # 接收客户端消息
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=1.0)
                message_data = json.loads(data)

                # 处理不同类型的CTP消息
                if message_data.get("type") == "login":
                    # 模拟登录响应
                    response = {
                        "type": "login_response",
                        "success": True,
                        "user_id": message_data.get("user_id", "test_user"),
                        "broker_id": message_data.get("broker_id", "test_broker"),
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response), websocket)

                elif message_data.get("type") == "order_update":
                    # 模拟订单更新
                    response = {
                        "type": "ctp_order_update",
                        "order_id": message_data.get("order_id"),
                        "status": "FILLED",
                        "filled_quantity": message_data.get("quantity", 0),
                        "timestamp": datetime.now().isoformat()
                    }
                    await manager.send_personal_message(json.dumps(response), websocket)

            except asyncio.TimeoutError:
                # 发送心跳
                heartbeat = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                }
                await manager.send_personal_message(json.dumps(heartbeat), websocket)

    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("CTP WebSocket disconnected")
    except Exception as e:
        logger.error(f"CTP WebSocket error: {e}")
        manager.disconnect(websocket)

# ============ 用户管理API ============

@app.get("/api/v1/auth/me")
async def get_current_user():
    """获取当前用户信息"""
    return {
        "success": True,
        "data": {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "role": "user",
            "created_at": "2024-01-01T00:00:00Z"
        }
    }

@app.get("/api/v1/auth/user/profile")
async def get_user_profile():
    """获取用户详细资料"""
    return {
        "success": True,
        "data": {
            "id": 1,
            "username": "demo_user",
            "email": "<EMAIL>",
            "nickname": "演示用户",
            "avatar": "",
            "phone": "",
            "role": "user",
            "status": "active",
            "last_login": "2025-07-30T16:00:00Z",
            "created_at": "2024-01-01T00:00:00Z",
            "settings": {
                "theme": "light",
                "language": "zh-CN",
                "notifications": True
            }
        }
    }

@app.post("/api/v1/auth/register")
async def register_user(request: UserRegisterRequest):
    """用户注册"""
    try:
        if request.username in USERS_DB:
            return {"success": False, "message": "用户名已存在"}
        
        user_id = len(USERS_DB) + 1
        USERS_DB[request.username] = {
            "id": user_id,
            "username": request.username,
            "email": request.email,
            "password": request.password,
            "created_at": datetime.now().isoformat()
        }
        
        return {
            "success": True,
            "data": {
                "id": user_id,
                "username": request.username,
                "email": request.email
            },
            "message": "注册成功"
        }
    except Exception as e:
        logger.error(f"注册失败: {e}")
        return {"success": False, "message": "注册失败"}

@app.post("/api/v1/auth/logout")
async def logout_user():
    """用户登出"""
    return {"success": True, "message": "登出成功"}

@app.post("/api/v1/auth/refresh")
async def refresh_token():
    """刷新Token"""
    new_token = f"token_{secrets.token_hex(16)}"
    return {
        "success": True,
        "data": {"token": new_token},
        "message": "Token刷新成功"
    }

# ============ 交易相关API ============

@app.post("/api/v1/trading/orders")
async def create_order(request: Request):
    """提交订单"""
    try:
        data = await request.json()
        
        # 参数验证
        symbol = data.get("symbol")
        side = data.get("side")
        quantity = data.get("quantity")
        price = data.get("price", 10.0)
        
        # 必填参数检查
        if not symbol:
            return {"success": False, "message": "股票代码不能为空"}
        if not side or side not in ["buy", "sell"]:
            return {"success": False, "message": "交易方向必须是buy或sell"}
        if not quantity or not isinstance(quantity, (int, float)) or quantity <= 0:
            return {"success": False, "message": "交易数量必须大于0"}
        if not isinstance(price, (int, float)) or price <= 0:
            return {"success": False, "message": "价格必须大于0"}
        
        # 股票代码格式验证
        import re
        if not re.match(r'^[0-9]{6}$', symbol):
            return {"success": False, "message": "股票代码格式错误，应为6位数字"}
        
        order_id = f"order_{secrets.token_hex(8)}"
        
        # 构建符合前端期望的订单响应
        order = {
            "orderId": order_id,
            "symbol": symbol,
            "side": side,
            "type": data.get("type", "limit"),
            "quantity": quantity,
            "price": price,
            "status": "submitted",
            "createdAt": datetime.now().isoformat(),
            "filledQuantity": 0,
            "avgPrice": 0
        }
        
        # 将订单保存到内存中
        ORDERS_DB[order_id] = order
        
        return {
            "success": True,
            "data": order,
            "message": "订单提交成功"
        }
    except Exception as e:
        logger.error(f"提交订单失败: {e}")
        return {"success": False, "message": "提交订单失败"}

@app.delete("/api/v1/trading/orders/{order_id}")
async def cancel_order(order_id: str):
    """取消订单"""
    return {
        "success": True,
        "data": {"order_id": order_id, "status": "cancelled"},
        "message": "订单取消成功"
    }

# ============ 投资组合API ============

@app.get("/api/v1/portfolio/overview")
async def get_portfolio_overview():
    """获取投资组合概览"""
    return {
        "success": True,
        "data": {
            "total_value": 1000000.00,
            "available_cash": 200000.00,
            "total_profit": 50000.00,
            "profit_rate": 0.05,
            "positions_count": 8
        }
    }

@app.get("/api/v1/portfolio/positions")
async def get_portfolio_positions():
    """获取持仓列表"""
    return {
        "success": True,
        "data": {
            "positions": [
                {
                    "symbol": "000001",
                    "name": "平安银行",
                    "quantity": 1000,
                    "avg_cost": 12.50,
                    "current_price": 13.20,
                    "market_value": 13200.00,
                    "profit": 700.00,
                    "profit_rate": 0.056
                }
            ]
        }
    }

# ============ 风险管理API ============

@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    return {
        "success": True,
        "data": {
            "var_95": -0.025,
            "max_drawdown": -0.08,
            "sharpe_ratio": 1.2,
            "beta": 0.9,
            "volatility": 0.15
        }
    }

@app.get("/api/v1/risk/limits")
async def get_risk_limits():
    """获取风险限额"""
    return {
        "success": True,
        "data": {
            "max_position_size": 100000,
            "max_daily_loss": -10000,
            "max_leverage": 2.0,
            "sector_concentration": 0.3
        }
    }

# ============ 监控和系统API ============

@app.get("/api/v1/metrics")
async def get_system_metrics():
    """获取系统监控指标"""
    import psutil
    import sys
    
    try:
        # CPU和内存信息
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        
        return {
            "success": True,
            "data": {
                "system": {
                    "cpu_usage": cpu_percent,
                    "memory_usage": memory.percent,
                    "memory_total": memory.total,
                    "memory_available": memory.available,
                    "python_version": sys.version,
                    "uptime": time.time() - 1722355200  # 简单的运行时间
                },
                "api": {
                    "total_requests": 1000,
                    "error_rate": 0.02,
                    "avg_response_time": 150,
                    "active_connections": 10
                },
                "business": {
                    "active_strategies": len([s for s in STRATEGIES_DB.values() if s["status"] == "running"]),
                    "total_users": len(USERS_DB),
                    "total_trades": 156,
                    "cache_hit_rate": 0.85
                }
            }
        }
    except ImportError:
        # 如果psutil不可用，返回模拟数据
        return {
            "success": True,
            "data": {
                "system": {
                    "cpu_usage": 15.5,
                    "memory_usage": 67.2,
                    "memory_total": 8589934592,  # 8GB
                    "memory_available": 2818572288,  # ~2.6GB
                    "python_version": sys.version,
                    "uptime": 3600
                },
                "api": {
                    "total_requests": 1000,
                    "error_rate": 0.02,
                    "avg_response_time": 150,
                    "active_connections": 10
                },
                "business": {
                    "active_strategies": 0,
                    "total_users": 1,
                    "total_trades": 156,
                    "cache_hit_rate": 0.85
                }
            }
        }

@app.get("/api/v1/status")
async def get_system_status():
    """获取系统状态"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "environment": "development",
            "services": {
                "database": "connected",
                "cache": "connected", 
                "market_data": "connected",
                "websocket": "running"
            },
            "features": {
                "trading": True,
                "strategies": True,
                "backtest": True,
                "risk_management": True,
                "real_time_data": True
            }
        }
    }

@app.get("/api/v1/logs")
async def get_system_logs(level: str = "INFO", limit: int = 100):
    """获取系统日志"""
    # 模拟日志数据
    logs = []
    log_levels = ["INFO", "WARNING", "ERROR", "DEBUG"]
    
    for i in range(min(limit, 50)):
        logs.append({
            "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
            "level": log_levels[i % len(log_levels)],
            "module": f"module_{i % 5}",
            "message": f"示例日志消息 {i}",
            "user_id": "admin" if i % 3 == 0 else None,
            "request_id": f"req_{i:04d}"
        })
    
    return {
        "success": True,
        "data": {
            "logs": [log for log in logs if level.upper() in log["level"]],
            "total": len(logs),
            "level": level,
            "limit": limit
        }
    }

# ============ 异常处理 ============

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"Unexpected error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "internal_error", "message": str(exc)}
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)