apiVersion: v1
kind: ConfigMap
metadata:
  name: quant-config
  namespace: quant-platform
data:
  # 应用配置
  ENVIRONMENT: "production"
  LOG_LEVEL: "INFO"
  
  # API配置
  API_BASE_URL: "http://quant-backend-service:8000"
  FRONTEND_URL: "http://quant-frontend-service"
  
  # 数据库配置（非敏感信息）
  DATABASE_HOST: "postgres-service"
  DATABASE_PORT: "5432"
  DATABASE_NAME: "quant_platform"
  
  # Redis配置
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"
  
  # WebSocket配置
  WS_HEARTBEAT_INTERVAL: "30000"
  WS_RECONNECT_ATTEMPTS: "5"