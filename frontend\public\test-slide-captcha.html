<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>滑动验证码测试</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f5f7fa;
    }
    
    .container {
      max-width: 400px;
      margin: 50px auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    }
    
    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }
    
    .test-info {
      background: #f0f9ff;
      border: 1px solid #bae6fd;
      border-radius: 6px;
      padding: 15px;
      margin-bottom: 20px;
      font-size: 14px;
      color: #0369a1;
    }
    
    .result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 6px;
      font-weight: 500;
      text-align: center;
    }
    
    .result.success {
      background: #f0f9ff;
      color: #059669;
      border: 1px solid #67c23a;
    }
    
    .result.fail {
      background: #fef0f0;
      color: #dc2626;
      border: 1px solid #f56c6c;
    }
    
    .controls {
      margin-top: 20px;
      text-align: center;
    }
    
    .btn {
      background: #409eff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 0 5px;
      font-size: 14px;
    }
    
    .btn:hover {
      background: #337ecc;
    }
    
    .btn.secondary {
      background: #909399;
    }
    
    .btn.secondary:hover {
      background: #73767a;
    }

    /* 简单的验证码容器样式 */
    .captcha-container {
      margin: 20px 0;
    }
    
    .slide-verify {
      width: 100%;
      max-width: 350px;
      margin: 0 auto;
    }
    
    .slide-verify-block {
      position: relative;
      background: #f5f7fa;
      border-radius: 8px;
      overflow: hidden;
      border: 1px solid #e4e7ed;
      margin-bottom: 15px;
    }
    
    .slide-verify-canvas, .slide-verify-block-canvas {
      display: block;
      width: 100%;
      height: auto;
    }
    
    .slide-verify-block-canvas {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
    }
    
    .slide-verify-refresh-icon {
      position: absolute;
      top: 8px;
      right: 8px;
      width: 32px;
      height: 32px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 3;
      font-size: 16px;
      color: #606266;
    }
    
    .slide-verify-slider {
      position: relative;
      text-align: center;
      width: 100%;
      height: 40px;
      line-height: 40px;
      background: #f7f9fa;
      color: #45494c;
      border: 1px solid #e4e7ed;
      border-radius: 20px;
      font-size: 14px;
    }
    
    .slide-verify-slider-btn {
      position: absolute;
      top: -1px;
      left: 0;
      width: 38px;
      height: 38px;
      background: #fff;
      border: 1px solid #ccc;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
      font-size: 16px;
      color: #666;
    }
    
    .slide-verify-slider-mask {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background: #d1e9fe;
      border-radius: 20px;
      width: 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔒 滑动验证码测试</h1>
    
    <div class="test-info">
      <strong>测试说明：</strong><br>
      1. 向右拖动滑块完成拼图验证<br>
      2. 验证成功会显示绿色提示<br>
      3. 验证失败会显示红色提示并自动重置<br>
      4. 点击刷新按钮可以获取新的验证码
    </div>
    
    <div class="captcha-container">
      <div id="slide-captcha">
        <!-- 这里会插入Vue组件 -->
        <div class="slide-verify">
          <div class="slide-verify-block">
            <div class="slide-verify-refresh-icon" onclick="refreshCaptcha()">
              ↻
            </div>
            <canvas id="canvas" width="310" height="155" class="slide-verify-canvas"></canvas>
            <canvas id="block" width="310" height="155" class="slide-verify-block-canvas"></canvas>
          </div>
          <div class="slide-verify-slider">
            <div class="slide-verify-slider-mask" id="mask"></div>
            <div class="slide-verify-slider-btn" id="slider">→</div>
            <span>向右拖动滑块填充拼图</span>
          </div>
        </div>
      </div>
    </div>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <div class="controls">
      <button class="btn" onclick="refreshCaptcha()">🔄 刷新验证码</button>
      <button class="btn secondary" onclick="resetCaptcha()">🔄 重置状态</button>
    </div>
  </div>

  <script>
    // 简单的滑动验证码实现
    class SimpleSlideCaptcha {
      constructor() {
        this.canvas = document.getElementById('canvas');
        this.block = document.getElementById('block');
        this.slider = document.getElementById('slider');
        this.mask = document.getElementById('mask');
        this.result = document.getElementById('result');
        
        this.canvasCtx = this.canvas.getContext('2d');
        this.blockCtx = this.block.getContext('2d');
        
        this.correctX = 0;
        this.sliderLeft = 0;
        this.isMouseDown = false;
        this.originX = 0;
        this.trail = [];
        
        this.init();
        this.bindEvents();
      }
      
      init() {
        this.generateCaptcha();
      }
      
      generateCaptcha() {
        // 清除画布
        this.canvasCtx.clearRect(0, 0, 310, 155);
        this.blockCtx.clearRect(0, 0, 310, 155);
        
        // 生成背景
        this.drawBackground();
        
        // 生成拼图块
        this.drawPuzzle();
        
        this.reset();
      }
      
      drawBackground() {
        // 创建渐变背景
        const gradient = this.canvasCtx.createLinearGradient(0, 0, 310, 155);
        const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd'];
        const color1 = colors[Math.floor(Math.random() * colors.length)];
        const color2 = colors[Math.floor(Math.random() * colors.length)];
        
        gradient.addColorStop(0, color1);
        gradient.addColorStop(1, color2);
        
        this.canvasCtx.fillStyle = gradient;
        this.canvasCtx.fillRect(0, 0, 310, 155);
        
        // 添加一些装饰图案
        for (let i = 0; i < 15; i++) {
          const x = Math.random() * 310;
          const y = Math.random() * 155;
          const radius = Math.random() * 25 + 5;
          
          this.canvasCtx.beginPath();
          this.canvasCtx.arc(x, y, radius, 0, 2 * Math.PI);
          this.canvasCtx.fillStyle = `hsla(${Math.random() * 360}, 70%, 80%, 0.4)`;
          this.canvasCtx.fill();
        }
      }
      
      drawPuzzle() {
        const l = 42; // 拼图块边长
        const r = 10; // 圆角半径
        
        // 随机生成拼图块位置
        this.correctX = Math.floor(Math.random() * (310 - l - 20)) + l + 10;
        const y = Math.floor(Math.random() * (155 - l - 20)) + 10;
        
        // 在主画布上绘制缺口
        this.drawPuzzlePath(this.canvasCtx, this.correctX, y, l, r, 'fill');
        
        // 在块画布上绘制拼图块
        this.drawPuzzlePath(this.blockCtx, this.correctX, y, l, r, 'clip');
      }
      
      drawPuzzlePath(ctx, x, y, l, r, operation) {
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.arc(x + l / 2, y - r + 2, r, 0.72 * Math.PI, 2.26 * Math.PI);
        ctx.lineTo(x + l, y);
        ctx.arc(x + l + r - 2, y + l / 2, r, 1.21 * Math.PI, 2.78 * Math.PI);
        ctx.lineTo(x + l, y + l);
        ctx.lineTo(x, y + l);
        ctx.arc(x + r - 2, y + l / 2, r + 0.4, 2.76 * Math.PI, 1.24 * Math.PI, true);
        ctx.lineTo(x, y);
        
        ctx.lineWidth = 2;
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.stroke();
        
        if (operation === 'fill') {
          ctx.globalCompositeOperation = 'destination-over';
          ctx.fill();
        } else if (operation === 'clip') {
          ctx.clip();
          ctx.drawImage(this.canvas, 0, 0);
        }
      }
      
      bindEvents() {
        this.slider.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.slider.addEventListener('touchstart', this.handleMouseDown.bind(this));
      }
      
      handleMouseDown(e) {
        e.preventDefault();
        this.isMouseDown = true;
        this.originX = e.type === 'mousedown' ? e.clientX : e.touches[0].clientX;
        this.trail = [];
        
        document.addEventListener('mousemove', this.handleMouseMove.bind(this));
        document.addEventListener('mouseup', this.handleMouseUp.bind(this));
        document.addEventListener('touchmove', this.handleMouseMove.bind(this));
        document.addEventListener('touchend', this.handleMouseUp.bind(this));
      }
      
      handleMouseMove(e) {
        if (!this.isMouseDown) return;
        e.preventDefault();
        
        const clientX = e.type === 'mousemove' ? e.clientX : e.touches[0].clientX;
        const moveX = clientX - this.originX;
        
        if (moveX < 0 || moveX + 38 > 310) return;
        
        this.sliderLeft = moveX;
        this.slider.style.left = moveX + 'px';
        this.mask.style.width = moveX + 'px';
        this.block.style.left = (310 - 40) / (310 - 40) * moveX + 'px';
        
        this.trail.push(Math.round(moveX));
      }
      
      handleMouseUp() {
        if (!this.isMouseDown) return;
        
        this.isMouseDown = false;
        
        document.removeEventListener('mousemove', this.handleMouseMove.bind(this));
        document.removeEventListener('mouseup', this.handleMouseUp.bind(this));
        document.removeEventListener('touchmove', this.handleMouseMove.bind(this));
        document.removeEventListener('touchend', this.handleMouseUp.bind(this));
        
        this.verify();
      }
      
      verify() {
        const blockLeft = (310 - 40) / (310 - 40) * this.sliderLeft;
        
        if (Math.abs(blockLeft - this.correctX) < 5) {
          // 验证成功
          this.showResult(true, '✅ 验证成功！');
          this.slider.innerHTML = '✓';
          this.slider.style.background = '#67c23a';
          this.slider.style.color = 'white';
        } else {
          // 验证失败
          this.showResult(false, '❌ 验证失败，请重试');
          this.slider.innerHTML = '✗';
          this.slider.style.background = '#f56c6c';
          this.slider.style.color = 'white';
          
          setTimeout(() => {
            this.reset();
            this.hideResult();
          }, 1500);
        }
      }
      
      showResult(success, message) {
        this.result.textContent = message;
        this.result.className = 'result ' + (success ? 'success' : 'fail');
        this.result.style.display = 'block';
      }
      
      hideResult() {
        this.result.style.display = 'none';
      }
      
      reset() {
        this.sliderLeft = 0;
        this.slider.style.left = '0px';
        this.mask.style.width = '0px';
        this.block.style.left = '0px';
        this.slider.innerHTML = '→';
        this.slider.style.background = '#fff';
        this.slider.style.color = '#666';
        this.trail = [];
      }
      
      refresh() {
        this.hideResult();
        this.generateCaptcha();
      }
    }
    
    // 初始化验证码
    let captcha;
    
    window.onload = function() {
      captcha = new SimpleSlideCaptcha();
    };
    
    function refreshCaptcha() {
      if (captcha) {
        captcha.refresh();
      }
    }
    
    function resetCaptcha() {
      if (captcha) {
        captcha.reset();
        captcha.hideResult();
      }
    }
  </script>
</body>
</html>