#!/usr/bin/env python3
"""
策略执行引擎
实现策略加载、执行、监控和风险控制
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
import importlib.util
import sys
from pathlib import Path

from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.models.strategy import Strategy, StrategyExecution, StrategyStatus, ExecutionStatus
from app.models.trading import Order, OrderDirection, OrderType, OrderStatus
from app.services.trading_service import TradingService
from app.services.risk_management_service import RiskManagementService
from app.core.database import get_db


class StrategyExecutionEngine:
    """策略执行引擎"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.trading_service = TradingService(db)
        self.risk_service = RiskManagementService(db)
        
        # 运行中的策略实例
        self.running_strategies: Dict[str, Dict[str, Any]] = {}
        
        # 策略执行任务
        self.execution_tasks: Dict[str, asyncio.Task] = {}
        
        # 风险控制参数
        self.risk_limits = {
            "max_position_size": 100000,  # 最大持仓金额
            "max_daily_loss": 5000,       # 最大日损失
            "max_drawdown": 0.1,          # 最大回撤比例
            "max_orders_per_minute": 10   # 每分钟最大订单数
        }
        
        logger.info("策略执行引擎初始化完成")
    
    async def load_strategy(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """加载策略"""
        try:
            # 从数据库获取策略信息
            result = await self.db.execute(
                select(Strategy).where(Strategy.id == strategy_id)
            )
            strategy = result.scalar_one_or_none()
            
            if not strategy:
                logger.error(f"策略不存在: {strategy_id}")
                return None
            
            if strategy.status != StrategyStatus.ACTIVE:
                logger.error(f"策略状态不可用: {strategy.status}")
                return None
            
            # 加载策略代码
            strategy_module = await self._load_strategy_code(strategy)
            if not strategy_module:
                return None
            
            # 创建策略实例
            strategy_instance = {
                "id": strategy_id,
                "name": strategy.name,
                "module": strategy_module,
                "config": json.loads(strategy.config) if strategy.config else {},
                "status": "loaded",
                "created_at": datetime.utcnow(),
                "last_execution": None,
                "execution_count": 0,
                "total_pnl": 0.0,
                "positions": {},
                "orders": []
            }
            
            self.running_strategies[strategy_id] = strategy_instance
            logger.info(f"策略加载成功: {strategy.name}")
            
            return strategy_instance
            
        except Exception as e:
            logger.error(f"加载策略失败: {e}")
            return None
    
    async def _load_strategy_code(self, strategy: Strategy) -> Optional[Any]:
        """加载策略代码模块"""
        try:
            if strategy.file_path:
                # 从文件加载
                strategy_path = Path(strategy.file_path)
                if strategy_path.exists():
                    spec = importlib.util.spec_from_file_location(
                        f"strategy_{strategy.id}", strategy_path
                    )
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    return module
            
            elif strategy.code:
                # 从代码字符串加载
                module_name = f"strategy_{strategy.id}"
                spec = importlib.util.spec_from_loader(module_name, loader=None)
                module = importlib.util.module_from_spec(spec)
                exec(strategy.code, module.__dict__)
                sys.modules[module_name] = module
                return module
            
            logger.error("策略代码为空")
            return None
            
        except Exception as e:
            logger.error(f"加载策略代码失败: {e}")
            return None
    
    async def start_strategy(self, strategy_id: str) -> bool:
        """启动策略执行"""
        try:
            if strategy_id not in self.running_strategies:
                strategy_instance = await self.load_strategy(strategy_id)
                if not strategy_instance:
                    return False
            
            strategy_instance = self.running_strategies[strategy_id]
            
            if strategy_instance["status"] == "running":
                logger.warning(f"策略已在运行: {strategy_id}")
                return True
            
            # 风险检查
            if not await self._pre_execution_risk_check(strategy_id):
                logger.error(f"策略风险检查失败: {strategy_id}")
                return False
            
            # 创建执行记录
            execution = StrategyExecution(
                id=str(uuid.uuid4()),
                strategy_id=strategy_id,
                status=ExecutionStatus.RUNNING,
                start_time=datetime.utcnow(),
                config=json.dumps(strategy_instance["config"])
            )
            
            self.db.add(execution)
            await self.db.commit()
            
            # 启动执行任务
            task = asyncio.create_task(
                self._execute_strategy_loop(strategy_id, execution.id)
            )
            self.execution_tasks[strategy_id] = task
            
            strategy_instance["status"] = "running"
            strategy_instance["execution_id"] = execution.id
            
            logger.info(f"策略启动成功: {strategy_instance['name']}")
            return True
            
        except Exception as e:
            logger.error(f"启动策略失败: {e}")
            return False
    
    async def stop_strategy(self, strategy_id: str) -> bool:
        """停止策略执行"""
        try:
            if strategy_id not in self.running_strategies:
                logger.warning(f"策略未运行: {strategy_id}")
                return True
            
            strategy_instance = self.running_strategies[strategy_id]
            
            # 取消执行任务
            if strategy_id in self.execution_tasks:
                task = self.execution_tasks[strategy_id]
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                del self.execution_tasks[strategy_id]
            
            # 更新执行记录
            if "execution_id" in strategy_instance:
                await self.db.execute(
                    update(StrategyExecution)
                    .where(StrategyExecution.id == strategy_instance["execution_id"])
                    .values(
                        status=ExecutionStatus.STOPPED,
                        end_time=datetime.utcnow(),
                        total_pnl=strategy_instance["total_pnl"]
                    )
                )
                await self.db.commit()
            
            strategy_instance["status"] = "stopped"
            logger.info(f"策略停止成功: {strategy_instance['name']}")
            
            return True
            
        except Exception as e:
            logger.error(f"停止策略失败: {e}")
            return False
    
    async def _execute_strategy_loop(self, strategy_id: str, execution_id: str):
        """策略执行循环"""
        strategy_instance = self.running_strategies[strategy_id]
        
        try:
            while strategy_instance["status"] == "running":
                # 执行策略逻辑
                await self._execute_strategy_once(strategy_id)
                
                # 更新执行统计
                strategy_instance["execution_count"] += 1
                strategy_instance["last_execution"] = datetime.utcnow()
                
                # 风险监控
                if not await self._runtime_risk_check(strategy_id):
                    logger.warning(f"策略触发风险控制，停止执行: {strategy_id}")
                    await self.stop_strategy(strategy_id)
                    break
                
                # 等待下次执行
                execution_interval = strategy_instance["config"].get("execution_interval", 60)
                await asyncio.sleep(execution_interval)
                
        except asyncio.CancelledError:
            logger.info(f"策略执行被取消: {strategy_id}")
        except Exception as e:
            logger.error(f"策略执行异常: {e}")
            strategy_instance["status"] = "error"
            
            # 更新执行记录
            await self.db.execute(
                update(StrategyExecution)
                .where(StrategyExecution.id == execution_id)
                .values(
                    status=ExecutionStatus.ERROR,
                    end_time=datetime.utcnow(),
                    error_message=str(e)
                )
            )
            await self.db.commit()
    
    async def _execute_strategy_once(self, strategy_id: str):
        """执行策略一次"""
        strategy_instance = self.running_strategies[strategy_id]
        module = strategy_instance["module"]
        
        try:
            # 检查策略模块是否有必要的函数
            if not hasattr(module, 'execute'):
                logger.error(f"策略缺少execute函数: {strategy_id}")
                return
            
            # 准备策略执行上下文
            context = {
                "strategy_id": strategy_id,
                "config": strategy_instance["config"],
                "positions": strategy_instance["positions"],
                "trading_service": self.trading_service,
                "market_data": await self._get_market_data(),
                "current_time": datetime.utcnow()
            }
            
            # 执行策略
            signals = await self._call_strategy_function(module.execute, context)
            
            # 处理交易信号
            if signals:
                await self._process_trading_signals(strategy_id, signals)
                
        except Exception as e:
            logger.error(f"策略执行失败: {e}")
            raise
    
    async def _call_strategy_function(self, func: Callable, context: Dict[str, Any]) -> Any:
        """调用策略函数"""
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(context)
            else:
                return func(context)
        except Exception as e:
            logger.error(f"调用策略函数失败: {e}")
            raise
    
    async def _process_trading_signals(self, strategy_id: str, signals: List[Dict[str, Any]]):
        """处理交易信号"""
        strategy_instance = self.running_strategies[strategy_id]
        
        for signal in signals:
            try:
                # 验证信号格式
                if not self._validate_signal(signal):
                    logger.warning(f"无效的交易信号: {signal}")
                    continue
                
                # 风险检查
                if not await self._signal_risk_check(strategy_id, signal):
                    logger.warning(f"信号风险检查失败: {signal}")
                    continue
                
                # 创建订单
                order = await self._create_order_from_signal(strategy_id, signal)
                if order:
                    strategy_instance["orders"].append(order.order_id)
                    logger.info(f"策略订单创建成功: {order.order_id}")
                
            except Exception as e:
                logger.error(f"处理交易信号失败: {e}")
    
    def _validate_signal(self, signal: Dict[str, Any]) -> bool:
        """验证交易信号"""
        required_fields = ["symbol", "direction", "quantity"]
        return all(field in signal for field in required_fields)
    
    async def _signal_risk_check(self, strategy_id: str, signal: Dict[str, Any]) -> bool:
        """信号风险检查"""
        try:
            # 检查持仓限制
            current_position = await self._get_current_position(signal["symbol"])
            new_quantity = signal["quantity"]
            
            if signal["direction"] == "BUY":
                total_position = current_position + new_quantity
            else:
                total_position = current_position - new_quantity
            
            # 检查最大持仓
            if abs(total_position) * signal.get("price", 0) > self.risk_limits["max_position_size"]:
                return False
            
            # 检查订单频率
            if not await self._check_order_frequency(strategy_id):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"信号风险检查失败: {e}")
            return False
    
    async def _create_order_from_signal(self, strategy_id: str, signal: Dict[str, Any]) -> Optional[Order]:
        """从信号创建订单"""
        try:
            order_data = {
                "symbol": signal["symbol"],
                "direction": OrderDirection.BUY if signal["direction"] == "BUY" else OrderDirection.SELL,
                "order_type": OrderType.MARKET if signal.get("type") == "MARKET" else OrderType.LIMIT,
                "quantity": signal["quantity"],
                "price": signal.get("price"),
                "strategy_id": strategy_id
            }
            
            return await self.trading_service.create_order(**order_data)
            
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None
    
    async def _pre_execution_risk_check(self, strategy_id: str) -> bool:
        """执行前风险检查"""
        try:
            # 检查账户状态
            # 检查资金充足性
            # 检查策略配置合理性
            return True
        except Exception as e:
            logger.error(f"执行前风险检查失败: {e}")
            return False
    
    async def _runtime_risk_check(self, strategy_id: str) -> bool:
        """运行时风险检查"""
        try:
            strategy_instance = self.running_strategies[strategy_id]
            
            # 检查日损失
            if strategy_instance["total_pnl"] < -self.risk_limits["max_daily_loss"]:
                return False
            
            # 检查回撤
            # 检查其他风险指标
            
            return True
        except Exception as e:
            logger.error(f"运行时风险检查失败: {e}")
            return False
    
    async def _get_market_data(self) -> Dict[str, Any]:
        """获取市场数据"""
        # 这里应该调用市场数据服务
        return {}
    
    async def _get_current_position(self, symbol: str) -> float:
        """获取当前持仓"""
        # 这里应该调用持仓服务
        return 0.0
    
    async def _check_order_frequency(self, strategy_id: str) -> bool:
        """检查订单频率"""
        # 检查最近一分钟的订单数量
        return True
    
    async def get_strategy_status(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """获取策略状态"""
        if strategy_id not in self.running_strategies:
            return None
        
        strategy_instance = self.running_strategies[strategy_id]
        return {
            "id": strategy_instance["id"],
            "name": strategy_instance["name"],
            "status": strategy_instance["status"],
            "execution_count": strategy_instance["execution_count"],
            "last_execution": strategy_instance["last_execution"],
            "total_pnl": strategy_instance["total_pnl"],
            "positions": strategy_instance["positions"],
            "orders_count": len(strategy_instance["orders"])
        }
    
    async def get_all_running_strategies(self) -> List[Dict[str, Any]]:
        """获取所有运行中的策略"""
        return [
            await self.get_strategy_status(strategy_id)
            for strategy_id in self.running_strategies.keys()
        ]
    
    async def cleanup(self):
        """清理资源"""
        logger.info("策略执行引擎清理中...")
        
        # 停止所有策略
        for strategy_id in list(self.running_strategies.keys()):
            await self.stop_strategy(strategy_id)
        
        # 取消所有任务
        for task in self.execution_tasks.values():
            task.cancel()
        
        # 等待任务完成
        if self.execution_tasks:
            await asyncio.gather(*self.execution_tasks.values(), return_exceptions=True)
        
        self.running_strategies.clear()
        self.execution_tasks.clear()
        
        logger.info("策略执行引擎清理完成")


# 全局策略执行引擎实例
strategy_engine: Optional[StrategyExecutionEngine] = None


async def get_strategy_engine() -> StrategyExecutionEngine:
    """获取策略执行引擎实例"""
    global strategy_engine
    if strategy_engine is None:
        db = await anext(get_db())
        strategy_engine = StrategyExecutionEngine(db)
    return strategy_engine
