<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: green;
        }
        .error {
            color: red;
        }
    </style>
</head>
<body>
    <h1>前端Store调试</h1>
    
    <div class="debug-section">
        <h2>1. 检查Store状态</h2>
        <button onclick="checkStoreState()">检查Store状态</button>
        <div id="store-state" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h2>2. 测试API调用</h2>
        <button onclick="testMarketAPI()">测试市场API</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h2>3. 测试数据提取</h2>
        <button onclick="testDataExtraction()">测试数据提取</button>
        <div id="extraction-result" class="result"></div>
    </div>

    <script>
        // 检查Store状态
        function checkStoreState() {
            const resultElement = document.getElementById('store-state');
            
            try {
                // 检查是否有Vue应用实例
                if (typeof window.__VUE_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined') {
                    resultElement.innerHTML = '✅ Vue DevTools可用\n';
                } else {
                    resultElement.innerHTML = '❌ Vue DevTools不可用\n';
                }
                
                // 检查是否有Pinia store
                if (typeof window.pinia !== 'undefined') {
                    resultElement.innerHTML += '✅ Pinia store可用\n';
                } else {
                    resultElement.innerHTML += '❌ Pinia store不可用\n';
                }
                
                // 尝试访问market store
                if (typeof window.marketStore !== 'undefined') {
                    const store = window.marketStore;
                    resultElement.innerHTML += `✅ Market Store可用\n`;
                    resultElement.innerHTML += `  - stockList: ${store.stockList?.length || 0} 条\n`;
                    resultElement.innerHTML += `  - indices: ${Object.keys(store.indices || {}).length} 个\n`;
                    resultElement.innerHTML += `  - industries: ${store.industries?.length || 0} 个\n`;
                    resultElement.innerHTML += `  - sectors: ${store.sectors?.length || 0} 个\n`;
                } else {
                    resultElement.innerHTML += '❌ Market Store不可用\n';
                }
                
                resultElement.className = 'result success';
                
            } catch (error) {
                resultElement.innerHTML = `❌ 检查Store状态失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }
        
        // 测试API调用
        async function testMarketAPI() {
            const resultElement = document.getElementById('api-result');
            resultElement.innerHTML = '⏳ 测试API调用...';
            
            try {
                const BASE_URL = 'http://localhost:8000';
                
                // 测试市场概览
                const overviewResponse = await fetch(`${BASE_URL}/api/v1/market/overview`);
                const overviewData = await overviewResponse.json();
                
                // 测试股票列表
                const stocksResponse = await fetch(`${BASE_URL}/api/v1/market/stocks?pageSize=10`);
                const stocksData = await stocksResponse.json();
                
                // 测试板块数据
                const sectorsResponse = await fetch(`${BASE_URL}/api/v1/market/sectors`);
                const sectorsData = await sectorsResponse.json();
                
                const result = {
                    overview: {
                        success: overviewResponse.ok,
                        indicesCount: overviewData.data?.indices?.length || 0,
                        statsAvailable: !!overviewData.data?.stats
                    },
                    stocks: {
                        success: stocksResponse.ok,
                        count: stocksData.data?.items?.length || 0,
                        hasIndustry: stocksData.data?.items?.[0]?.industry ? true : false
                    },
                    sectors: {
                        success: sectorsResponse.ok,
                        count: sectorsData.data?.length || 0
                    }
                };
                
                resultElement.innerHTML = `✅ API测试结果:\n${JSON.stringify(result, null, 2)}`;
                resultElement.className = 'result success';
                
            } catch (error) {
                resultElement.innerHTML = `❌ API测试失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }
        
        // 测试数据提取
        async function testDataExtraction() {
            const resultElement = document.getElementById('extraction-result');
            resultElement.innerHTML = '⏳ 测试数据提取...';
            
            try {
                const BASE_URL = 'http://localhost:8000';
                
                // 获取股票数据
                const stocksResponse = await fetch(`${BASE_URL}/api/v1/market/stocks?pageSize=50`);
                const stocksData = await stocksResponse.json();
                const stocks = stocksData.data?.items || [];
                
                // 提取行业数据
                const industrySet = new Set();
                const industries = [];
                
                stocks.forEach(stock => {
                    if (stock.industry && !industrySet.has(stock.industry)) {
                        industrySet.add(stock.industry);
                        industries.push({
                            code: stock.industry,
                            name: stock.industry
                        });
                    }
                });
                
                // 获取板块数据
                const sectorsResponse = await fetch(`${BASE_URL}/api/v1/market/sectors`);
                const sectorsData = await sectorsResponse.json();
                const sectors = sectorsData.data || [];
                
                const sectorIndustries = sectors.map(sector => ({
                    code: sector.name,
                    name: sector.name
                }));
                
                // 获取市场概览数据
                const overviewResponse = await fetch(`${BASE_URL}/api/v1/market/overview`);
                const overviewData = await overviewResponse.json();
                const indices = overviewData.data?.indices || [];
                
                const result = {
                    stocksProcessed: stocks.length,
                    industriesFromStocks: industries,
                    industriesFromSectors: sectorIndustries,
                    totalIndustryOptions: industries.length + sectorIndustries.length,
                    indicesData: indices.map(index => ({
                        name: index.name,
                        value: index.value,
                        change: index.change,
                        changePercent: index.changePercent
                    }))
                };
                
                resultElement.innerHTML = `✅ 数据提取结果:\n${JSON.stringify(result, null, 2)}`;
                resultElement.className = 'result success';
                
            } catch (error) {
                resultElement.innerHTML = `❌ 数据提取失败: ${error.message}`;
                resultElement.className = 'result error';
            }
        }
        
        // 页面加载时自动检查
        window.onload = function() {
            console.log('调试页面加载完成');
            setTimeout(() => {
                checkStoreState();
            }, 1000);
        };
    </script>
</body>
</html>
