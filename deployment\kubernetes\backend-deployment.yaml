apiVersion: apps/v1
kind: Deployment
metadata:
  name: quant-backend
  namespace: quant-trading
  labels:
    app: quant-backend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: quant-backend
  template:
    metadata:
      labels:
        app: quant-backend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: quant-backend-sa
      containers:
      - name: backend
        image: quant-backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: quant-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: quant-secrets
              key: redis-url
        - name: CLICKHOUSE_URL
          valueFrom:
            secretKeyRef:
              name: quant-secrets
              key: clickhouse-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-secrets
              key: secret-key
        - name: JWT_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: quant-secrets
              key: jwt-secret-key
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "INFO"
        - name: WORKERS
          value: "4"
        resources:
          limits:
            cpu: 2000m
            memory: 4Gi
          requests:
            cpu: 500m
            memory: 1Gi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: data
          mountPath: /app/data
        - name: config
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: quant-logs-pvc
      - name: data
        persistentVolumeClaim:
          claimName: quant-data-pvc
      - name: config
        configMap:
          name: quant-config
      nodeSelector:
        kubernetes.io/arch: amd64
      tolerations:
      - key: "quant-workload"
        operator: "Equal"
        value: "backend"
        effect: "NoSchedule"
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - quant-backend
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: quant-backend-service
  namespace: quant-trading
  labels:
    app: quant-backend
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8000"
spec:
  type: ClusterIP
  ports:
  - port: 8000
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: quant-backend
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: quant-backend-sa
  namespace: quant-trading
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: quant-backend-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: quant-backend-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: quant-backend-role
subjects:
- kind: ServiceAccount
  name: quant-backend-sa
  namespace: quant-trading