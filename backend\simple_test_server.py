#!/usr/bin/env python3
"""
简化的测试后端服务
用于支持前端测试，提供基本的API端点
"""

import asyncio
import json
import math
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

app = FastAPI(title="量化投资平台测试API", version="1.0.0")

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 模拟数据
MOCK_STOCKS = [
    {"symbol": "000001", "name": "平安银行", "price": 12.45, "change": 0.12, "change_percent": 0.97},
    {"symbol": "000002", "name": "万科A", "price": 8.76, "change": -0.05, "change_percent": -0.57},
    {"symbol": "600000", "name": "浦发银行", "price": 7.89, "change": 0.08, "change_percent": 1.02},
    {"symbol": "600036", "name": "招商银行", "price": 35.67, "change": 0.45, "change_percent": 1.28},
    {"symbol": "000858", "name": "五粮液", "price": 128.90, "change": -2.10, "change_percent": -1.60},
]

MOCK_STRATEGIES = [
    {"id": 1, "name": "均线策略", "status": "运行中", "return_rate": 12.5, "risk_level": "中"},
    {"id": 2, "name": "动量策略", "status": "已停止", "return_rate": 8.3, "risk_level": "高"},
    {"id": 3, "name": "价值投资", "status": "运行中", "return_rate": 15.2, "risk_level": "低"},
]

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/market/stocks")
async def get_market_data():
    """获取市场数据"""
    # 模拟实时价格变动
    for stock in MOCK_STOCKS:
        change = random.uniform(-0.5, 0.5)
        stock["price"] += change
        stock["change"] += change
        stock["change_percent"] = (stock["change"] / (stock["price"] - stock["change"])) * 100
    
    return {
        "success": True,
        "data": MOCK_STOCKS,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(symbol: str):
    """获取K线数据"""
    # 生成模拟K线数据
    kline_data = []
    base_price = 100
    
    for i in range(30):  # 30天数据
        date = (datetime.now() - timedelta(days=29-i)).strftime("%Y-%m-%d")
        open_price = base_price + random.uniform(-5, 5)
        close_price = open_price + random.uniform(-3, 3)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)
        volume = random.randint(1000000, 10000000)
        
        kline_data.append({
            "date": date,
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })
        
        base_price = close_price
    
    return {
        "success": True,
        "data": kline_data,
        "symbol": symbol
    }

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """获取市场概览"""
    return {
        "success": True,
        "data": {
            "total_stocks": 4000,
            "rising_stocks": 2100,
            "falling_stocks": 1500,
            "unchanged_stocks": 400,
            "total_volume": 125000000000,
            "total_turnover": 890000000000,
            "main_indices": [
                {"name": "上证指数", "value": 3245.67, "change": 12.34, "change_percent": 0.38},
                {"name": "深证成指", "value": 12456.78, "change": -23.45, "change_percent": -0.19},
                {"name": "创业板指", "value": 2567.89, "change": 45.67, "change_percent": 1.81}
            ]
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/sectors")
async def get_market_sectors():
    """获取板块数据"""
    sectors = [
        {"name": "科技", "change_percent": 2.34, "stocks_count": 245, "leader": "腾讯控股"},
        {"name": "金融", "change_percent": -0.56, "stocks_count": 189, "leader": "中国平安"},
        {"name": "医药", "change_percent": 1.78, "stocks_count": 156, "leader": "恒瑞医药"},
        {"name": "消费", "change_percent": 0.89, "stocks_count": 234, "leader": "贵州茅台"},
        {"name": "地产", "change_percent": -1.23, "stocks_count": 123, "leader": "万科A"},
        {"name": "能源", "change_percent": 3.45, "stocks_count": 98, "leader": "中石油"},
        {"name": "制造", "change_percent": 1.56, "stocks_count": 567, "leader": "比亚迪"},
        {"name": "通信", "change_percent": -0.34, "stocks_count": 87, "leader": "中国移动"}
    ]

    return {
        "success": True,
        "data": sectors,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/news")
async def get_market_news(limit: int = 10):
    """获取市场新闻"""
    news = [
        {
            "id": i,
            "title": f"市场新闻标题 {i}",
            "summary": f"这是第{i}条市场新闻的摘要内容...",
            "source": "财经网",
            "publish_time": (datetime.now() - timedelta(hours=i)).isoformat(),
            "url": f"https://example.com/news/{i}"
        }
        for i in range(1, limit + 1)
    ]

    return {
        "success": True,
        "data": news,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/rankings")
async def get_market_rankings(type: str = "change_percent", limit: int = 50):
    """获取市场排行榜"""
    rankings = []

    for i in range(limit):
        if type == "change_percent":
            value = random.uniform(-10, 10)
            rankings.append({
                "symbol": f"00000{i+1:02d}",
                "name": f"股票{i+1}",
                "price": round(random.uniform(10, 200), 2),
                "change_percent": round(value, 2),
                "volume": random.randint(1000000, 100000000)
            })
        elif type == "turnover":
            value = random.randint(100000000, 10000000000)
            rankings.append({
                "symbol": f"00000{i+1:02d}",
                "name": f"股票{i+1}",
                "price": round(random.uniform(10, 200), 2),
                "turnover": value,
                "volume": random.randint(1000000, 100000000)
            })

    # 按指定类型排序
    if type == "change_percent":
        rankings.sort(key=lambda x: x["change_percent"], reverse=True)
    elif type == "turnover":
        rankings.sort(key=lambda x: x["turnover"], reverse=True)

    return {
        "success": True,
        "data": rankings,
        "type": type,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/market/kline")
async def get_kline_data_new(symbol: str, period: str = "1d", limit: int = 500):
    """获取K线数据 (新版本支持查询参数)"""
    # 生成模拟K线数据
    kline_data = []
    base_price = 100

    for i in range(min(limit, 500)):  # 限制最大500条
        if period == "1d":
            timestamp = datetime.now() - timedelta(days=limit-i)
        elif period == "1h":
            timestamp = datetime.now() - timedelta(hours=limit-i)
        else:
            timestamp = datetime.now() - timedelta(minutes=limit-i)

        open_price = base_price + random.uniform(-2, 2)
        close_price = open_price + random.uniform(-1, 1)
        high_price = max(open_price, close_price) + random.uniform(0, 0.5)
        low_price = min(open_price, close_price) - random.uniform(0, 0.5)
        volume = random.randint(1000000, 10000000)

        kline_data.append({
            "timestamp": timestamp.isoformat(),
            "open": round(open_price, 2),
            "high": round(high_price, 2),
            "low": round(low_price, 2),
            "close": round(close_price, 2),
            "volume": volume
        })

        base_price = close_price

    return {
        "success": True,
        "data": kline_data,
        "symbol": symbol,
        "period": period,
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/strategies")
async def get_strategies():
    """获取策略列表"""
    return {
        "success": True,
        "data": MOCK_STRATEGIES,
        "total": len(MOCK_STRATEGIES)
    }

@app.post("/api/v1/strategies")
async def create_strategy(strategy_data: dict):
    """创建新策略"""
    new_strategy = {
        "id": len(MOCK_STRATEGIES) + 1,
        "name": strategy_data.get("name", "新策略"),
        "status": "已创建",
        "return_rate": 0.0,
        "risk_level": strategy_data.get("risk_level", "中")
    }
    MOCK_STRATEGIES.append(new_strategy)
    
    return {
        "success": True,
        "data": new_strategy,
        "message": "策略创建成功"
    }

@app.get("/api/v1/portfolio")
async def get_portfolio():
    """获取投资组合"""
    portfolio_data = {
        "total_value": 1000000.00,
        "total_return": 125000.00,
        "return_rate": 12.5,
        "positions": [
            {"symbol": "000001", "name": "平安银行", "quantity": 1000, "cost": 12.00, "current_price": 12.45, "profit": 450.00},
            {"symbol": "600036", "name": "招商银行", "quantity": 500, "cost": 35.00, "current_price": 35.67, "profit": 335.00},
        ]
    }
    
    return {
        "success": True,
        "data": portfolio_data
    }

@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """获取风险指标"""
    risk_data = {
        "var": 0.05,  # Value at Risk
        "max_drawdown": 0.08,
        "sharpe_ratio": 1.25,
        "beta": 0.95,
        "volatility": 0.15
    }
    
    return {
        "success": True,
        "data": risk_data
    }

@app.post("/api/v1/trading/order")
async def place_order(order_data: dict):
    """下单"""
    order = {
        "order_id": f"ORDER_{int(time.time())}",
        "symbol": order_data.get("symbol"),
        "side": order_data.get("side"),  # buy/sell
        "quantity": order_data.get("quantity"),
        "price": order_data.get("price"),
        "status": "已提交",
        "timestamp": datetime.now().isoformat()
    }
    
    return {
        "success": True,
        "data": order,
        "message": "订单提交成功"
    }

@app.get("/api/v1/trading/orders")
async def get_orders():
    """获取订单列表"""
    orders = [
        {
            "order_id": "ORDER_001",
            "symbol": "000001",
            "side": "buy",
            "quantity": 100,
            "price": 12.40,
            "status": "已成交",
            "timestamp": datetime.now().isoformat()
        }
    ]
    
    return {
        "success": True,
        "data": orders
    }

@app.get("/api/v1/trading/trades")
async def get_trades(limit: int = 100, startDate: str = None, endDate: str = None):
    """获取成交记录"""
    trades = []

    for i in range(min(limit, 20)):  # 生成一些模拟成交记录
        trade_time = datetime.now() - timedelta(hours=i)
        trades.append({
            "trade_id": f"TRADE_{i+1:03d}",
            "order_id": f"ORDER_{i+1:03d}",
            "symbol": f"00000{(i % 10) + 1}",
            "side": "buy" if i % 2 == 0 else "sell",
            "quantity": random.randint(100, 1000),
            "price": round(random.uniform(10, 200), 2),
            "amount": round(random.uniform(1000, 20000), 2),
            "commission": round(random.uniform(5, 50), 2),
            "timestamp": trade_time.isoformat()
        })

    return {
        "success": True,
        "data": trades,
        "total": len(trades),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/status")
async def get_ctp_status():
    """获取CTP连接状态"""
    return {
        "success": True,
        "data": {
            "connected": True,
            "login_status": "已登录",
            "trading_day": datetime.now().strftime("%Y%m%d"),
            "front_id": 1,
            "session_id": 123456,
            "last_heartbeat": datetime.now().isoformat()
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/account")
async def get_ctp_account():
    """获取CTP账户信息"""
    return {
        "success": True,
        "data": {
            "account_id": "*********",
            "balance": 1000000.00,
            "available": 950000.00,
            "frozen": 50000.00,
            "margin": 30000.00,
            "profit_loss": 15000.00,
            "currency": "CNY"
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/ctp/orders")
async def get_ctp_orders():
    """获取CTP订单列表"""
    orders = [
        {
            "order_id": f"CTP_ORDER_{i+1:03d}",
            "symbol": f"00000{(i % 5) + 1}",
            "side": "buy" if i % 2 == 0 else "sell",
            "quantity": random.randint(100, 1000),
            "price": round(random.uniform(10, 200), 2),
            "status": random.choice(["已提交", "部分成交", "已成交", "已撤销"]),
            "timestamp": (datetime.now() - timedelta(hours=i)).isoformat()
        }
        for i in range(10)
    ]

    return {
        "success": True,
        "data": orders,
        "total": len(orders),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/auth/user")
async def get_user_info():
    """获取用户信息"""
    user_data = {
        "id": 1,
        "username": "demo_user",
        "email": "<EMAIL>",
        "balance": 1000000.00,
        "permissions": ["trading", "strategy", "portfolio", "risk"]
    }
    
    return {
        "success": True,
        "data": user_data
    }

@app.post("/api/v1/auth/login")
async def login(credentials: dict):
    """登录"""
    username = credentials.get("username")
    password = credentials.get("password")
    
    # 简单的模拟登录
    if username and password:
        return {
            "success": True,
            "data": {
                "token": "mock_jwt_token",
                "user": {
                    "id": 1,
                    "username": username,
                    "email": f"{username}@example.com"
                }
            },
            "message": "登录成功"
        }
    else:
        raise HTTPException(status_code=400, detail="用户名或密码不能为空")

@app.get("/api/v1/backtest/results/{strategy_id}")
async def get_backtest_results(strategy_id: int):
    """获取回测结果"""
    backtest_data = {
        "strategy_id": strategy_id,
        "start_date": "2024-01-01",
        "end_date": "2024-12-31",
        "total_return": 15.2,
        "max_drawdown": 8.5,
        "sharpe_ratio": 1.35,
        "win_rate": 0.65,
        "trades": 156,
        "equity_curve": [
            {"date": "2024-01-01", "value": 100000},
            {"date": "2024-06-01", "value": 108000},
            {"date": "2024-12-31", "value": 115200}
        ]
    }
    
    return {
        "success": True,
        "data": backtest_data
    }

# 新增的API端点

@app.get("/api/v1/storage/stats")
async def get_storage_stats():
    """获取存储统计信息"""
    return {
        "success": True,
        "data": {
            "database": {
                "connection_status": "connected",
                "table_count": 15,
                "last_check": datetime.now().isoformat()
            },
            "filesystem": {
                "total_space": 1000000000000,  # 1TB
                "used_space": 500000000000,    # 500GB
                "free_space": 500000000000,    # 500GB
                "usage_percent": 50.0,
                "status": "healthy"
            },
            "cache": {
                "type": "memory",
                "status": "active",
                "hit_rate": 85.5,
                "memory_usage": 45.2
            },
            "timestamp": datetime.now().isoformat()
        },
        "message": "存储统计信息获取成功"
    }

@app.get("/api/v1/compression/stats")
async def get_compression_stats():
    """获取压缩统计信息"""
    return {
        "success": True,
        "data": {
            "total_compressed_data": "1.2 GB",
            "compression_ratio": 0.65,
            "space_saved": "0.8 GB",
            "last_compression": datetime.now().isoformat(),
            "compression_algorithms": [
                {"name": "gzip", "ratio": 0.65, "speed": "fast"},
                {"name": "lz4", "ratio": 0.55, "speed": "very_fast"},
                {"name": "zstd", "ratio": 0.70, "speed": "medium"}
            ]
        },
        "message": "压缩统计信息获取成功"
    }

@app.get("/api/v1/monitoring/system")
async def get_system_monitoring():
    """获取系统监控信息"""
    return {
        "success": True,
        "data": {
            "timestamp": datetime.now().isoformat(),
            "system_resources": {
                "cpu_percent": random.uniform(20, 80),
                "memory_percent": random.uniform(40, 90),
                "memory_available_gb": random.uniform(2, 8),
                "disk_percent": random.uniform(30, 70),
                "disk_free_gb": random.uniform(100, 500)
            },
            "cache_performance": {
                "hit_rate_percent": random.uniform(70, 95),
                "total_requests": random.randint(1000, 5000),
                "cache_hits": random.randint(700, 4500),
                "cache_misses": random.randint(100, 1000)
            },
            "api_performance": {
                "total_operations": random.randint(50, 200),
                "avg_response_time": random.uniform(100, 500)
            }
        },
        "message": "系统监控信息获取成功"
    }

@app.post("/api/v1/auth/login")
async def login():
    """用户登录"""
    return {
        "success": True,
        "data": {
            "access_token": "mock_access_token_" + str(int(time.time())),
            "token_type": "bearer",
            "expires_in": 3600,
            "user": {
                "id": 1,
                "username": "demo_user",
                "email": "<EMAIL>",
                "role": "trader"
            }
        },
        "message": "登录成功"
    }

# 增强的市场数据API

@app.get("/api/v1/market/enhanced/stocks")
async def get_enhanced_stock_list():
    """获取增强的股票列表"""
    # 模拟增强的股票数据
    enhanced_stocks = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "market": "A股",
            "exchange": "SZSE",
            "industry": "银行",
            "price": 12.45 + random.uniform(-0.5, 0.5),
            "change": random.uniform(-0.3, 0.3),
            "change_percent": random.uniform(-2, 2),
            "volume": random.randint(100000, 1000000),
            "market_cap": 2400000000000,  # 市值
            "pe_ratio": 5.2,
            "pb_ratio": 0.8,
            "data_source": "enhanced"
        },
        {
            "symbol": "600036",
            "name": "招商银行",
            "market": "A股",
            "exchange": "SSE",
            "industry": "银行",
            "price": 35.67 + random.uniform(-1, 1),
            "change": random.uniform(-0.8, 0.8),
            "change_percent": random.uniform(-2, 2),
            "volume": random.randint(200000, 2000000),
            "market_cap": 9200000000000,
            "pe_ratio": 6.8,
            "pb_ratio": 1.1,
            "data_source": "enhanced"
        },
        {
            "symbol": "600519",
            "name": "贵州茅台",
            "market": "A股",
            "exchange": "SSE",
            "industry": "食品饮料",
            "price": 1680.00 + random.uniform(-20, 20),
            "change": random.uniform(-15, 15),
            "change_percent": random.uniform(-1, 1),
            "volume": random.randint(50000, 500000),
            "market_cap": 21000000000000,
            "pe_ratio": 28.5,
            "pb_ratio": 12.8,
            "data_source": "enhanced"
        }
    ]

    return {
        "success": True,
        "data": {
            "stocks": enhanced_stocks,
            "total": len(enhanced_stocks),
            "data_source": "enhanced_mock",
            "timestamp": datetime.now().isoformat()
        },
        "message": "增强股票数据获取成功"
    }

@app.get("/api/v1/market/enhanced/realtime/{symbol}")
async def get_enhanced_realtime_data(symbol: str):
    """获取增强的实时数据"""
    base_prices = {
        "000001": 12.45, "000002": 8.76, "600000": 7.89,
        "600036": 35.67, "000858": 128.90, "600519": 1680.00
    }

    base_price = base_prices.get(symbol, 10.0)

    # 生成五档行情数据
    bid_prices = []
    ask_prices = []

    for i in range(5):
        bid_price = base_price - (i + 1) * 0.01
        ask_price = base_price + (i + 1) * 0.01

        bid_prices.append({
            "price": round(bid_price, 2),
            "volume": random.randint(100, 1000) * 100
        })

        ask_prices.append({
            "price": round(ask_price, 2),
            "volume": random.randint(100, 1000) * 100
        })

    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "current_price": round(base_price + random.uniform(-0.1, 0.1), 2),
            "change": round(random.uniform(-0.3, 0.3), 2),
            "change_percent": round(random.uniform(-2, 2), 2),
            "volume": random.randint(100000, 1000000),
            "turnover": random.randint(1000000, 10000000),
            "bid_prices": bid_prices,
            "ask_prices": ask_prices,
            "timestamp": datetime.now().isoformat(),
            "data_source": "enhanced_mock"
        },
        "message": "增强实时数据获取成功"
    }

@app.get("/api/v1/market/data-source/status")
async def get_data_source_status():
    """获取数据源状态"""
    return {
        "success": True,
        "data": {
            "current_source": "mock",
            "available_sources": ["mock", "akshare", "tushare"],
            "akshare_available": False,  # 在实际环境中会检测
            "tushare_available": False,  # 在实际环境中会检测
            "cache_size": random.randint(10, 100),
            "last_update": datetime.now().isoformat()
        },
        "message": "数据源状态获取成功"
    }

# 增强的MiniQMT实盘交易API

@app.post("/api/v1/miniqmt/connect")
async def connect_miniqmt():
    """连接MiniQMT"""
    # 模拟连接过程
    connection_result = {
        "success": True,
        "message": "MiniQMT连接成功",
        "account_id": "demo_account_001",
        "session_id": f"session_{int(time.time())}",
        "timestamp": datetime.now().isoformat(),
        "account_info": {
            "account_name": "模拟交易账户",
            "account_type": "simulation",
            "server": "模拟服务器",
            "status": "active"
        }
    }

    return connection_result

@app.get("/api/v1/miniqmt/status")
async def get_miniqmt_status():
    """获取MiniQMT连接状态"""
    return {
        "success": True,
        "data": {
            "connected": True,
            "account_id": "demo_account_001",
            "session_id": f"session_{int(time.time())}",
            "base_url": "http://localhost:16099",
            "risk_stats": {
                "daily_pnl": random.uniform(-500, 1000),
                "daily_trades": random.randint(0, 20),
                "last_reset_date": datetime.now().date().isoformat()
            },
            "health_check": {
                "miniqmt_running": True,
                "api_responsive": True,
                "last_check": datetime.now().isoformat()
            }
        },
        "message": "MiniQMT状态获取成功"
    }

@app.post("/api/v1/miniqmt/order")
async def place_miniqmt_order():
    """MiniQMT下单"""
    # 模拟风险检查
    risk_check = {
        "passed": True,
        "risks": [],
        "order_value": random.uniform(1000, 50000),
        "daily_pnl": random.uniform(-500, 1000),
        "daily_trades": random.randint(0, 20)
    }

    order_id = f"miniqmt_order_{int(time.time())}_{random.randint(1000, 9999)}"

    return {
        "success": True,
        "message": "订单提交成功",
        "order_id": order_id,
        "order_data": {
            "account_id": "demo_account_001",
            "symbol": "000001",
            "side": "buy",
            "order_type": "limit",
            "quantity": 1000,
            "price": 12.45
        },
        "timestamp": datetime.now().isoformat(),
        "risk_check": risk_check
    }

@app.get("/api/v1/miniqmt/positions")
async def get_miniqmt_positions():
    """获取MiniQMT持仓"""
    positions = [
        {
            "symbol": "000001",
            "name": "平安银行",
            "quantity": 1000,
            "available_quantity": 1000,
            "avg_price": 12.30,
            "current_price": 12.45 + random.uniform(-0.1, 0.1),
            "market_value": 12450,
            "pnl": random.uniform(-200, 300),
            "pnl_percent": random.uniform(-2, 3)
        },
        {
            "symbol": "600036",
            "name": "招商银行",
            "quantity": 500,
            "available_quantity": 500,
            "avg_price": 35.20,
            "current_price": 35.67 + random.uniform(-0.2, 0.2),
            "market_value": 17835,
            "pnl": random.uniform(-300, 400),
            "pnl_percent": random.uniform(-1.5, 2.5)
        }
    ]

    total_market_value = sum(pos["market_value"] for pos in positions)
    total_pnl = sum(pos["pnl"] for pos in positions)

    return {
        "success": True,
        "data": {
            "positions": positions,
            "summary": {
                "total_positions": len(positions),
                "total_market_value": total_market_value,
                "total_pnl": total_pnl,
                "total_pnl_percent": (total_pnl / (total_market_value - total_pnl)) * 100 if total_market_value > total_pnl else 0
            }
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/miniqmt/orders")
async def get_miniqmt_orders():
    """获取MiniQMT订单"""
    orders = [
        {
            "order_id": f"miniqmt_order_{int(time.time())}_000001",
            "symbol": "000001",
            "name": "平安银行",
            "side": "buy",
            "order_type": "limit",
            "quantity": 1000,
            "price": 12.30,
            "filled_quantity": 1000,
            "status": "filled",
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        },
        {
            "order_id": f"miniqmt_order_{int(time.time())}_600036",
            "symbol": "600036",
            "name": "招商银行",
            "side": "buy",
            "order_type": "limit",
            "quantity": 500,
            "price": 35.20,
            "filled_quantity": 0,
            "status": "pending",
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
    ]

    return {
        "success": True,
        "data": {
            "orders": orders,
            "total": len(orders)
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/miniqmt/account")
async def get_miniqmt_account():
    """获取MiniQMT账户信息"""
    account_info = {
        "account_id": "demo_account_001",
        "account_name": "模拟交易账户",
        "total_assets": 1000000.00 + random.uniform(-10000, 20000),
        "available_cash": 970000.00 + random.uniform(-5000, 10000),
        "market_value": 30285.00 + random.uniform(-1000, 2000),
        "frozen_cash": random.uniform(0, 5000),
        "total_pnl": random.uniform(-1000, 2000),
        "today_pnl": random.uniform(-500, 1000),
        "currency": "CNY",
        "risk_level": "低风险"
    }

    return {
        "success": True,
        "data": account_info,
        "timestamp": datetime.now().isoformat()
    }

# 高级交易功能API

@app.post("/api/v1/trading/advanced/order")
async def place_advanced_order():
    """下高级订单"""
    # 模拟高级订单数据
    order_types = ["stop_loss", "stop_profit", "conditional", "twap", "vwap", "iceberg"]
    selected_type = random.choice(order_types)

    order_id = f"advanced_order_{int(time.time())}_{random.randint(1000, 9999)}"

    return {
        "success": True,
        "message": "高级订单提交成功",
        "order_id": order_id,
        "order_type": selected_type,
        "estimated_execution_time": {
            "stop_loss": "条件触发时",
            "stop_profit": "条件触发时",
            "conditional": "条件触发时",
            "twap": "5分钟",
            "vwap": "5分钟",
            "iceberg": "分批执行"
        }.get(selected_type, "未知"),
        "algorithm_details": {
            "twap": {
                "slice_count": 10,
                "time_window": 300,
                "slice_interval": 30
            },
            "vwap": {
                "time_window": 300,
                "volume_weighted": True
            },
            "iceberg": {
                "visible_quantity": 100,
                "total_quantity": 1000
            }
        }.get(selected_type, {}),
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/trading/advanced/orders")
async def get_advanced_orders():
    """获取高级订单列表"""
    orders = [
        {
            "order_id": f"advanced_order_{int(time.time())}_001",
            "symbol": "000001",
            "name": "平安银行",
            "side": "buy",
            "order_type": "twap",
            "quantity": 1000,
            "filled_quantity": 600,
            "avg_price": 12.42,
            "status": "partial",
            "progress": 60,
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "estimated_completion": (datetime.now() + timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M:%S"),
            "child_orders": [
                {"slice_id": "TWAP_1", "quantity": 100, "price": 12.40, "status": "filled"},
                {"slice_id": "TWAP_2", "quantity": 100, "price": 12.41, "status": "filled"},
                {"slice_id": "TWAP_3", "quantity": 100, "price": 12.43, "status": "filled"}
            ]
        },
        {
            "order_id": f"advanced_order_{int(time.time())}_002",
            "symbol": "600036",
            "name": "招商银行",
            "side": "sell",
            "order_type": "stop_loss",
            "quantity": 500,
            "filled_quantity": 0,
            "stop_price": 35.00,
            "current_price": 35.67,
            "status": "pending",
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "trigger_condition": "价格跌破 35.00 元"
        },
        {
            "order_id": f"advanced_order_{int(time.time())}_003",
            "symbol": "600519",
            "name": "贵州茅台",
            "side": "buy",
            "order_type": "iceberg",
            "quantity": 100,
            "filled_quantity": 30,
            "visible_quantity": 10,
            "status": "partial",
            "progress": 30,
            "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "child_orders": [
                {"slice_id": "ICEBERG_1", "quantity": 10, "price": 1678.00, "status": "filled"},
                {"slice_id": "ICEBERG_2", "quantity": 10, "price": 1679.50, "status": "filled"},
                {"slice_id": "ICEBERG_3", "quantity": 10, "price": 1680.20, "status": "filled"}
            ]
        }
    ]

    return {
        "success": True,
        "data": {
            "orders": orders,
            "total": len(orders),
            "active_count": sum(1 for order in orders if order["status"] in ["pending", "partial"]),
            "completed_count": sum(1 for order in orders if order["status"] == "filled")
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/trading/advanced/order/{order_id}")
async def get_advanced_order_detail(order_id: str):
    """获取高级订单详情"""
    # 模拟订单详情
    order_detail = {
        "order_id": order_id,
        "symbol": "000001",
        "name": "平安银行",
        "side": "buy",
        "order_type": "twap",
        "quantity": 1000,
        "filled_quantity": 600,
        "remaining_quantity": 400,
        "avg_price": 12.42,
        "status": "partial",
        "progress": 60,
        "create_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "algorithm_params": {
            "time_window": 300,
            "slice_count": 10,
            "slice_interval": 30,
            "completed_slices": 6,
            "remaining_slices": 4
        },
        "execution_log": [
            {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "event": "订单创建",
                "details": "TWAP算法订单创建成功"
            },
            {
                "timestamp": (datetime.now() - timedelta(minutes=3)).strftime("%Y-%m-%d %H:%M:%S"),
                "event": "切片执行",
                "details": "TWAP_1 执行完成: 100股 @ 12.40元"
            },
            {
                "timestamp": (datetime.now() - timedelta(minutes=2)).strftime("%Y-%m-%d %H:%M:%S"),
                "event": "切片执行",
                "details": "TWAP_2 执行完成: 100股 @ 12.41元"
            }
        ],
        "child_orders": [
            {"slice_id": "TWAP_1", "quantity": 100, "price": 12.40, "status": "filled", "timestamp": "2025-08-06 09:30:00"},
            {"slice_id": "TWAP_2", "quantity": 100, "price": 12.41, "status": "filled", "timestamp": "2025-08-06 09:30:30"},
            {"slice_id": "TWAP_3", "quantity": 100, "price": 12.43, "status": "filled", "timestamp": "2025-08-06 09:31:00"},
            {"slice_id": "TWAP_4", "quantity": 100, "price": 12.42, "status": "filled", "timestamp": "2025-08-06 09:31:30"},
            {"slice_id": "TWAP_5", "quantity": 100, "price": 12.44, "status": "filled", "timestamp": "2025-08-06 09:32:00"},
            {"slice_id": "TWAP_6", "quantity": 100, "price": 12.41, "status": "filled", "timestamp": "2025-08-06 09:32:30"}
        ],
        "performance_metrics": {
            "execution_rate": 60,
            "price_improvement": 0.02,
            "market_impact": 0.01,
            "timing_efficiency": 85
        }
    }

    return {
        "success": True,
        "data": order_detail,
        "timestamp": datetime.now().isoformat()
    }

@app.post("/api/v1/trading/advanced/cancel/{order_id}")
async def cancel_advanced_order(order_id: str):
    """取消高级订单"""
    return {
        "success": True,
        "message": "高级订单已取消",
        "order_id": order_id,
        "cancelled_time": datetime.now().isoformat(),
        "cancellation_details": {
            "reason": "用户主动取消",
            "filled_quantity": random.randint(0, 500),
            "remaining_quantity": random.randint(100, 1000),
            "refund_amount": random.uniform(1000, 10000)
        }
    }

@app.get("/api/v1/trading/algorithms")
async def get_trading_algorithms():
    """获取可用的交易算法"""
    algorithms = [
        {
            "name": "TWAP",
            "display_name": "时间加权平均价格",
            "description": "在指定时间内均匀分割订单，减少市场冲击",
            "suitable_for": ["大额订单", "流动性一般的股票"],
            "parameters": [
                {"name": "time_window", "display_name": "时间窗口", "type": "integer", "unit": "秒", "default": 300},
                {"name": "slice_count", "display_name": "切片数量", "type": "integer", "default": 10}
            ],
            "risk_level": "低",
            "execution_time": "可控"
        },
        {
            "name": "VWAP",
            "display_name": "成交量加权平均价格",
            "description": "根据历史成交量分布执行订单，追求接近VWAP的成交价格",
            "suitable_for": ["大额订单", "高流动性股票"],
            "parameters": [
                {"name": "time_window", "display_name": "时间窗口", "type": "integer", "unit": "秒", "default": 300},
                {"name": "volume_profile", "display_name": "成交量配置", "type": "string", "default": "historical"}
            ],
            "risk_level": "低",
            "execution_time": "可控"
        },
        {
            "name": "Iceberg",
            "display_name": "冰山单",
            "description": "只显示部分订单数量，隐藏真实订单规模",
            "suitable_for": ["超大额订单", "避免市场冲击"],
            "parameters": [
                {"name": "visible_quantity", "display_name": "可见数量", "type": "integer", "default": 100},
                {"name": "refresh_interval", "display_name": "刷新间隔", "type": "integer", "unit": "秒", "default": 5}
            ],
            "risk_level": "中",
            "execution_time": "较长"
        },
        {
            "name": "Smart",
            "display_name": "智能算法",
            "description": "根据市场状况自动选择最优执行策略",
            "suitable_for": ["所有订单类型", "自适应执行"],
            "parameters": [
                {"name": "aggressiveness", "display_name": "激进程度", "type": "string", "options": ["conservative", "moderate", "aggressive"], "default": "moderate"}
            ],
            "risk_level": "中",
            "execution_time": "自适应"
        }
    ]

    return {
        "success": True,
        "data": {
            "algorithms": algorithms,
            "total": len(algorithms)
        },
        "message": "交易算法列表获取成功"
    }

# 真实数据源API

@app.get("/api/v1/real-data/stocks/realtime")
async def get_real_stock_data():
    """获取真实股票实时数据"""
    symbols = ["000001", "600036", "600519"]

    # 模拟真实数据源响应
    real_data = {}
    for symbol in symbols:
        base_price = {"000001": 12.45, "600036": 35.67, "600519": 1680.00}[symbol]

        real_data[symbol] = {
            "symbol": symbol,
            "name": {"000001": "平安银行", "600036": "招商银行", "600519": "贵州茅台"}[symbol],
            "current_price": base_price + random.uniform(-0.5, 0.5),
            "change": random.uniform(-0.3, 0.3),
            "change_percent": random.uniform(-2, 2),
            "volume": random.randint(100000, 1000000),
            "turnover": random.randint(1000000, 10000000),
            "high": base_price + random.uniform(0, 0.5),
            "low": base_price - random.uniform(0, 0.5),
            "open": base_price + random.uniform(-0.2, 0.2),
            "pe_ratio": random.uniform(5, 30),
            "pb_ratio": random.uniform(0.5, 5),
            "market_cap": random.randint(1000000000, 100000000000),
            "data_source": "tushare",
            "timestamp": datetime.now().isoformat()
        }

    return {
        "success": True,
        "data": real_data,
        "source_info": {
            "primary_source": "tushare",
            "fallback_source": "akshare",
            "cache_hit": random.choice([True, False]),
            "response_time_ms": random.randint(50, 200)
        },
        "message": "真实股票数据获取成功"
    }

@app.get("/api/v1/real-data/crypto/realtime")
async def get_real_crypto_data():
    """获取真实加密货币数据"""
    symbols = ["BTC", "ETH", "BNB"]

    real_data = {}
    for symbol in symbols:
        base_price = {"BTC": 45000, "ETH": 3000, "BNB": 300}[symbol]

        real_data[symbol] = {
            "symbol": symbol,
            "current_price": base_price + random.uniform(-1000, 1000),
            "change": random.uniform(-500, 500),
            "change_percent": random.uniform(-5, 5),
            "volume_24h": random.randint(1000000, 10000000),
            "market_cap": random.randint(100000000000, 1000000000000),
            "high_24h": base_price + random.uniform(0, 1000),
            "low_24h": base_price - random.uniform(0, 1000),
            "data_source": "binance",
            "timestamp": datetime.now().isoformat()
        }

    return {
        "success": True,
        "data": real_data,
        "source_info": {
            "primary_source": "binance",
            "fallback_source": "coinbase",
            "cache_hit": random.choice([True, False]),
            "response_time_ms": random.randint(30, 150)
        },
        "message": "真实加密货币数据获取成功"
    }

@app.get("/api/v1/real-data/forex/realtime")
async def get_real_forex_data():
    """获取真实外汇数据"""
    pairs = ["USD/CNY", "EUR/USD", "GBP/USD"]

    real_data = {}
    for pair in pairs:
        base_rate = {"USD/CNY": 7.2, "EUR/USD": 1.1, "GBP/USD": 1.3}[pair]

        real_data[pair] = {
            "pair": pair,
            "rate": base_rate + random.uniform(-0.1, 0.1),
            "change": random.uniform(-0.05, 0.05),
            "change_percent": random.uniform(-1, 1),
            "bid": base_rate - 0.001,
            "ask": base_rate + 0.001,
            "high_24h": base_rate + random.uniform(0, 0.05),
            "low_24h": base_rate - random.uniform(0, 0.05),
            "data_source": "alpha_vantage",
            "timestamp": datetime.now().isoformat()
        }

    return {
        "success": True,
        "data": real_data,
        "source_info": {
            "primary_source": "alpha_vantage",
            "fallback_source": "fixer",
            "cache_hit": random.choice([True, False]),
            "response_time_ms": random.randint(100, 300)
        },
        "message": "真实外汇数据获取成功"
    }

@app.get("/api/v1/real-data/sources/status")
async def get_data_sources_status():
    """获取数据源状态"""
    sources = {
        "tushare": {
            "enabled": True,
            "status": "active",
            "rate_limit": 200,
            "current_requests": random.randint(50, 150),
            "last_request": datetime.now().isoformat(),
            "success_rate": random.uniform(95, 99.9),
            "avg_response_time": random.randint(100, 300)
        },
        "akshare": {
            "enabled": True,
            "status": "active",
            "rate_limit": 100,
            "current_requests": random.randint(20, 80),
            "last_request": datetime.now().isoformat(),
            "success_rate": random.uniform(90, 98),
            "avg_response_time": random.randint(200, 500)
        },
        "binance": {
            "enabled": True,
            "status": "active",
            "rate_limit": 1200,
            "current_requests": random.randint(100, 800),
            "last_request": datetime.now().isoformat(),
            "success_rate": random.uniform(98, 99.9),
            "avg_response_time": random.randint(50, 150)
        },
        "alpha_vantage": {
            "enabled": False,
            "status": "disabled",
            "rate_limit": 5,
            "current_requests": 0,
            "last_request": None,
            "success_rate": 0,
            "avg_response_time": 0,
            "reason": "API密钥未配置"
        }
    }

    return {
        "success": True,
        "data": {
            "sources": sources,
            "total_sources": len(sources),
            "active_sources": sum(1 for s in sources.values() if s["status"] == "active"),
            "cache_size": random.randint(50, 200),
            "last_cache_clear": (datetime.now() - timedelta(hours=1)).isoformat()
        },
        "message": "数据源状态获取成功"
    }

# 真实交易平台API

@app.post("/api/v1/real-trading/{platform}/order")
async def place_real_order(platform: str):
    """在真实交易平台下单"""
    platforms = ["binance", "alpaca", "interactive_brokers", "futu"]

    if platform not in platforms:
        return {
            "success": False,
            "message": f"不支持的交易平台: {platform}"
        }

    # 模拟真实交易平台下单
    order_id = f"real_{platform}_{int(time.time())}_{random.randint(1000, 9999)}"

    platform_configs = {
        "binance": {
            "asset_type": "crypto",
            "supported_symbols": ["BTCUSDT", "ETHUSDT", "BNBUSDT"],
            "order_types": ["market", "limit", "stop_loss"],
            "testnet": True
        },
        "alpaca": {
            "asset_type": "stock",
            "supported_symbols": ["AAPL", "GOOGL", "TSLA"],
            "order_types": ["market", "limit", "stop", "stop_limit"],
            "paper_trading": True
        },
        "interactive_brokers": {
            "asset_type": "multi",
            "supported_symbols": ["AAPL", "BTCUSD", "EURUSD"],
            "order_types": ["market", "limit", "stop", "trail"],
            "demo_account": True
        },
        "futu": {
            "asset_type": "stock",
            "supported_symbols": ["00700", "09988", "AAPL"],
            "order_types": ["market", "limit"],
            "simulation": True
        }
    }

    config = platform_configs[platform]

    return {
        "success": True,
        "message": f"{platform}订单提交成功",
        "order_id": order_id,
        "platform": platform,
        "platform_config": config,
        "order_details": {
            "symbol": random.choice(config["supported_symbols"]),
            "side": random.choice(["buy", "sell"]),
            "order_type": random.choice(config["order_types"]),
            "quantity": random.uniform(0.1, 10) if config["asset_type"] == "crypto" else random.randint(1, 100),
            "price": random.uniform(100, 50000),
            "status": "pending",
            "create_time": datetime.now().isoformat()
        },
        "risk_check": {
            "passed": True,
            "daily_trades": random.randint(1, 20),
            "daily_pnl": random.uniform(-1000, 2000),
            "position_limit_used": random.uniform(10, 80)
        }
    }

@app.get("/api/v1/real-trading/{platform}/positions")
async def get_real_positions(platform: str):
    """获取真实交易平台持仓"""
    platforms = ["binance", "alpaca", "interactive_brokers", "futu"]

    if platform not in platforms:
        return {
            "success": False,
            "message": f"不支持的交易平台: {platform}"
        }

    # 模拟不同平台的持仓数据
    if platform == "binance":
        positions = [
            {
                "symbol": "BTCUSDT",
                "quantity": 0.5,
                "avg_price": 44000,
                "current_price": 45000,
                "unrealized_pnl": 500,
                "unrealized_pnl_percent": 2.27,
                "margin_used": 22000
            },
            {
                "symbol": "ETHUSDT",
                "quantity": 2.0,
                "avg_price": 2900,
                "current_price": 3000,
                "unrealized_pnl": 200,
                "unrealized_pnl_percent": 3.45,
                "margin_used": 5800
            }
        ]
    elif platform == "alpaca":
        positions = [
            {
                "symbol": "AAPL",
                "quantity": 50,
                "avg_price": 150,
                "current_price": 155,
                "unrealized_pnl": 250,
                "unrealized_pnl_percent": 3.33,
                "market_value": 7750
            },
            {
                "symbol": "GOOGL",
                "quantity": 10,
                "avg_price": 2800,
                "current_price": 2850,
                "unrealized_pnl": 500,
                "unrealized_pnl_percent": 1.79,
                "market_value": 28500
            }
        ]
    else:
        positions = [
            {
                "symbol": "DEMO_ASSET",
                "quantity": 100,
                "avg_price": 100,
                "current_price": 105,
                "unrealized_pnl": 500,
                "unrealized_pnl_percent": 5.0,
                "market_value": 10500
            }
        ]

    total_value = sum(pos.get("market_value", pos["quantity"] * pos["current_price"]) for pos in positions)
    total_pnl = sum(pos["unrealized_pnl"] for pos in positions)

    return {
        "success": True,
        "data": {
            "platform": platform,
            "positions": positions,
            "summary": {
                "total_positions": len(positions),
                "total_market_value": total_value,
                "total_unrealized_pnl": total_pnl,
                "total_pnl_percent": (total_pnl / (total_value - total_pnl)) * 100 if total_value > total_pnl else 0
            },
            "account_info": {
                "account_id": f"{platform}_demo_account",
                "account_type": "demo" if platform != "binance" else "testnet",
                "currency": "USDT" if platform == "binance" else "USD"
            }
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/real-trading/{platform}/account")
async def get_real_account_info(platform: str):
    """获取真实交易平台账户信息"""
    platforms = ["binance", "alpaca", "interactive_brokers", "futu"]

    if platform not in platforms:
        return {
            "success": False,
            "message": f"不支持的交易平台: {platform}"
        }

    # 模拟不同平台的账户信息
    if platform == "binance":
        account_info = {
            "account_type": "spot_testnet",
            "total_balance": 50000.0,
            "available_balance": 45000.0,
            "locked_balance": 5000.0,
            "currency": "USDT",
            "maker_commission": 0.1,
            "taker_commission": 0.1,
            "can_trade": True,
            "can_withdraw": False,
            "can_deposit": True
        }
    elif platform == "alpaca":
        account_info = {
            "account_type": "paper",
            "portfolio_value": 100000.0,
            "buying_power": 95000.0,
            "cash": 90000.0,
            "currency": "USD",
            "day_trade_count": 2,
            "pattern_day_trader": False,
            "trading_blocked": False,
            "transfers_blocked": False
        }
    elif platform == "interactive_brokers":
        account_info = {
            "account_type": "demo",
            "net_liquidation": 100000.0,
            "available_funds": 95000.0,
            "excess_liquidity": 90000.0,
            "currency": "USD",
            "margin_req": 5000.0,
            "maintenance_margin": 3000.0
        }
    else:  # futu
        account_info = {
            "account_type": "simulation",
            "total_assets": 1000000.0,
            "available_cash": 950000.0,
            "market_value": 50000.0,
            "currency": "HKD",
            "frozen_cash": 0.0,
            "max_power_short": 0.0
        }

    return {
        "success": True,
        "data": {
            "platform": platform,
            "account_info": account_info,
            "connection_status": {
                "connected": True,
                "last_heartbeat": datetime.now().isoformat(),
                "session_duration": random.randint(3600, 28800),
                "api_rate_limit": {
                    "requests_per_minute": {"binance": 1200, "alpaca": 200, "interactive_brokers": 50, "futu": 30}[platform],
                    "current_usage": random.randint(10, 100)
                }
            },
            "trading_permissions": {
                "can_trade_stocks": platform in ["alpaca", "interactive_brokers", "futu"],
                "can_trade_crypto": platform in ["binance", "interactive_brokers"],
                "can_trade_forex": platform == "interactive_brokers",
                "can_trade_options": platform in ["alpaca", "interactive_brokers"],
                "can_short_sell": platform in ["alpaca", "interactive_brokers"]
            }
        },
        "timestamp": datetime.now().isoformat()
    }

@app.get("/api/v1/real-trading/platforms/status")
async def get_trading_platforms_status():
    """获取所有交易平台状态"""
    platforms = {
        "binance": {
            "enabled": True,
            "status": "connected",
            "asset_type": "crypto",
            "connection_type": "testnet",
            "last_ping": datetime.now().isoformat(),
            "response_time_ms": random.randint(50, 150),
            "success_rate": random.uniform(98, 99.9),
            "daily_trades": random.randint(5, 50),
            "api_limits": {
                "requests_per_minute": 1200,
                "current_usage": random.randint(100, 800)
            }
        },
        "alpaca": {
            "enabled": True,
            "status": "connected",
            "asset_type": "stock",
            "connection_type": "paper",
            "last_ping": datetime.now().isoformat(),
            "response_time_ms": random.randint(100, 300),
            "success_rate": random.uniform(95, 99),
            "daily_trades": random.randint(2, 20),
            "api_limits": {
                "requests_per_minute": 200,
                "current_usage": random.randint(10, 100)
            }
        },
        "interactive_brokers": {
            "enabled": False,
            "status": "disconnected",
            "asset_type": "multi",
            "connection_type": "demo",
            "last_ping": None,
            "response_time_ms": 0,
            "success_rate": 0,
            "daily_trades": 0,
            "error": "TWS Gateway未运行",
            "api_limits": {
                "requests_per_minute": 50,
                "current_usage": 0
            }
        },
        "futu": {
            "enabled": False,
            "status": "disconnected",
            "asset_type": "stock",
            "connection_type": "simulation",
            "last_ping": None,
            "response_time_ms": 0,
            "success_rate": 0,
            "daily_trades": 0,
            "error": "API密钥未配置",
            "api_limits": {
                "requests_per_minute": 30,
                "current_usage": 0
            }
        }
    }

    active_platforms = sum(1 for p in platforms.values() if p["status"] == "connected")
    total_daily_trades = sum(p["daily_trades"] for p in platforms.values())

    return {
        "success": True,
        "data": {
            "platforms": platforms,
            "summary": {
                "total_platforms": len(platforms),
                "active_platforms": active_platforms,
                "total_daily_trades": total_daily_trades,
                "supported_assets": ["crypto", "stock", "forex", "options"],
                "last_update": datetime.now().isoformat()
            },
            "global_settings": {
                "risk_management_enabled": True,
                "auto_reconnect": True,
                "max_daily_loss": 10000,
                "max_position_size": 100000,
                "trading_hours_only": False
            }
        },
        "message": "交易平台状态获取成功"
    }

# 增强的用户认证和权限管理API

@app.post("/api/v1/auth/register")
async def register_user():
    """用户注册"""
    # 模拟注册数据
    registration_data = {
        "username": f"user_{random.randint(1000, 9999)}",
        "email": f"user{random.randint(1000, 9999)}@example.com",
        "full_name": "新用户",
        "trading_level": random.choice(["beginner", "intermediate", "advanced"]),
        "risk_tolerance": random.choice(["conservative", "moderate", "aggressive"])
    }

    user_id = random.randint(1000, 9999)

    return {
        "success": True,
        "message": "注册成功，请查收邮箱验证邮件",
        "user_id": user_id,
        "verification_token": f"verify_{int(time.time())}_{user_id}",
        "user_info": {
            "id": user_id,
            "username": registration_data["username"],
            "email": registration_data["email"],
            "full_name": registration_data["full_name"],
            "roles": ["viewer"],
            "trading_level": registration_data["trading_level"],
            "risk_tolerance": registration_data["risk_tolerance"],
            "is_verified": False,
            "created_at": datetime.now().isoformat()
        },
        "password_policy": {
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_numbers": True,
            "require_special_chars": True,
            "max_age_days": 90
        }
    }

@app.post("/api/v1/auth/login/enhanced")
async def enhanced_login():
    """增强登录"""
    # 模拟登录成功
    user_id = random.randint(1, 1000)
    session_id = f"session_{int(time.time())}_{user_id}"
    access_token = f"access_{int(time.time())}_{random.randint(1000, 9999)}"
    refresh_token = f"refresh_{int(time.time())}_{random.randint(1000, 9999)}"

    user_roles = random.choice([
        ["admin"], ["trader"], ["analyst"], ["viewer"],
        ["senior_trader"], ["trader", "analyst"]
    ])

    # 根据角色分配权限
    permissions_map = {
        "admin": ["user.create", "user.read", "user.update", "user.delete", "system.admin", "trading.execute"],
        "senior_trader": ["trading.create", "trading.read", "trading.update", "trading.execute", "data.read", "account.update"],
        "trader": ["trading.create", "trading.read", "trading.execute", "data.read", "account.read"],
        "analyst": ["data.read", "data.export", "account.read"],
        "viewer": ["data.read", "account.read"]
    }

    user_permissions = []
    for role in user_roles:
        user_permissions.extend(permissions_map.get(role, []))
    user_permissions = list(set(user_permissions))  # 去重

    return {
        "success": True,
        "message": "登录成功",
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": 1800,  # 30分钟
        "session_id": session_id,
        "user_info": {
            "id": user_id,
            "username": f"user_{user_id}",
            "email": f"user{user_id}@example.com",
            "full_name": "测试用户",
            "roles": user_roles,
            "permissions": user_permissions,
            "is_active": True,
            "is_verified": True,
            "is_superuser": "admin" in user_roles,
            "trading_level": random.choice(["beginner", "intermediate", "advanced", "professional"]),
            "risk_tolerance": random.choice(["conservative", "moderate", "aggressive"]),
            "last_login": datetime.now().isoformat(),
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat()
        },
        "session_info": {
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "device_info": "Windows 10 Desktop",
            "location": "北京, 中国",
            "created_at": datetime.now().isoformat(),
            "expires_at": (datetime.now() + timedelta(days=7)).isoformat()
        },
        "security_info": {
            "password_expires_in_days": random.randint(30, 90),
            "require_password_change": False,
            "two_factor_enabled": random.choice([True, False]),
            "last_password_change": (datetime.now() - timedelta(days=random.randint(1, 60))).isoformat()
        }
    }

@app.get("/api/v1/auth/user/profile")
async def get_user_profile():
    """获取用户配置文件"""
    user_id = random.randint(1, 1000)

    return {
        "success": True,
        "data": {
            "id": user_id,
            "username": f"user_{user_id}",
            "email": f"user{user_id}@example.com",
            "full_name": "测试用户",
            "phone": "+86 138****8888",
            "avatar_url": f"https://api.dicebear.com/7.x/avataaars/svg?seed={user_id}",
            "roles": random.choice([["admin"], ["trader"], ["analyst"], ["viewer"]]),
            "permissions": ["data.read", "trading.read", "account.read"],
            "is_active": True,
            "is_verified": True,
            "is_superuser": False,
            "trading_level": random.choice(["beginner", "intermediate", "advanced", "professional"]),
            "risk_tolerance": random.choice(["conservative", "moderate", "aggressive"]),
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "theme": random.choice(["light", "dark", "auto"]),
            "created_at": (datetime.now() - timedelta(days=random.randint(1, 365))).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "last_login": datetime.now().isoformat(),
            "login_count": random.randint(1, 1000),
            "preferences": {
                "email_notifications": True,
                "sms_notifications": False,
                "trading_alerts": True,
                "market_updates": True,
                "newsletter": False
            },
            "security_settings": {
                "two_factor_enabled": random.choice([True, False]),
                "login_alerts": True,
                "session_timeout": 30,
                "password_expires_in_days": random.randint(30, 90)
            }
        },
        "message": "用户配置文件获取成功"
    }

@app.get("/api/v1/auth/user/sessions")
async def get_user_sessions():
    """获取用户会话列表"""
    sessions = []

    for i in range(random.randint(1, 5)):
        session_id = f"session_{int(time.time())}_{i}"
        is_current = i == 0

        sessions.append({
            "session_id": session_id,
            "ip_address": f"192.168.1.{random.randint(100, 200)}",
            "user_agent": random.choice([
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15"
            ]),
            "device_info": random.choice(["Windows 10 Desktop", "macOS Laptop", "iPhone Mobile"]),
            "location": random.choice(["北京, 中国", "上海, 中国", "深圳, 中国"]),
            "is_active": True,
            "is_current": is_current,
            "created_at": (datetime.now() - timedelta(hours=random.randint(1, 72))).isoformat(),
            "last_activity": (datetime.now() - timedelta(minutes=random.randint(1, 60))).isoformat(),
            "expires_at": (datetime.now() + timedelta(days=random.randint(1, 7))).isoformat()
        })

    return {
        "success": True,
        "data": {
            "sessions": sessions,
            "total": len(sessions),
            "active_count": len([s for s in sessions if s["is_active"]]),
            "current_session": sessions[0]["session_id"] if sessions else None
        },
        "message": "用户会话列表获取成功"
    }

@app.get("/api/v1/auth/roles")
async def get_available_roles():
    """获取可用角色列表"""
    roles = [
        {
            "name": "super_admin",
            "display_name": "超级管理员",
            "description": "拥有所有权限的系统管理员",
            "permissions_count": 15,
            "is_system": True,
            "user_count": 1
        },
        {
            "name": "admin",
            "display_name": "管理员",
            "description": "系统管理员，除超级权限外的所有权限",
            "permissions_count": 12,
            "is_system": True,
            "user_count": 3
        },
        {
            "name": "senior_trader",
            "display_name": "高级交易员",
            "description": "有经验的交易员，可执行高级交易策略",
            "permissions_count": 8,
            "is_system": False,
            "user_count": 15
        },
        {
            "name": "trader",
            "display_name": "交易员",
            "description": "普通交易员，可进行基本交易操作",
            "permissions_count": 6,
            "is_system": False,
            "user_count": 45
        },
        {
            "name": "analyst",
            "display_name": "分析师",
            "description": "数据分析师，专注于市场分析",
            "permissions_count": 4,
            "is_system": False,
            "user_count": 20
        },
        {
            "name": "viewer",
            "display_name": "观察者",
            "description": "只读用户，可查看基本信息",
            "permissions_count": 2,
            "is_system": False,
            "user_count": 100
        }
    ]

    return {
        "success": True,
        "data": {
            "roles": roles,
            "total": len(roles),
            "system_roles": len([r for r in roles if r["is_system"]]),
            "custom_roles": len([r for r in roles if not r["is_system"]])
        },
        "message": "角色列表获取成功"
    }

@app.get("/api/v1/auth/permissions")
async def get_available_permissions():
    """获取可用权限列表"""
    permissions = [
        # 用户管理权限
        {"name": "user.create", "display_name": "创建用户", "resource": "user", "action": "create", "description": "创建新用户账户"},
        {"name": "user.read", "display_name": "查看用户", "resource": "user", "action": "read", "description": "查看用户信息"},
        {"name": "user.update", "display_name": "更新用户", "resource": "user", "action": "update", "description": "修改用户信息"},
        {"name": "user.delete", "display_name": "删除用户", "resource": "user", "action": "delete", "description": "删除用户账户"},

        # 交易权限
        {"name": "trading.create", "display_name": "创建交易", "resource": "trading", "action": "create", "description": "创建新的交易订单"},
        {"name": "trading.read", "display_name": "查看交易", "resource": "trading", "action": "read", "description": "查看交易记录"},
        {"name": "trading.update", "display_name": "修改交易", "resource": "trading", "action": "update", "description": "修改交易订单"},
        {"name": "trading.delete", "display_name": "删除交易", "resource": "trading", "action": "delete", "description": "删除交易订单"},
        {"name": "trading.execute", "display_name": "执行交易", "resource": "trading", "action": "execute", "description": "执行交易操作"},

        # 数据权限
        {"name": "data.read", "display_name": "查看数据", "resource": "data", "action": "read", "description": "查看市场数据"},
        {"name": "data.export", "display_name": "导出数据", "resource": "data", "action": "export", "description": "导出数据文件"},

        # 系统权限
        {"name": "system.admin", "display_name": "系统管理", "resource": "system", "action": "admin", "description": "系统管理权限"},
        {"name": "system.monitor", "display_name": "系统监控", "resource": "system", "action": "monitor", "description": "系统监控权限"},
        {"name": "system.config", "display_name": "系统配置", "resource": "system", "action": "config", "description": "系统配置权限"},

        # 账户权限
        {"name": "account.read", "display_name": "查看账户", "resource": "account", "action": "read", "description": "查看账户信息"},
        {"name": "account.update", "display_name": "更新账户", "resource": "account", "action": "update", "description": "更新账户信息"},
        {"name": "account.deposit", "display_name": "账户充值", "resource": "account", "action": "deposit", "description": "账户充值操作"},
        {"name": "account.withdraw", "display_name": "账户提现", "resource": "account", "action": "withdraw", "description": "账户提现操作"}
    ]

    # 按资源分组
    grouped_permissions = {}
    for perm in permissions:
        resource = perm["resource"]
        if resource not in grouped_permissions:
            grouped_permissions[resource] = []
        grouped_permissions[resource].append(perm)

    return {
        "success": True,
        "data": {
            "permissions": permissions,
            "grouped_permissions": grouped_permissions,
            "total": len(permissions),
            "resources": list(grouped_permissions.keys())
        },
        "message": "权限列表获取成功"
    }

# 增强的监控和告警系统API

@app.get("/api/v1/monitoring/dashboard")
async def get_monitoring_dashboard():
    """获取监控仪表板"""
    # 模拟监控仪表板数据
    current_time = datetime.now()

    # 系统健康状态
    health_score = random.randint(75, 98)
    if health_score >= 90:
        health_status = "healthy"
        status_text = "系统运行正常"
    elif health_score >= 70:
        health_status = "warning"
        status_text = "系统存在一些问题"
    else:
        health_status = "critical"
        status_text = "系统存在严重问题"

    # 活跃告警
    active_alerts = []
    alert_levels = ["info", "warning", "error", "critical"]
    alert_types = ["system", "application", "security", "performance"]

    for i in range(random.randint(0, 5)):
        alert = {
            "id": f"alert_{int(time.time())}_{i}",
            "level": random.choice(alert_levels),
            "type": random.choice(alert_types),
            "title": random.choice([
                "CPU使用率过高", "内存使用率过高", "磁盘空间不足",
                "API响应时间过长", "错误率过高", "数据库连接数过高"
            ]),
            "message": "系统指标超过阈值",
            "timestamp": (current_time - timedelta(minutes=random.randint(1, 60))).isoformat(),
            "source": f"rule_{random.randint(1, 10)}",
            "resolved": False
        }
        active_alerts.append(alert)

    # 指标摘要
    metrics_summary = {
        "cpu": {
            "avg": random.uniform(30, 70),
            "max": random.uniform(70, 95),
            "min": random.uniform(10, 30),
            "current": random.uniform(40, 80)
        },
        "memory": {
            "avg": random.uniform(40, 70),
            "max": random.uniform(70, 90),
            "min": random.uniform(20, 40),
            "current": random.uniform(50, 80)
        },
        "response_time": {
            "avg": random.uniform(200, 500),
            "max": random.uniform(500, 2000),
            "min": random.uniform(50, 200),
            "current": random.uniform(150, 600)
        },
        "error_rate": {
            "avg": random.uniform(0.5, 2),
            "max": random.uniform(2, 5),
            "min": 0,
            "current": random.uniform(0.1, 3)
        }
    }

    return {
        "success": True,
        "data": {
            "overview": {
                "health_status": health_status,
                "health_score": health_score,
                "active_alerts": len(active_alerts),
                "critical_alerts": len([a for a in active_alerts if a["level"] == "critical"]),
                "monitoring_enabled": True,
                "last_update": current_time.isoformat()
            },
            "system_health": {
                "status": health_status,
                "health_score": health_score,
                "message": status_text,
                "components": {
                    "cpu": random.choice(["healthy", "warning"]),
                    "memory": random.choice(["healthy", "warning"]),
                    "disk": random.choice(["healthy", "warning"]),
                    "application": random.choice(["healthy", "warning"]),
                    "performance": random.choice(["healthy", "warning"])
                },
                "uptime": random.randint(3600, 86400),
                "last_check": current_time.isoformat()
            },
            "active_alerts": active_alerts,
            "metrics_summary": metrics_summary,
            "alert_rules": {
                "total": 12,
                "enabled": 10,
                "by_type": {
                    "system": 6,
                    "application": 3,
                    "performance": 2,
                    "security": 1
                }
            },
            "notification_channels": {
                "email": True,
                "webhook": True,
                "slack": True,
                "dingtalk": True,
                "sms": False
            },
            "health_checks": {
                "database": {
                    "name": "数据库连接",
                    "status": random.choice(["healthy", "warning"]),
                    "last_check": current_time.isoformat()
                },
                "redis": {
                    "name": "Redis缓存",
                    "status": random.choice(["healthy", "warning"]),
                    "last_check": current_time.isoformat()
                },
                "external_api": {
                    "name": "外部API",
                    "status": random.choice(["healthy", "warning"]),
                    "last_check": current_time.isoformat()
                }
            }
        },
        "message": "监控仪表板数据获取成功"
    }

@app.get("/api/v1/monitoring/alerts")
async def get_monitoring_alerts():
    """获取监控告警列表"""
    alerts = []
    alert_levels = ["info", "warning", "error", "critical"]
    alert_types = ["system", "application", "security", "performance", "business"]

    # 生成模拟告警数据
    for i in range(random.randint(5, 15)):
        level = random.choice(alert_levels)
        alert_type = random.choice(alert_types)
        resolved = random.choice([True, False, False])  # 大部分未解决

        alert = {
            "id": f"alert_{int(time.time())}_{i}",
            "level": level,
            "type": alert_type,
            "title": {
                "system": random.choice(["CPU使用率过高", "内存使用率过高", "磁盘空间不足"]),
                "application": random.choice(["API响应时间过长", "错误率过高", "数据库连接数过高"]),
                "security": random.choice(["异常登录尝试", "权限提升检测", "恶意请求检测"]),
                "performance": random.choice(["响应时间超标", "吞吐量下降", "缓存命中率低"]),
                "business": random.choice(["交易量异常", "用户活跃度下降", "收入指标异常"])
            }[alert_type],
            "message": f"{alert_type}类型告警触发",
            "source": f"rule_{alert_type}_{random.randint(1, 5)}",
            "timestamp": (datetime.now() - timedelta(minutes=random.randint(1, 1440))).isoformat(),
            "resolved": resolved,
            "resolved_at": (datetime.now() - timedelta(minutes=random.randint(1, 60))).isoformat() if resolved else None,
            "metadata": {
                "threshold": random.uniform(50, 100),
                "current_value": random.uniform(60, 120),
                "duration": random.randint(60, 3600),
                "affected_components": random.choice([["web"], ["api"], ["database"], ["web", "api"]])
            }
        }
        alerts.append(alert)

    # 按级别和时间排序
    level_priority = {"critical": 0, "error": 1, "warning": 2, "info": 3}
    alerts.sort(key=lambda x: (level_priority.get(x["level"], 4), x["timestamp"]), reverse=True)

    active_alerts = [a for a in alerts if not a["resolved"]]
    resolved_alerts = [a for a in alerts if a["resolved"]]

    return {
        "success": True,
        "data": {
            "alerts": alerts,
            "summary": {
                "total": len(alerts),
                "active": len(active_alerts),
                "resolved": len(resolved_alerts),
                "by_level": {
                    level: len([a for a in alerts if a["level"] == level])
                    for level in alert_levels
                },
                "by_type": {
                    alert_type: len([a for a in alerts if a["type"] == alert_type])
                    for alert_type in alert_types
                }
            },
            "active_alerts": active_alerts,
            "recent_resolved": resolved_alerts[:5]
        },
        "message": "告警列表获取成功"
    }

@app.get("/api/v1/monitoring/metrics")
async def get_monitoring_metrics():
    """获取监控指标"""
    current_time = datetime.now()

    # 生成最近1小时的指标数据
    metrics_data = []
    for i in range(60):  # 每分钟一个数据点
        timestamp = current_time - timedelta(minutes=59-i)

        # 模拟指标波动
        base_cpu = 50 + 20 * math.sin(i * 0.1) + random.uniform(-5, 5)
        base_memory = 60 + 15 * math.cos(i * 0.08) + random.uniform(-3, 3)
        base_response_time = 300 + 100 * math.sin(i * 0.05) + random.uniform(-20, 20)

        metrics_data.append({
            "timestamp": timestamp.isoformat(),
            "system": {
                "cpu_percent": max(0, min(100, base_cpu)),
                "memory_percent": max(0, min(100, base_memory)),
                "disk_percent": random.uniform(45, 55),
                "network_io": {
                    "bytes_sent": random.randint(1000000, 10000000),
                    "bytes_recv": random.randint(1000000, 10000000)
                },
                "process_count": random.randint(150, 200),
                "load_average": [
                    random.uniform(0.5, 2.0),
                    random.uniform(0.8, 2.5),
                    random.uniform(1.0, 3.0)
                ]
            },
            "application": {
                "active_users": random.randint(80, 150),
                "active_sessions": random.randint(50, 120),
                "api_requests_per_minute": random.randint(200, 800),
                "response_time_avg": max(50, base_response_time),
                "error_rate": random.uniform(0, 2),
                "database_connections": random.randint(15, 45),
                "cache_hit_rate": random.uniform(85, 98),
                "queue_size": random.randint(0, 100)
            }
        })

    # 计算统计信息
    cpu_values = [m["system"]["cpu_percent"] for m in metrics_data]
    memory_values = [m["system"]["memory_percent"] for m in metrics_data]
    response_times = [m["application"]["response_time_avg"] for m in metrics_data]

    statistics = {
        "cpu": {
            "avg": sum(cpu_values) / len(cpu_values),
            "max": max(cpu_values),
            "min": min(cpu_values),
            "p95": sorted(cpu_values)[int(len(cpu_values) * 0.95)]
        },
        "memory": {
            "avg": sum(memory_values) / len(memory_values),
            "max": max(memory_values),
            "min": min(memory_values),
            "p95": sorted(memory_values)[int(len(memory_values) * 0.95)]
        },
        "response_time": {
            "avg": sum(response_times) / len(response_times),
            "max": max(response_times),
            "min": min(response_times),
            "p95": sorted(response_times)[int(len(response_times) * 0.95)]
        }
    }

    return {
        "success": True,
        "data": {
            "metrics": metrics_data,
            "statistics": statistics,
            "period": {
                "start": metrics_data[0]["timestamp"],
                "end": metrics_data[-1]["timestamp"],
                "duration_minutes": 60,
                "data_points": len(metrics_data)
            }
        },
        "message": "监控指标获取成功"
    }

@app.get("/api/v1/monitoring/health")
async def get_system_health():
    """获取系统健康状态"""
    # 模拟健康检查
    health_checks = {
        "database": {
            "name": "数据库连接",
            "status": random.choice(["healthy", "warning", "error"]),
            "response_time": random.randint(10, 100),
            "last_check": datetime.now().isoformat(),
            "details": "PostgreSQL连接正常"
        },
        "redis": {
            "name": "Redis缓存",
            "status": random.choice(["healthy", "warning"]),
            "response_time": random.randint(1, 10),
            "last_check": datetime.now().isoformat(),
            "details": "Redis连接正常，内存使用率65%"
        },
        "external_api": {
            "name": "外部API",
            "status": random.choice(["healthy", "warning", "error"]),
            "response_time": random.randint(100, 1000),
            "last_check": datetime.now().isoformat(),
            "details": "第三方数据源API"
        },
        "message_queue": {
            "name": "消息队列",
            "status": random.choice(["healthy", "warning"]),
            "response_time": random.randint(5, 50),
            "last_check": datetime.now().isoformat(),
            "details": "RabbitMQ运行正常"
        },
        "file_system": {
            "name": "文件系统",
            "status": random.choice(["healthy", "warning"]),
            "response_time": random.randint(1, 5),
            "last_check": datetime.now().isoformat(),
            "details": "磁盘空间充足"
        }
    }

    # 计算整体健康分数
    healthy_count = sum(1 for check in health_checks.values() if check["status"] == "healthy")
    warning_count = sum(1 for check in health_checks.values() if check["status"] == "warning")
    error_count = sum(1 for check in health_checks.values() if check["status"] == "error")

    total_checks = len(health_checks)
    health_score = (healthy_count * 100 + warning_count * 50) / total_checks

    if health_score >= 90:
        overall_status = "healthy"
        status_message = "所有系统组件运行正常"
    elif health_score >= 70:
        overall_status = "warning"
        status_message = "部分系统组件存在问题"
    else:
        overall_status = "critical"
        status_message = "系统存在严重问题"

    return {
        "success": True,
        "data": {
            "overall_status": overall_status,
            "health_score": round(health_score, 1),
            "status_message": status_message,
            "health_checks": health_checks,
            "summary": {
                "total_checks": total_checks,
                "healthy": healthy_count,
                "warning": warning_count,
                "error": error_count
            },
            "last_update": datetime.now().isoformat()
        },
        "message": "系统健康状态获取成功"
    }

@app.get("/api/v1/monitoring/alert-rules")
async def get_alert_rules():
    """获取告警规则"""
    alert_rules = [
        {
            "id": "cpu_high",
            "name": "CPU使用率过高",
            "description": "CPU使用率持续超过80%",
            "condition": "cpu_percent > 80",
            "level": "warning",
            "type": "system",
            "threshold": 80,
            "duration": 300,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=30)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 10)
        },
        {
            "id": "memory_critical",
            "name": "内存使用率严重过高",
            "description": "内存使用率超过95%，可能导致OOM",
            "condition": "memory_percent > 95",
            "level": "critical",
            "type": "system",
            "threshold": 95,
            "duration": 60,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=25)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 5)
        },
        {
            "id": "response_time_high",
            "name": "API响应时间过长",
            "description": "API平均响应时间超过2秒",
            "condition": "response_time_avg > 2000",
            "level": "warning",
            "type": "performance",
            "threshold": 2000,
            "duration": 300,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=20)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 15)
        },
        {
            "id": "error_rate_high",
            "name": "错误率过高",
            "description": "应用错误率超过5%",
            "condition": "error_rate > 5",
            "level": "error",
            "type": "application",
            "threshold": 5,
            "duration": 180,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=15)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 8)
        },
        {
            "id": "disk_space_low",
            "name": "磁盘空间不足",
            "description": "磁盘使用率超过90%",
            "condition": "disk_percent > 90",
            "level": "warning",
            "type": "system",
            "threshold": 90,
            "duration": 600,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=10)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 3)
        },
        {
            "id": "security_breach",
            "name": "安全威胁检测",
            "description": "检测到异常登录或安全威胁",
            "condition": "failed_login_attempts > 10",
            "level": "critical",
            "type": "security",
            "threshold": 10,
            "duration": 60,
            "enabled": True,
            "created_at": (datetime.now() - timedelta(days=5)).isoformat(),
            "updated_at": datetime.now().isoformat(),
            "trigger_count": random.randint(0, 2)
        }
    ]

    # 按类型分组统计
    by_type = {}
    by_level = {}

    for rule in alert_rules:
        rule_type = rule["type"]
        rule_level = rule["level"]

        if rule_type not in by_type:
            by_type[rule_type] = 0
        by_type[rule_type] += 1

        if rule_level not in by_level:
            by_level[rule_level] = 0
        by_level[rule_level] += 1

    enabled_count = sum(1 for rule in alert_rules if rule["enabled"])

    return {
        "success": True,
        "data": {
            "rules": alert_rules,
            "summary": {
                "total": len(alert_rules),
                "enabled": enabled_count,
                "disabled": len(alert_rules) - enabled_count,
                "by_type": by_type,
                "by_level": by_level
            }
        },
        "message": "告警规则获取成功"
    }

@app.get("/api/v1/monitoring/notification-channels")
async def get_notification_channels():
    """获取通知渠道配置"""
    channels = {
        "email": {
            "name": "邮件通知",
            "enabled": True,
            "config": {
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "recipients": ["<EMAIL>", "<EMAIL>"],
                "template": "default"
            },
            "last_sent": (datetime.now() - timedelta(minutes=random.randint(10, 120))).isoformat(),
            "success_rate": random.uniform(95, 99.9),
            "total_sent": random.randint(100, 1000)
        },
        "webhook": {
            "name": "Webhook通知",
            "enabled": True,
            "config": {
                "url": "https://hooks.example.com/alerts",
                "method": "POST",
                "headers": {"Content-Type": "application/json"},
                "timeout": 10
            },
            "last_sent": (datetime.now() - timedelta(minutes=random.randint(5, 60))).isoformat(),
            "success_rate": random.uniform(98, 99.9),
            "total_sent": random.randint(200, 2000)
        },
        "slack": {
            "name": "Slack通知",
            "enabled": True,
            "config": {
                "webhook_url": "https://hooks.slack.com/services/...",
                "channel": "#alerts",
                "username": "监控系统"
            },
            "last_sent": (datetime.now() - timedelta(minutes=random.randint(1, 30))).isoformat(),
            "success_rate": random.uniform(97, 99.5),
            "total_sent": random.randint(50, 500)
        },
        "dingtalk": {
            "name": "钉钉通知",
            "enabled": True,
            "config": {
                "webhook_url": "https://oapi.dingtalk.com/robot/send?access_token=...",
                "secret": "SEC...",
                "at_all": False
            },
            "last_sent": (datetime.now() - timedelta(minutes=random.randint(15, 90))).isoformat(),
            "success_rate": random.uniform(96, 99),
            "total_sent": random.randint(30, 300)
        },
        "sms": {
            "name": "短信通知",
            "enabled": False,
            "config": {
                "provider": "aliyun",
                "template_id": "SMS_*********",
                "phone_numbers": ["+86138****8888"]
            },
            "last_sent": None,
            "success_rate": 0,
            "total_sent": 0
        }
    }

    enabled_channels = [name for name, config in channels.items() if config["enabled"]]
    total_sent = sum(config["total_sent"] for config in channels.values())
    avg_success_rate = sum(config["success_rate"] for config in channels.values() if config["enabled"]) / len(enabled_channels)

    return {
        "success": True,
        "data": {
            "channels": channels,
            "summary": {
                "total_channels": len(channels),
                "enabled_channels": len(enabled_channels),
                "total_notifications_sent": total_sent,
                "average_success_rate": round(avg_success_rate, 2)
            }
        },
        "message": "通知渠道配置获取成功"
    }

@app.post("/api/v1/monitoring/test-alert")
async def send_test_alert():
    """发送测试告警"""
    test_alert = {
        "id": f"test_alert_{int(time.time())}",
        "level": "info",
        "type": "system",
        "title": "测试告警",
        "message": "这是一个测试告警，用于验证通知渠道是否正常工作",
        "timestamp": datetime.now().isoformat(),
        "source": "manual_test",
        "metadata": {
            "test": True,
            "user": "admin",
            "purpose": "通知渠道测试"
        }
    }

    # 模拟发送到各个渠道
    send_results = {
        "email": {"success": True, "response_time": random.randint(100, 500)},
        "webhook": {"success": True, "response_time": random.randint(50, 200)},
        "slack": {"success": True, "response_time": random.randint(200, 800)},
        "dingtalk": {"success": True, "response_time": random.randint(150, 600)},
        "sms": {"success": False, "error": "渠道未启用"}
    }

    successful_channels = [channel for channel, result in send_results.items() if result["success"]]

    return {
        "success": True,
        "message": f"测试告警已发送到 {len(successful_channels)} 个渠道",
        "data": {
            "alert": test_alert,
            "send_results": send_results,
            "summary": {
                "total_channels": len(send_results),
                "successful_channels": len(successful_channels),
                "failed_channels": len(send_results) - len(successful_channels)
            }
        }
    }

# 性能和安全测试API

@app.post("/api/v1/testing/performance/load-test")
async def run_load_test():
    """运行负载测试"""
    # 模拟负载测试
    test_config = {
        "concurrent_users": random.choice([10, 50, 100, 200]),
        "test_duration": random.choice([60, 120, 300]),
        "target_url": "http://localhost:8000/api/v1/health"
    }

    # 模拟测试执行
    await asyncio.sleep(2)  # 模拟测试时间

    # 生成模拟结果
    total_requests = test_config["concurrent_users"] * test_config["test_duration"] // 2
    success_rate = random.uniform(95, 99.9)
    successful_requests = int(total_requests * success_rate / 100)
    failed_requests = total_requests - successful_requests

    response_times = [random.uniform(50, 500) for _ in range(100)]

    result = {
        "test_name": f"Load Test - {test_config['concurrent_users']} users",
        "test_type": "load_test",
        "start_time": (datetime.now() - timedelta(seconds=test_config["test_duration"])).isoformat(),
        "end_time": datetime.now().isoformat(),
        "duration": test_config["test_duration"],
        "config": test_config,
        "results": {
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": failed_requests,
            "requests_per_second": round(total_requests / test_config["test_duration"], 2),
            "avg_response_time": round(sum(response_times) / len(response_times), 2),
            "min_response_time": round(min(response_times), 2),
            "max_response_time": round(max(response_times), 2),
            "p95_response_time": round(sorted(response_times)[int(len(response_times) * 0.95)], 2),
            "p99_response_time": round(sorted(response_times)[int(len(response_times) * 0.99)], 2),
            "error_rate": round((failed_requests / total_requests) * 100, 2),
            "throughput_mb": round(random.uniform(10, 100), 2)
        },
        "system_metrics": {
            "cpu_usage": {
                "avg": random.uniform(30, 80),
                "max": random.uniform(80, 95),
                "min": random.uniform(10, 30)
            },
            "memory_usage": {
                "avg": random.uniform(40, 70),
                "max": random.uniform(70, 90),
                "min": random.uniform(30, 50)
            }
        },
        "errors": [
            {
                "error": "Connection timeout",
                "count": random.randint(0, 5),
                "percentage": random.uniform(0, 1)
            },
            {
                "error": "HTTP 500",
                "count": random.randint(0, 3),
                "percentage": random.uniform(0, 0.5)
            }
        ]
    }

    return {
        "success": True,
        "message": "负载测试完成",
        "data": result
    }

@app.post("/api/v1/testing/performance/stress-test")
async def run_stress_test():
    """运行压力测试"""
    # 模拟压力测试
    stress_levels = [50, 100, 200, 500, 1000]
    results = []

    for concurrent_users in stress_levels:
        # 模拟每个压力级别的测试
        total_requests = concurrent_users * 60  # 1分钟测试
        success_rate = max(50, 100 - (concurrent_users / 10))  # 用户越多成功率越低

        result = {
            "concurrent_users": concurrent_users,
            "total_requests": total_requests,
            "successful_requests": int(total_requests * success_rate / 100),
            "failed_requests": int(total_requests * (100 - success_rate) / 100),
            "requests_per_second": round(total_requests / 60, 2),
            "avg_response_time": random.uniform(100, 2000 + concurrent_users),
            "error_rate": round(100 - success_rate, 2),
            "cpu_usage": min(100, 20 + concurrent_users / 10),
            "memory_usage": min(100, 30 + concurrent_users / 20)
        }
        results.append(result)

        # 如果错误率过高，停止测试
        if result["error_rate"] > 50:
            break

    return {
        "success": True,
        "message": "压力测试完成",
        "data": {
            "test_name": "Stress Test",
            "test_type": "stress_test",
            "start_time": (datetime.now() - timedelta(minutes=len(results))).isoformat(),
            "end_time": datetime.now().isoformat(),
            "stress_levels": results,
            "max_concurrent_users": max(r["concurrent_users"] for r in results),
            "breaking_point": next((r["concurrent_users"] for r in results if r["error_rate"] > 50), None),
            "summary": {
                "total_tests": len(results),
                "peak_rps": max(r["requests_per_second"] for r in results),
                "avg_response_time": sum(r["avg_response_time"] for r in results) / len(results)
            }
        }
    }

@app.post("/api/v1/testing/performance/benchmark")
async def run_benchmark_test():
    """运行性能基准测试"""
    # 模拟基准测试
    endpoints = [
        "/api/v1/health",
        "/api/v1/market/stocks",
        "/api/v1/trading/orders",
        "/api/v1/monitoring/metrics",
        "/api/v1/auth/login"
    ]

    benchmark_results = {}

    for endpoint in endpoints:
        # 模拟每个端点的基准测试
        benchmark_results[endpoint] = {
            "avg_response_time": random.uniform(50, 300),
            "p95_response_time": random.uniform(200, 500),
            "p99_response_time": random.uniform(400, 800),
            "requests_per_second": random.uniform(100, 1000),
            "error_rate": random.uniform(0, 2),
            "throughput_mb": random.uniform(1, 50),
            "baseline_comparison": {
                "response_time_change": random.uniform(-20, 20),  # 与基线的变化百分比
                "rps_change": random.uniform(-15, 15),
                "status": random.choice(["improved", "degraded", "stable"])
            }
        }

    # 计算总体评分
    avg_response_time = sum(r["avg_response_time"] for r in benchmark_results.values()) / len(benchmark_results)
    avg_rps = sum(r["requests_per_second"] for r in benchmark_results.values()) / len(benchmark_results)
    avg_error_rate = sum(r["error_rate"] for r in benchmark_results.values()) / len(benchmark_results)

    # 性能评级
    if avg_response_time < 200 and avg_error_rate < 1:
        performance_grade = "A"
    elif avg_response_time < 500 and avg_error_rate < 2:
        performance_grade = "B"
    elif avg_response_time < 1000 and avg_error_rate < 5:
        performance_grade = "C"
    else:
        performance_grade = "D"

    return {
        "success": True,
        "message": "基准测试完成",
        "data": {
            "test_name": "Performance Benchmark",
            "test_type": "benchmark_test",
            "start_time": (datetime.now() - timedelta(minutes=10)).isoformat(),
            "end_time": datetime.now().isoformat(),
            "endpoints": benchmark_results,
            "summary": {
                "performance_grade": performance_grade,
                "avg_response_time": round(avg_response_time, 2),
                "avg_requests_per_second": round(avg_rps, 2),
                "avg_error_rate": round(avg_error_rate, 2),
                "total_endpoints_tested": len(endpoints)
            },
            "recommendations": [
                "优化数据库查询性能" if avg_response_time > 500 else "响应时间表现良好",
                "增加缓存机制" if avg_rps < 200 else "吞吐量表现良好",
                "检查错误处理逻辑" if avg_error_rate > 2 else "错误率控制良好"
            ]
        }
    }

@app.post("/api/v1/testing/security/vulnerability-scan")
async def run_vulnerability_scan():
    """运行漏洞扫描"""
    target_url = "http://localhost:8000"

    # 模拟漏洞扫描
    await asyncio.sleep(3)  # 模拟扫描时间

    # 生成模拟漏洞
    vulnerabilities = []

    # 模拟发现的漏洞
    sample_vulnerabilities = [
        {
            "id": f"vuln_{int(time.time())}_1",
            "severity": "high",
            "category": "injection",
            "title": "SQL注入漏洞",
            "description": "在登录表单中发现SQL注入漏洞",
            "affected_url": f"{target_url}/login",
            "evidence": "Payload: ' OR '1'='1",
            "recommendation": "使用参数化查询防止SQL注入",
            "cve_id": "CWE-89",
            "cvss_score": 8.5,
            "discovered_at": datetime.now().isoformat()
        },
        {
            "id": f"vuln_{int(time.time())}_2",
            "severity": "medium",
            "category": "authentication",
            "title": "缺少CSRF保护",
            "description": "表单缺少CSRF令牌保护",
            "affected_url": f"{target_url}/api/v1/trading/order",
            "evidence": "表单未包含CSRF令牌",
            "recommendation": "实施CSRF保护机制",
            "cve_id": "CWE-352",
            "cvss_score": 6.1,
            "discovered_at": datetime.now().isoformat()
        },
        {
            "id": f"vuln_{int(time.time())}_3",
            "severity": "low",
            "category": "information_disclosure",
            "title": "服务器信息泄露",
            "description": "HTTP响应头泄露服务器版本信息",
            "affected_url": target_url,
            "evidence": "Server: nginx/1.18.0",
            "recommendation": "隐藏服务器版本信息",
            "cve_id": "CWE-200",
            "cvss_score": 3.1,
            "discovered_at": datetime.now().isoformat()
        }
    ]

    # 随机选择一些漏洞
    vulnerabilities = random.sample(sample_vulnerabilities, random.randint(1, 3))

    # 计算安全分数
    severity_weights = {"critical": 25, "high": 15, "medium": 8, "low": 3, "info": 1}
    total_deduction = sum(severity_weights.get(v["severity"], 1) for v in vulnerabilities)
    security_score = max(0, 100 - total_deduction)

    # 生成建议
    recommendations = [
        "定期更新系统和依赖库",
        "实施Web应用防火墙(WAF)",
        "进行定期安全审计",
        "建立安全事件响应流程"
    ]

    # 按严重程度统计
    severity_counts = {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0}
    for vuln in vulnerabilities:
        severity_counts[vuln["severity"]] += 1

    result = {
        "test_name": f"Vulnerability Scan - {target_url}",
        "test_type": "vulnerability_scan",
        "start_time": (datetime.now() - timedelta(minutes=5)).isoformat(),
        "end_time": datetime.now().isoformat(),
        "duration": 300,
        "target_url": target_url,
        "total_checks": 25,
        "vulnerabilities_found": len(vulnerabilities),
        "vulnerabilities": vulnerabilities,
        "security_score": round(security_score, 1),
        "severity_distribution": severity_counts,
        "recommendations": recommendations,
        "scan_coverage": {
            "sql_injection": True,
            "xss": True,
            "csrf": True,
            "authentication": True,
            "information_disclosure": True,
            "communication_security": True
        }
    }

    return {
        "success": True,
        "message": f"漏洞扫描完成，发现 {len(vulnerabilities)} 个漏洞",
        "data": result
    }

@app.post("/api/v1/testing/security/penetration-test")
async def run_penetration_test():
    """运行渗透测试"""
    target_url = "http://localhost:8000"

    # 模拟渗透测试
    await asyncio.sleep(5)  # 模拟测试时间

    # 模拟渗透测试阶段
    phases = [
        {
            "phase": "reconnaissance",
            "name": "信息收集",
            "duration": 120,
            "findings": [
                "发现开放端口: 80, 443, 8000",
                "识别Web服务器: nginx/1.18.0",
                "发现管理后台: /admin"
            ]
        },
        {
            "phase": "vulnerability_assessment",
            "name": "漏洞评估",
            "duration": 180,
            "findings": [
                "发现SQL注入点: /login",
                "发现XSS漏洞: /search",
                "发现目录遍历: /files"
            ]
        },
        {
            "phase": "exploitation",
            "name": "漏洞利用",
            "duration": 240,
            "findings": [
                "成功利用SQL注入获取用户数据",
                "通过XSS获取管理员Cookie",
                "利用目录遍历读取配置文件"
            ]
        },
        {
            "phase": "post_exploitation",
            "name": "后渗透",
            "duration": 180,
            "findings": [
                "尝试权限提升失败",
                "横向移动受限",
                "数据外泄风险评估完成"
            ]
        }
    ]

    # 生成高危漏洞
    critical_vulnerabilities = [
        {
            "id": f"pentest_{int(time.time())}_1",
            "severity": "critical",
            "category": "injection",
            "title": "SQL注入导致数据泄露",
            "description": "通过SQL注入成功获取用户敏感数据",
            "affected_url": f"{target_url}/login",
            "evidence": "成功提取用户表中的密码哈希",
            "recommendation": "立即修复SQL注入漏洞并重置所有用户密码",
            "cvss_score": 9.8,
            "exploited": True
        }
    ]

    security_score = 45.2  # 渗透测试发现严重问题

    result = {
        "test_name": f"Penetration Test - {target_url}",
        "test_type": "penetration_test",
        "start_time": (datetime.now() - timedelta(minutes=12)).isoformat(),
        "end_time": datetime.now().isoformat(),
        "duration": 720,
        "target_url": target_url,
        "phases": phases,
        "critical_vulnerabilities": critical_vulnerabilities,
        "security_score": security_score,
        "risk_level": "high",
        "executive_summary": {
            "overall_risk": "高风险",
            "key_findings": [
                "发现可被利用的SQL注入漏洞",
                "存在数据泄露风险",
                "需要立即采取补救措施"
            ],
            "business_impact": "可能导致用户数据泄露和业务中断",
            "immediate_actions": [
                "立即修复SQL注入漏洞",
                "重置所有用户密码",
                "加强输入验证",
                "部署WAF防护"
            ]
        },
        "compliance_status": {
            "gdpr": "不合规 - 存在数据泄露风险",
            "pci_dss": "不合规 - 支付数据安全风险",
            "iso27001": "部分合规 - 需要改进安全控制"
        }
    }

    return {
        "success": True,
        "message": "渗透测试完成，发现严重安全问题",
        "data": result
    }

@app.post("/api/v1/testing/security/baseline-check")
async def run_security_baseline_check():
    """运行安全基线检查"""
    # 模拟安全基线检查
    await asyncio.sleep(2)

    # 检查项目
    security_checks = [
        {
            "category": "authentication",
            "name": "密码策略",
            "status": "pass",
            "score": 85,
            "details": "密码长度要求符合标准，但缺少复杂度要求"
        },
        {
            "category": "encryption",
            "name": "数据加密",
            "status": "fail",
            "score": 60,
            "details": "数据库连接未加密，敏感数据存储未加密"
        },
        {
            "category": "access_control",
            "name": "访问控制",
            "status": "pass",
            "score": 90,
            "details": "RBAC实施良好，权限分离明确"
        },
        {
            "category": "logging",
            "name": "日志审计",
            "status": "warning",
            "score": 75,
            "details": "日志记录完整，但缺少实时监控"
        },
        {
            "category": "network_security",
            "name": "网络安全",
            "status": "pass",
            "score": 80,
            "details": "防火墙配置合理，但缺少IDS/IPS"
        },
        {
            "category": "vulnerability_management",
            "name": "漏洞管理",
            "status": "warning",
            "score": 70,
            "details": "定期扫描，但修复周期较长"
        }
    ]

    # 计算总体分数
    total_score = sum(check["score"] for check in security_checks) / len(security_checks)

    # 合规性检查
    compliance_results = {
        "iso27001": {
            "status": "partial",
            "score": 78,
            "missing_controls": [
                "A.10.1.1 - 加密密钥管理",
                "A.12.6.1 - 漏洞管理",
                "A.16.1.2 - 安全事件报告"
            ]
        },
        "nist_csf": {
            "status": "partial",
            "score": 82,
            "missing_controls": [
                "PR.DS-1 - 数据静态保护",
                "DE.CM-1 - 网络监控",
                "RS.RP-1 - 响应计划"
            ]
        },
        "gdpr": {
            "status": "non_compliant",
            "score": 65,
            "missing_controls": [
                "数据加密要求",
                "数据泄露通知机制",
                "用户同意管理"
            ]
        }
    }

    # 改进建议
    recommendations = [
        {
            "priority": "high",
            "category": "encryption",
            "title": "实施数据加密",
            "description": "对敏感数据进行加密存储和传输",
            "estimated_effort": "2-3周"
        },
        {
            "priority": "medium",
            "category": "monitoring",
            "title": "部署安全监控",
            "description": "实施实时安全监控和告警系统",
            "estimated_effort": "1-2周"
        },
        {
            "priority": "medium",
            "category": "vulnerability_management",
            "title": "优化漏洞管理流程",
            "description": "建立自动化漏洞扫描和快速修复流程",
            "estimated_effort": "1周"
        }
    ]

    result = {
        "test_name": "Security Baseline Check",
        "test_type": "baseline_check",
        "start_time": (datetime.now() - timedelta(minutes=3)).isoformat(),
        "end_time": datetime.now().isoformat(),
        "duration": 180,
        "overall_score": round(total_score, 1),
        "security_checks": security_checks,
        "compliance_results": compliance_results,
        "recommendations": recommendations,
        "summary": {
            "passed_checks": len([c for c in security_checks if c["status"] == "pass"]),
            "failed_checks": len([c for c in security_checks if c["status"] == "fail"]),
            "warning_checks": len([c for c in security_checks if c["status"] == "warning"]),
            "total_checks": len(security_checks)
        }
    }

    return {
        "success": True,
        "message": f"安全基线检查完成，总体分数: {total_score:.1f}",
        "data": result
    }

@app.get("/api/v1/testing/results")
async def get_test_results():
    """获取测试结果汇总"""
    # 模拟测试结果汇总
    test_summary = {
        "performance_tests": {
            "total_tests": random.randint(5, 15),
            "latest_test": {
                "test_type": "load_test",
                "date": datetime.now().isoformat(),
                "result": "passed",
                "score": random.uniform(80, 95)
            },
            "average_score": random.uniform(75, 90),
            "trends": {
                "response_time": "improving",
                "throughput": "stable",
                "error_rate": "improving"
            }
        },
        "security_tests": {
            "total_tests": random.randint(3, 8),
            "latest_test": {
                "test_type": "vulnerability_scan",
                "date": datetime.now().isoformat(),
                "vulnerabilities_found": random.randint(0, 5),
                "security_score": random.uniform(70, 95)
            },
            "average_security_score": random.uniform(75, 88),
            "trends": {
                "vulnerability_count": "decreasing",
                "security_score": "improving",
                "compliance": "stable"
            }
        },
        "overall_health": {
            "status": "good",
            "performance_grade": "B+",
            "security_grade": "B",
            "recommendations": [
                "继续优化API响应时间",
                "加强数据加密措施",
                "完善安全监控体系"
            ]
        }
    }

    return {
        "success": True,
        "data": test_summary,
        "message": "测试结果汇总获取成功"
    }

if __name__ == "__main__":
    print("🚀 启动量化投资平台测试后端服务...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    
    uvicorn.run(
        "simple_test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
