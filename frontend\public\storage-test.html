<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 基于pythonstock的数据存储系统测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }

        .btn.danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        .result-box {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-text { color: #27ae60; font-weight: bold; }
        .error-text { color: #e74c3c; font-weight: bold; }
        .info-text { color: #3498db; font-weight: bold; }

        .architecture-diagram {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #ecf0f1;
        }

        .layer {
            background: #f8f9fa;
            border: 2px solid #3498db;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            min-width: 200px;
        }

        .layer h4 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .layer p {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 基于pythonstock的数据存储系统</h1>
            <p>分层缓存 • gzip压缩 • 智能预加载 • API频率控制</p>
        </div>

        <div class="content">
            <!-- 系统架构展示 -->
            <div class="test-section">
                <h2>🏗️ 系统架构 (学习pythonstock设计)</h2>
                <div class="architecture-diagram">
                    <div class="layer">
                        <h4>🔥 热数据层</h4>
                        <p>内存 + Redis<br>30秒TTL<br>最快访问</p>
                    </div>
                    <div class="layer">
                        <h4>🌡️ 温数据层</h4>
                        <p>gzip压缩存储<br>3天保留策略<br>pickle序列化</p>
                    </div>
                    <div class="layer">
                        <h4>❄️ 冷数据层</h4>
                        <p>Parquet格式<br>按月分片<br>长期存储</p>
                    </div>
                </div>
            </div>

            <!-- 核心特性 -->
            <div class="test-section">
                <h2>✨ 核心特性</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🗜️ gzip压缩存储</h3>
                        <p>学习pythonstock的压缩策略，使用pickle + gzip实现70%的存储空间节省</p>
                    </div>
                    <div class="feature-card">
                        <h3>📅 按天分片缓存</h3>
                        <p>采用pythonstock的3天缓存策略，自动清理过期数据，防止磁盘空间耗尽</p>
                    </div>
                    <div class="feature-card">
                        <h3>🚦 API频率控制</h3>
                        <p>智能限制API调用频率，防止接口被封，支持多数据源配置</p>
                    </div>
                    <div class="feature-card">
                        <h3>🔄 智能预加载</h3>
                        <p>预热热门股票数据，提升响应速度，支持批量处理和并发控制</p>
                    </div>
                </div>
            </div>

            <!-- 存储统计 -->
            <div class="test-section">
                <h2>📊 存储系统统计</h2>
                <div class="stats-grid" id="statsGrid">
                    <div class="stat-card">
                        <div class="stat-value" id="cacheHitRate">--</div>
                        <div class="stat-label">缓存命中率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="apiCalls">--</div>
                        <div class="stat-label">API调用次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="cacheHits">--</div>
                        <div class="stat-label">缓存命中</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="errors">--</div>
                        <div class="stat-label">错误次数</div>
                    </div>
                </div>
                <div class="button-group">
                    <button class="btn" onclick="loadStorageStats()">🔄 刷新统计</button>
                    <button class="btn success" onclick="warmUpCache()">🔥 预热缓存</button>
                    <button class="btn warning" onclick="cleanupCache()">🧹 清理缓存</button>
                </div>
            </div>

            <!-- 数据测试 -->
            <div class="test-section">
                <h2>🧪 数据存储测试</h2>
                <div class="button-group">
                    <button class="btn" onclick="testRealtimeQuote()">📈 测试实时行情</button>
                    <button class="btn" onclick="testKlineData()">📊 测试K线数据</button>
                    <button class="btn" onclick="testMarketOverview()">🌐 测试市场概览</button>
                    <button class="btn" onclick="testBatchQuotes()">📦 测试批量行情</button>
                </div>
                <div class="result-box" id="testResults">
                    <div class="info-text">点击上方按钮开始测试...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 显示加载状态
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.innerHTML = '<span class="loading"></span>加载中...';
        }

        // 显示结果
        function showResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            const className = isError ? 'error-text' : 'success-text';
            element.innerHTML = `<div class="${className}">${content}</div>`;
        }

        // 格式化JSON显示
        function formatJSON(obj) {
            return JSON.stringify(obj, null, 2);
        }

        // 加载存储统计
        async function loadStorageStats() {
            try {
                showLoading('statsGrid');
                
                const response = await fetch(`${API_BASE}/storage/stats`);
                const result = await response.json();
                
                if (result.data && result.data.service_stats) {
                    const stats = result.data.service_stats;
                    document.getElementById('cacheHitRate').textContent = result.data.cache_hit_rate || '0%';
                    document.getElementById('apiCalls').textContent = stats.api_calls || 0;
                    document.getElementById('cacheHits').textContent = stats.cache_hits || 0;
                    document.getElementById('errors').textContent = stats.errors || 0;
                } else {
                    document.getElementById('cacheHitRate').textContent = '0%';
                    document.getElementById('apiCalls').textContent = '0';
                    document.getElementById('cacheHits').textContent = '0';
                    document.getElementById('errors').textContent = '0';
                }
                
                console.log('存储统计:', result);
            } catch (error) {
                console.error('加载统计失败:', error);
                showResult('testResults', `加载统计失败: ${error.message}`, true);
            }
        }

        // 预热缓存
        async function warmUpCache() {
            try {
                showResult('testResults', '正在预热缓存...');
                
                const response = await fetch(`${API_BASE}/storage/warm-up`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                showResult('testResults', `缓存预热完成: ${result.message}`);
                
                // 刷新统计
                setTimeout(loadStorageStats, 1000);
            } catch (error) {
                showResult('testResults', `缓存预热失败: ${error.message}`, true);
            }
        }

        // 清理缓存
        async function cleanupCache() {
            try {
                showResult('testResults', '正在清理缓存...');
                
                const response = await fetch(`${API_BASE}/storage/cleanup`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                showResult('testResults', `缓存清理完成: ${result.message}`);
                
                // 刷新统计
                setTimeout(loadStorageStats, 1000);
            } catch (error) {
                showResult('testResults', `缓存清理失败: ${error.message}`, true);
            }
        }

        // 测试实时行情
        async function testRealtimeQuote() {
            try {
                showResult('testResults', '正在测试实时行情...');
                
                const symbol = '000001.SZ';
                const response = await fetch(`${API_BASE}/market/enhanced/quote/${symbol}`);
                const result = await response.json();
                
                showResult('testResults', `
                    <div class="success-text">✅ 实时行情测试成功</div>
                    <div>股票代码: ${symbol}</div>
                    <div>缓存信息: ${result.cache_info || '未知'}</div>
                    <pre>${formatJSON(result)}</pre>
                `);
                
                // 刷新统计
                setTimeout(loadStorageStats, 500);
            } catch (error) {
                showResult('testResults', `实时行情测试失败: ${error.message}`, true);
            }
        }

        // 测试K线数据
        async function testKlineData() {
            try {
                showResult('testResults', '正在测试K线数据...');
                
                const symbol = '600519.SH';
                const response = await fetch(`${API_BASE}/market/enhanced/kline/${symbol}?period=daily&limit=10`);
                const result = await response.json();
                
                showResult('testResults', `
                    <div class="success-text">✅ K线数据测试成功</div>
                    <div>股票代码: ${symbol}</div>
                    <div>数据条数: ${result.count || 0}</div>
                    <div>缓存信息: ${result.cache_info || '未知'}</div>
                    <pre>${formatJSON(result)}</pre>
                `);
                
                // 刷新统计
                setTimeout(loadStorageStats, 500);
            } catch (error) {
                showResult('testResults', `K线数据测试失败: ${error.message}`, true);
            }
        }

        // 测试市场概览
        async function testMarketOverview() {
            try {
                showResult('testResults', '正在测试市场概览...');
                
                const response = await fetch(`${API_BASE}/market/enhanced/overview`);
                const result = await response.json();
                
                showResult('testResults', `
                    <div class="success-text">✅ 市场概览测试成功</div>
                    <div>缓存信息: ${result.cache_info || '未知'}</div>
                    <pre>${formatJSON(result)}</pre>
                `);
                
                // 刷新统计
                setTimeout(loadStorageStats, 500);
            } catch (error) {
                showResult('testResults', `市场概览测试失败: ${error.message}`, true);
            }
        }

        // 测试批量行情
        async function testBatchQuotes() {
            try {
                showResult('testResults', '正在测试批量行情...');
                
                const symbols = ['000001.SZ', '600519.SH', '000858.SZ'];
                const promises = symbols.map(symbol => 
                    fetch(`${API_BASE}/market/enhanced/quote/${symbol}`).then(r => r.json())
                );
                
                const results = await Promise.all(promises);
                
                showResult('testResults', `
                    <div class="success-text">✅ 批量行情测试成功</div>
                    <div>测试股票: ${symbols.join(', ')}</div>
                    <div>成功获取: ${results.filter(r => r.data).length}/${symbols.length}</div>
                    <pre>${formatJSON(results)}</pre>
                `);
                
                // 刷新统计
                setTimeout(loadStorageStats, 500);
            } catch (error) {
                showResult('testResults', `批量行情测试失败: ${error.message}`, true);
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStorageStats();
            
            // 每30秒自动刷新统计
            setInterval(loadStorageStats, 30000);
        });
    </script>
</body>
</html>
