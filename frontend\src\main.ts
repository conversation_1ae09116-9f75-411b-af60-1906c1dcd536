/**
 * 简化版应用入口文件
 */
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'

// 导入路由
import router from './router'

// 导入Element Plus (按需加载)
import ElementPlus from 'element-plus'
import 'element-plus/theme-chalk/index.css'
// 只导入常用图标，其他图标在组件中按需导入
import {
  Monitor, TrendCharts, DataAnalysis, PieChart, Warning,
  Document, FolderOpened, Grid, Menu, Expand, Fold,
  Bell, ArrowRight, User, Setting, Search, Refresh
} from '@element-plus/icons-vue'

console.log('🚀 应用启动中...')

// 创建Vue应用实例
const app = createApp(App)

// 配置Pinia状态管理
const pinia = createPinia()
app.use(pinia)

// 配置路由
app.use(router)

// 配置Element Plus
app.use(ElementPlus)

// 注册常用图标（按需注册，提升性能）
const commonIcons = {
  Monitor, TrendCharts, DataAnalysis, PieChart, Warning,
  Document, FolderOpened, Grid, Menu, Expand, Fold,
  Bell, ArrowRight, User, Setting, Search, Refresh
}

for (const [key, component] of Object.entries(commonIcons)) {
  app.component(key, component)
}

// 挂载应用
app.mount('#app')

console.log('✅ 应用启动成功')
