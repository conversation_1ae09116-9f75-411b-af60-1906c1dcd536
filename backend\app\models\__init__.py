"""Backward-compatibility package.

Older code imports models from `app.models.*`, but the actual implementation
lives in `app.db.models.*` after the directory re-organization.  This package
re-exports the real modules so that legacy import paths continue to work until
all references are updated.
"""

import importlib
import sys
from types import ModuleType

# List of concrete model submodules to proxy
_SUBMODULES = [
    "user",
    "backtest",
    "market",
    "strategy",
    "trading",
    "ctp_models",
    "types",
]

# Dynamically import the real submodule and insert a proxy into sys.modules
for _name in _SUBMODULES:
    real_name = f"app.db.models.{_name}"
    proxy_name = f"app.models.{_name}"
    real_module = importlib.import_module(real_name)
    sys.modules[proxy_name] = real_module  # make `import app.models.user` work
    # Also expose as attribute of this package (e.g. app.models.user)
    setattr(sys.modules[__name__], _name, real_module)

# Clean up
del importlib, sys, ModuleType, _SUBMODULES, _name, real_name, proxy_name, real_module
