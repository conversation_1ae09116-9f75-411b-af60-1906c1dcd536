<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background: #d4edda; color: #155724; }
        .disconnected { background: #f8d7da; color: #721c24; }
        .connecting { background: #fff3cd; color: #856404; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket连接测试</h1>
        
        <div>
            <label>WebSocket URL:</label>
            <select id="wsUrl">
                <option value="ws://localhost:8000/api/v1/ws">通用端点 (/api/v1/ws)</option>
                <option value="ws://localhost:8000/api/v1/ws/market">市场数据 (/api/v1/ws/market)</option>
                <option value="ws://localhost:8000/api/v1/ws/trading">交易数据 (/api/v1/ws/trading)</option>
                <option value="ws://localhost:8000/api/v1/ws/strategy">策略数据 (/api/v1/ws/strategy)</option>
                <option value="ws://localhost:8000/ws">旧版端点 (/ws)</option>
            </select>
        </div>
        
        <div id="status" class="status disconnected">未连接</div>
        
        <div>
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开</button>
            <button onclick="sendPing()">发送Ping</button>
            <button onclick="clearLog()">清空日志</button>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="输入消息..." style="width: 300px;">
            <button onclick="sendMessage()">发送消息</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        let reconnectAttempts = 0;
        const maxReconnectAttempts = 5;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(status, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = status;
            statusDiv.className = `status ${className}`;
        }

        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket已经连接', 'error');
                return;
            }

            updateStatus('连接中...', 'connecting');
            log(`尝试连接到: ${url}`);

            try {
                ws = new WebSocket(url);

                ws.onopen = function(event) {
                    log('WebSocket连接成功！', 'success');
                    updateStatus('已连接', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    reconnectAttempts = 0;
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`收到消息: ${JSON.stringify(data, null, 2)}`, 'success');
                    } catch (e) {
                        log(`收到文本消息: ${event.data}`, 'success');
                    }
                };

                ws.onclose = function(event) {
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason}`, 'error');
                    updateStatus('已断开', 'disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    
                    // 自动重连
                    if (reconnectAttempts < maxReconnectAttempts) {
                        reconnectAttempts++;
                        log(`${3}秒后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`);
                        setTimeout(connect, 3000);
                    }
                };

                ws.onerror = function(error) {
                    log(`WebSocket错误: ${error}`, 'error');
                    updateStatus('连接错误', 'disconnected');
                };

            } catch (error) {
                log(`连接失败: ${error.message}`, 'error');
                updateStatus('连接失败', 'disconnected');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) {
                log('请输入消息', 'error');
                return;
            }

            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }

            try {
                const data = JSON.parse(message);
                ws.send(JSON.stringify(data));
                log(`发送JSON消息: ${message}`);
            } catch (e) {
                ws.send(message);
                log(`发送文本消息: ${message}`);
            }
            
            input.value = '';
        }

        function sendPing() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }

            const pingMessage = {
                type: 'ping',
                timestamp: Date.now()
            };
            
            ws.send(JSON.stringify(pingMessage));
            log(`发送Ping: ${JSON.stringify(pingMessage)}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 页面加载时自动连接
        window.onload = function() {
            log('WebSocket测试页面加载完成');
        };

        // 回车发送消息
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
