<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚡ 性能优化演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .performance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .performance-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .performance-card:hover {
            transform: translateY(-5px);
        }

        .card-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #3498db;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #666;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .metric-value.good {
            color: #27ae60;
        }

        .metric-value.warning {
            color: #f39c12;
        }

        .metric-value.error {
            color: #e74c3c;
        }

        .optimization-demo {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }

        .demo-btn {
            padding: 8px 16px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .demo-btn:hover {
            background: #2980b9;
        }

        .demo-btn.active {
            background: #e74c3c;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .data-table th,
        .data-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .virtual-list {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
        }

        .virtual-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            transition: background 0.2s ease;
        }

        .virtual-item:hover {
            background: #f8f9fa;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            transition: width 0.3s ease;
            width: 0%;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e0e0e0;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .cache-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .cache-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
        }

        .cache-indicator.active {
            background: #2ecc71;
        }

        @media (max-width: 768px) {
            .performance-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>⚡ 前端性能优化演示</h1>
            <p>展示各种前端性能优化技术和实时监控</p>
        </div>

        <!-- 性能指标 -->
        <div class="performance-grid">
            <div class="performance-card">
                <div class="card-title">📊 页面性能指标</div>
                <div class="metric">
                    <span class="metric-label">页面加载时间</span>
                    <span class="metric-value good" id="loadTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">DOM解析时间</span>
                    <span class="metric-value" id="domTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">资源加载时间</span>
                    <span class="metric-value" id="resourceTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">首次内容绘制</span>
                    <span class="metric-value" id="fcpTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最大内容绘制</span>
                    <span class="metric-value" id="lcpTime">--</span>
                </div>
            </div>

            <div class="performance-card">
                <div class="card-title">🚀 运行时性能</div>
                <div class="metric">
                    <span class="metric-label">内存使用</span>
                    <span class="metric-value" id="memoryUsage">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">FPS</span>
                    <span class="metric-value good" id="fpsCounter">60</span>
                </div>
                <div class="metric">
                    <span class="metric-label">API响应时间</span>
                    <span class="metric-value" id="apiResponseTime">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">缓存命中率</span>
                    <span class="metric-value good" id="cacheHitRate">95%</span>
                </div>
                <div class="metric">
                    <span class="metric-label">错误率</span>
                    <span class="metric-value good" id="errorRate">0.1%</span>
                </div>
            </div>

            <div class="performance-card">
                <div class="card-title">💾 缓存状态</div>
                <div class="cache-status">
                    <div class="cache-indicator active"></div>
                    <span>本地存储缓存</span>
                </div>
                <div class="cache-status">
                    <div class="cache-indicator active"></div>
                    <span>内存缓存</span>
                </div>
                <div class="cache-status">
                    <div class="cache-indicator active"></div>
                    <span>Service Worker</span>
                </div>
                <div class="cache-status">
                    <div class="cache-indicator"></div>
                    <span>CDN缓存</span>
                </div>
                <div class="metric">
                    <span class="metric-label">缓存大小</span>
                    <span class="metric-value" id="cacheSize">2.3MB</span>
                </div>
            </div>

            <div class="performance-card">
                <div class="card-title">🌐 网络优化</div>
                <div class="metric">
                    <span class="metric-label">连接类型</span>
                    <span class="metric-value" id="connectionType">4g</span>
                </div>
                <div class="metric">
                    <span class="metric-label">下行速度</span>
                    <span class="metric-value" id="downlinkSpeed">10 Mbps</span>
                </div>
                <div class="metric">
                    <span class="metric-label">RTT</span>
                    <span class="metric-value" id="rttTime">50ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">数据压缩率</span>
                    <span class="metric-value good" id="compressionRatio">75%</span>
                </div>
            </div>
        </div>

        <!-- 优化演示 -->
        <div class="optimization-demo">
            <div class="card-title">🎯 性能优化演示</div>
            <div class="demo-controls">
                <button class="demo-btn" onclick="demoLazyLoading()">懒加载演示</button>
                <button class="demo-btn" onclick="demoVirtualScrolling()">虚拟滚动</button>
                <button class="demo-btn" onclick="demoDebouncing()">防抖优化</button>
                <button class="demo-btn" onclick="demoImageOptimization()">图片优化</button>
                <button class="demo-btn" onclick="demoCodeSplitting()">代码分割</button>
                <button class="demo-btn" onclick="clearDemo()">清除演示</button>
            </div>
            
            <div id="demoArea">
                <p>点击上方按钮查看不同的性能优化演示</p>
            </div>
        </div>
    </div>

    <script>
        // 性能监控
        class PerformanceMonitor {
            constructor() {
                this.metrics = {};
                this.observers = [];
                this.init();
            }

            init() {
                this.measurePageLoad();
                this.measureRuntime();
                this.setupObservers();
                this.startMonitoring();
            }

            measurePageLoad() {
                window.addEventListener('load', () => {
                    const navigation = performance.getEntriesByType('navigation')[0];
                    
                    this.updateMetric('loadTime', `${Math.round(navigation.loadEventEnd - navigation.fetchStart)}ms`);
                    this.updateMetric('domTime', `${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms`);
                    this.updateMetric('resourceTime', `${Math.round(navigation.loadEventEnd - navigation.domContentLoadedEventEnd)}ms`);
                });

                // 测量FCP和LCP
                if ('PerformanceObserver' in window) {
                    const observer = new PerformanceObserver((list) => {
                        for (const entry of list.getEntries()) {
                            if (entry.entryType === 'paint') {
                                if (entry.name === 'first-contentful-paint') {
                                    this.updateMetric('fcpTime', `${Math.round(entry.startTime)}ms`);
                                }
                            } else if (entry.entryType === 'largest-contentful-paint') {
                                this.updateMetric('lcpTime', `${Math.round(entry.startTime)}ms`);
                            }
                        }
                    });
                    
                    observer.observe({entryTypes: ['paint', 'largest-contentful-paint']});
                }
            }

            measureRuntime() {
                // 内存使用监控
                if ('memory' in performance) {
                    setInterval(() => {
                        const memory = performance.memory;
                        const used = Math.round(memory.usedJSHeapSize / 1048576);
                        this.updateMetric('memoryUsage', `${used}MB`);
                    }, 5000);
                }

                // FPS监控
                let lastTime = performance.now();
                let frames = 0;
                
                const countFPS = () => {
                    frames++;
                    const currentTime = performance.now();
                    
                    if (currentTime >= lastTime + 1000) {
                        const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                        this.updateMetric('fpsCounter', fps);
                        
                        // 根据FPS设置颜色
                        const fpsElement = document.getElementById('fpsCounter');
                        if (fps >= 55) {
                            fpsElement.className = 'metric-value good';
                        } else if (fps >= 30) {
                            fpsElement.className = 'metric-value warning';
                        } else {
                            fpsElement.className = 'metric-value error';
                        }
                        
                        frames = 0;
                        lastTime = currentTime;
                    }
                    
                    requestAnimationFrame(countFPS);
                };
                
                requestAnimationFrame(countFPS);
            }

            setupObservers() {
                // 网络信息监控
                if ('connection' in navigator) {
                    const connection = navigator.connection;
                    this.updateMetric('connectionType', connection.effectiveType || 'unknown');
                    this.updateMetric('downlinkSpeed', `${connection.downlink || 0} Mbps`);
                    this.updateMetric('rttTime', `${connection.rtt || 0}ms`);
                }
            }

            startMonitoring() {
                // 模拟API响应时间监控
                setInterval(() => {
                    const responseTime = Math.round(Math.random() * 200 + 50);
                    this.updateMetric('apiResponseTime', `${responseTime}ms`);
                }, 3000);

                // 模拟缓存命中率更新
                setInterval(() => {
                    const hitRate = Math.round(Math.random() * 10 + 90);
                    this.updateMetric('cacheHitRate', `${hitRate}%`);
                }, 5000);
            }

            updateMetric(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
                this.metrics[id] = value;
            }
        }

        // 优化演示功能
        function demoLazyLoading() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <h3>🖼️ 懒加载演示</h3>
                <p>图片懒加载可以显著提升页面初始加载速度</p>
                <div style="height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px;">
                    ${Array.from({length: 20}, (_, i) => `
                        <div style="margin: 10px 0; padding: 10px; background: #f8f9fa;">
                            <img data-src="https://picsum.photos/100/60?random=${i}" 
                                 src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='60'%3E%3Crect width='100%25' height='100%25' fill='%23ddd'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em'%3E加载中...%3C/text%3E%3C/svg%3E"
                                 class="lazy-image" style="width: 100px; height: 60px;">
                            <span>图片 ${i + 1} - 滚动时懒加载</span>
                        </div>
                    `).join('')}
                </div>
            `;
            
            // 实现懒加载
            const lazyImages = document.querySelectorAll('.lazy-image');
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy-image');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            lazyImages.forEach(img => imageObserver.observe(img));
        }

        function demoVirtualScrolling() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <h3>📜 虚拟滚动演示</h3>
                <p>虚拟滚动只渲染可见区域的元素，大幅提升大列表性能</p>
                <div class="virtual-list" id="virtualList">
                    <div class="loading-spinner"></div> 正在生成10000条数据...
                </div>
            `;
            
            setTimeout(() => {
                const virtualList = document.getElementById('virtualList');
                const totalItems = 10000;
                const itemHeight = 40;
                const visibleItems = Math.ceil(300 / itemHeight);
                
                virtualList.innerHTML = '';
                virtualList.style.height = `${totalItems * itemHeight}px`;
                virtualList.style.position = 'relative';
                
                function renderVisibleItems(scrollTop = 0) {
                    const startIndex = Math.floor(scrollTop / itemHeight);
                    const endIndex = Math.min(startIndex + visibleItems + 1, totalItems);
                    
                    virtualList.innerHTML = '';
                    
                    for (let i = startIndex; i < endIndex; i++) {
                        const item = document.createElement('div');
                        item.className = 'virtual-item';
                        item.style.position = 'absolute';
                        item.style.top = `${i * itemHeight}px`;
                        item.style.width = '100%';
                        item.style.height = `${itemHeight}px`;
                        item.innerHTML = `
                            <strong>项目 ${i + 1}</strong> - 
                            虚拟滚动项目，只有可见的项目被渲染到DOM中
                        `;
                        virtualList.appendChild(item);
                    }
                }
                
                renderVisibleItems();
                
                virtualList.addEventListener('scroll', (e) => {
                    renderVisibleItems(e.target.scrollTop);
                });
            }, 1000);
        }

        function demoDebouncing() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <h3>⏱️ 防抖优化演示</h3>
                <p>防抖技术可以减少频繁的API调用，提升性能</p>
                <div style="margin: 20px 0;">
                    <input type="text" id="searchInput" placeholder="输入搜索关键词..." 
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="display: flex; gap: 20px;">
                    <div style="flex: 1;">
                        <h4>无防抖 (每次输入都触发)</h4>
                        <div id="normalSearch" style="height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;">
                            开始输入查看效果...
                        </div>
                        <p>API调用次数: <span id="normalCount">0</span></p>
                    </div>
                    <div style="flex: 1;">
                        <h4>防抖优化 (300ms延迟)</h4>
                        <div id="debouncedSearch" style="height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f8f9fa;">
                            开始输入查看效果...
                        </div>
                        <p>API调用次数: <span id="debouncedCount">0</span></p>
                    </div>
                </div>
            `;
            
            let normalCount = 0;
            let debouncedCount = 0;
            let debounceTimer;
            
            const searchInput = document.getElementById('searchInput');
            const normalDiv = document.getElementById('normalSearch');
            const debouncedDiv = document.getElementById('debouncedSearch');
            
            searchInput.addEventListener('input', (e) => {
                const value = e.target.value;
                
                // 无防抖版本
                normalCount++;
                normalDiv.innerHTML += `<div>搜索: "${value}" (第${normalCount}次调用)</div>`;
                normalDiv.scrollTop = normalDiv.scrollHeight;
                document.getElementById('normalCount').textContent = normalCount;
                
                // 防抖版本
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    debouncedCount++;
                    debouncedDiv.innerHTML += `<div>搜索: "${value}" (第${debouncedCount}次调用)</div>`;
                    debouncedDiv.scrollTop = debouncedDiv.scrollHeight;
                    document.getElementById('debouncedCount').textContent = debouncedCount;
                }, 300);
            });
        }

        function demoImageOptimization() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <h3>🖼️ 图片优化演示</h3>
                <p>展示不同的图片优化技术对加载性能的影响</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0;">
                    <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px;">
                        <h4>原始图片 (较大)</h4>
                        <div class="progress-bar"><div class="progress-fill" id="progress1"></div></div>
                        <img id="img1" style="width: 100%; height: 150px; object-fit: cover; background: #f0f0f0;">
                        <p>大小: ~500KB</p>
                    </div>
                    <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px;">
                        <h4>WebP格式 (优化)</h4>
                        <div class="progress-bar"><div class="progress-fill" id="progress2"></div></div>
                        <img id="img2" style="width: 100%; height: 150px; object-fit: cover; background: #f0f0f0;">
                        <p>大小: ~150KB</p>
                    </div>
                    <div style="border: 1px solid #ddd; padding: 15px; border-radius: 8px;">
                        <h4>响应式图片</h4>
                        <div class="progress-bar"><div class="progress-fill" id="progress3"></div></div>
                        <img id="img3" style="width: 100%; height: 150px; object-fit: cover; background: #f0f0f0;">
                        <p>根据屏幕大小加载</p>
                    </div>
                </div>
            `;
            
            // 模拟图片加载进度
            const loadImage = (imgId, progressId, delay) => {
                const img = document.getElementById(imgId);
                const progress = document.getElementById(progressId);
                
                let loaded = 0;
                const interval = setInterval(() => {
                    loaded += Math.random() * 20;
                    if (loaded >= 100) {
                        loaded = 100;
                        clearInterval(interval);
                        img.src = `https://picsum.photos/300/150?random=${Math.floor(Math.random() * 100)}`;
                    }
                    progress.style.width = `${loaded}%`;
                }, delay);
            };
            
            loadImage('img1', 'progress1', 100); // 慢速加载
            loadImage('img2', 'progress2', 50);  // 中速加载
            loadImage('img3', 'progress3', 30);  // 快速加载
        }

        function demoCodeSplitting() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = `
                <h3>📦 代码分割演示</h3>
                <p>按需加载模块可以减少初始包大小，提升首屏加载速度</p>
                <div style="margin: 20px 0;">
                    <button class="demo-btn" onclick="loadModule('chart')">加载图表模块</button>
                    <button class="demo-btn" onclick="loadModule('table')">加载表格模块</button>
                    <button class="demo-btn" onclick="loadModule('form')">加载表单模块</button>
                </div>
                <div id="moduleArea" style="min-height: 200px; border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f8f9fa;">
                    点击上方按钮按需加载不同的功能模块
                </div>
            `;
        }

        function loadModule(moduleType) {
            const moduleArea = document.getElementById('moduleArea');
            moduleArea.innerHTML = '<div class="loading-spinner"></div> 正在加载模块...';
            
            // 模拟模块加载
            setTimeout(() => {
                switch(moduleType) {
                    case 'chart':
                        moduleArea.innerHTML = `
                            <h4>📊 图表模块已加载</h4>
                            <div style="height: 150px; background: linear-gradient(45deg, #3498db, #2ecc71); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                模拟图表组件
                            </div>
                        `;
                        break;
                    case 'table':
                        moduleArea.innerHTML = `
                            <h4>📋 表格模块已加载</h4>
                            <table class="data-table">
                                <thead>
                                    <tr><th>ID</th><th>名称</th><th>状态</th></tr>
                                </thead>
                                <tbody>
                                    <tr><td>1</td><td>项目A</td><td>活跃</td></tr>
                                    <tr><td>2</td><td>项目B</td><td>暂停</td></tr>
                                    <tr><td>3</td><td>项目C</td><td>完成</td></tr>
                                </tbody>
                            </table>
                        `;
                        break;
                    case 'form':
                        moduleArea.innerHTML = `
                            <h4>📝 表单模块已加载</h4>
                            <form style="display: grid; gap: 10px;">
                                <input type="text" placeholder="姓名" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <input type="email" placeholder="邮箱" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <button type="submit" class="demo-btn">提交</button>
                            </form>
                        `;
                        break;
                }
            }, 1000);
        }

        function clearDemo() {
            const demoArea = document.getElementById('demoArea');
            demoArea.innerHTML = '<p>演示已清除，点击上方按钮查看不同的性能优化演示</p>';
        }

        // 初始化性能监控
        const performanceMonitor = new PerformanceMonitor();

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('⚡ 性能优化演示页面已加载');
        });
    </script>
</body>
</html>
