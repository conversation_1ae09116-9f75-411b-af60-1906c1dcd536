input {
  # 接收后端应用日志
  tcp {
    port => 5000
    codec => json_lines
  }
  
  # 接收系统日志
  syslog {
    port => 5514
  }
  
  # 接收文件日志
  file {
    path => "/var/log/quant-platform/*.log"
    start_position => "beginning"
    codec => multiline {
      pattern => "^\d{4}-\d{2}-\d{2}"
      negate => true
      what => "previous"
    }
  }
}

filter {
  # 解析JSON格式的日志
  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
    }
  }
  
  # 解析Python日志格式
  if [logger_name] {
    mutate {
      add_field => {
        "application" => "quant-platform"
        "environment" => "${ENVIRONMENT:production}"
      }
    }
  }
  
  # 解析HTTP请求日志
  if [request_method] {
    mutate {
      add_field => {
        "log_type" => "http_request"
      }
    }
    
    # 计算响应时间
    if [response_time] {
      ruby {
        code => "event.set('response_time_ms', event.get('response_time') * 1000)"
      }
    }
  }
  
  # 解析错误日志
  if [level] == "ERROR" or [level] == "CRITICAL" {
    mutate {
      add_tag => [ "error" ]
    }
  }
  
  # 添加地理位置信息（如果有IP）
  if [client_ip] {
    geoip {
      source => "client_ip"
      target => "geoip"
    }
  }
  
  # 时间戳处理
  date {
    match => [ "timestamp", "ISO8601", "yyyy-MM-dd HH:mm:ss,SSS" ]
    target => "@timestamp"
  }
}

output {
  # 输出到Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "quant-platform-%{+YYYY.MM.dd}"
    template_name => "quant-platform"
    template => "/etc/logstash/templates/quant-platform.json"
    template_overwrite => true
  }
  
  # 错误日志单独索引
  if "error" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "quant-platform-errors-%{+YYYY.MM.dd}"
    }
  }
  
  # 调试输出（开发环境）
  if "${ENVIRONMENT:production}" == "development" {
    stdout {
      codec => rubydebug
    }
  }
}