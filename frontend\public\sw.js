/**
 * Service Worker for 量化投资平台
 * 提供基础的缓存和离线功能
 */

const CACHE_NAME = 'quant-platform-v1.0.0'
const STATIC_CACHE = 'static-v1'
const API_CACHE = 'api-v1'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico'
]

// 需要缓存的API路径
const API_PATTERNS = [
  /^\/api\/v1\/market\//,
  /^\/api\/v1\/user\//,
  /^\/api\/v1\/trading\//
]

// Service Worker 安装事件
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Service Worker: Installation complete')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error)
      })
  )
})

// Service Worker 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== API_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker: Activation complete')
        return self.clients.claim()
      })
  )
})

// 网络请求拦截
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // 跳过非HTTP请求
  if (!request.url.startsWith('http')) {
    return
  }
  
  // 跳过WebSocket请求
  if (request.url.includes('/ws/') || request.headers.get('upgrade') === 'websocket') {
    return
  }
  
  // 跳过外部API请求（到后端服务器的请求）
  if (url.hostname === 'localhost' && url.port === '8000') {
    return // 让浏览器直接处理，不通过Service Worker
  }

  // 处理本地API请求（仅处理前端服务器的API请求）
  if (url.hostname === 'localhost' && url.port === '5173' && url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(request))
    return
  }
  
  // 处理静态资源请求
  event.respondWith(handleStaticRequest(request))
})

/**
 * 处理API请求
 * 使用网络优先策略，失败时返回缓存
 */
async function handleApiRequest(request) {
  try {
    // 尝试网络请求
    const response = await fetch(request)
    
    // 如果是GET请求且响应成功，缓存响应
    if (request.method === 'GET' && response.ok) {
      const cache = await caches.open(API_CACHE)
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('Service Worker: Network request failed, trying cache', error)
    
    // 网络失败，尝试从缓存获取
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 缓存也没有，返回离线页面或错误响应
    return new Response(
      JSON.stringify({
        error: 'Network unavailable',
        message: '网络不可用，请检查网络连接'
      }),
      {
        status: 503,
        statusText: 'Service Unavailable',
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}

/**
 * 处理静态资源请求
 * 使用缓存优先策略
 */
async function handleStaticRequest(request) {
  try {
    // 先尝试从缓存获取
    const cachedResponse = await caches.match(request)
    if (cachedResponse) {
      return cachedResponse
    }
    
    // 缓存没有，尝试网络请求
    const response = await fetch(request)
    
    // 如果响应成功，缓存响应
    if (response.ok) {
      const cache = await caches.open(STATIC_CACHE)
      cache.put(request, response.clone())
    }
    
    return response
  } catch (error) {
    console.log('Service Worker: Static request failed', error)
    
    // 如果是导航请求，返回首页
    if (request.mode === 'navigate') {
      const cachedIndex = await caches.match('/')
      if (cachedIndex) {
        return cachedIndex
      }
    }
    
    // 返回网络错误
    return new Response('Network Error', {
      status: 408,
      statusText: 'Request Timeout'
    })
  }
}

// 消息处理
self.addEventListener('message', (event) => {
  const { type, payload } = event.data
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting()
      break
      
    case 'CLEAR_CACHE':
      clearAllCaches()
      break
      
    case 'GET_CACHE_INFO':
      getCacheInfo().then((info) => {
        event.ports[0].postMessage(info)
      })
      break
      
    default:
      console.log('Service Worker: Unknown message type', type)
  }
})

/**
 * 清除所有缓存
 */
async function clearAllCaches() {
  try {
    const cacheNames = await caches.keys()
    await Promise.all(
      cacheNames.map((cacheName) => caches.delete(cacheName))
    )
    console.log('Service Worker: All caches cleared')
  } catch (error) {
    console.error('Service Worker: Failed to clear caches', error)
  }
}

/**
 * 获取缓存信息
 */
async function getCacheInfo() {
  try {
    const cacheNames = await caches.keys()
    const info = {}
    
    for (const cacheName of cacheNames) {
      const cache = await caches.open(cacheName)
      const keys = await cache.keys()
      info[cacheName] = keys.length
    }
    
    return info
  } catch (error) {
    console.error('Service Worker: Failed to get cache info', error)
    return {}
  }
}

console.log('Service Worker: Script loaded successfully')
