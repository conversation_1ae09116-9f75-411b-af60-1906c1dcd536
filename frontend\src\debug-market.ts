/**
 * Market 页面调试工具
 * 用于检测数据连接问题
 */

import { marketApi } from '@/api'

export class MarketDebugger {
  private store: any = null

  constructor() {
    console.log('🔍 Market Debugger 初始化')
    this.initStore()
    this.setupGlobalDebug()
  }

  private async initStore() {
    try {
      const { useMarketStore } = await import('@/stores/modules/market')
      this.store = useMarketStore()
    } catch (error) {
      console.error('Market Debugger store初始化失败:', error)
    }
  }

  // 设置全局调试函数
  private setupGlobalDebug() {
    // @ts-ignore
    window.marketDebug = {
      testAPI: this.testAPI.bind(this),
      testStore: this.testStore.bind(this),
      testAll: this.testAll.bind(this),
      checkConfig: this.checkConfig.bind(this)
    }

    console.log('🔧 调试函数已注册到 window.marketDebug')
    console.log('使用方法:')
    console.log('  window.marketDebug.testAll() - 运行所有测试')
    console.log('  window.marketDebug.testAPI() - 测试API连接')
    console.log('  window.marketDebug.testStore() - 测试Store数据')
    console.log('  window.marketDebug.checkConfig() - 检查配置')
  }

  // 检查配置
  checkConfig() {
    console.group('📋 配置检查')

    console.log('环境变量:')
    console.log(`  VITE_USE_MOCK: ${import.meta.env.VITE_USE_MOCK}`)
    console.log(`  VITE_API_BASE_URL: ${import.meta.env.VITE_API_BASE_URL}`)
    console.log(`  MODE: ${import.meta.env.MODE}`)

    console.log('\nAPI 路径常量:')
    console.log(`  MARKET.OVERVIEW: /api/v1/market/overview`)
    console.log(`  MARKET.STOCKS: /api/v1/market/stocks`)
    console.log(`  MARKET.WATCHLIST: /api/v1/market/watchlist`)

    console.groupEnd()
  }

  // 测试API连接
  async testAPI() {
    console.group('🔌 API连接测试')

    try {
      // 1. 测试市场概览
      console.log('1. 测试市场概览API...')
      const overview = await marketApi.getMarketOverview()
      console.log('✅ 市场概览API成功:', overview)

      // 2. 测试股票列表
      console.log('2. 测试股票列表API...')
      const stocks = await marketApi.getStockList({ pageSize: 5 })
      console.log('✅ 股票列表API成功:', stocks)

      // 3. 测试板块数据
      console.log('3. 测试板块数据API...')
      const sectors = await marketApi.getSectors()
      console.log('✅ 板块数据API成功:', sectors)

      // 4. 测试排行榜
      console.log('4. 测试排行榜API...')
      const rankings = await marketApi.getRankings({
        type: 'change_percent',
        limit: 10
      })
      console.log('✅ 排行榜API成功:', rankings)

    } catch (error) {
      console.error('❌ API测试失败:', error)
    }

    console.groupEnd()
  }

  // 测试Store数据
  async testStore() {
    console.group('📦 Store数据测试')

    try {
      console.log('当前Store状态:')
      console.log(`  stockList: ${this.store.stockList?.length || 0} 条数据`)
      console.log(`  indices: ${Object.keys(this.store.indices).length} 个指数`)
      console.log(`  loading.initial: ${this.store.loading.initial}`)
      console.log(`  errors: `, this.store.errors)

      console.log('\n开始初始化Store...')
      await this.store.initialize()

      console.log('初始化后的Store状态:')
      console.log(`  stockList: ${this.store.stockList?.length || 0} 条数据`)
      console.log(`  indices: ${Object.keys(this.store.indices).length} 个指数`)
      console.log(`  sectors: ${this.store.sectors?.length || 0} 个板块`)
      console.log(`  news: ${this.store.news?.length || 0} 条新闻`)

      if (this.store.stockList?.length > 0) {
        console.log('✅ Store数据加载成功')
        console.log('前5条股票数据:', this.store.stockList.slice(0, 5))
      } else {
        console.error('❌ Store中无股票数据')
      }

    } catch (error) {
      console.error('❌ Store测试失败:', error)
    }

    console.groupEnd()
  }

  // 运行所有测试
  async testAll() {
    console.log('🚀 开始完整调试测试')

    this.checkConfig()
    await this.testAPI()
    await this.testStore()

    console.log('✅ 调试测试完成')
  }
}

// 自动初始化（仅在开发环境）
if (import.meta.env.MODE === 'development') {
  new MarketDebugger()
}

export default MarketDebugger
