<template>
  <div class="market-overview">
    <!-- 市场指数概览 -->
    <div class="indices-section">
      <h3 class="section-title">
        <el-icon><TrendCharts /></el-icon>
        主要指数
      </h3>
      <el-row :gutter="16">
        <el-col 
          v-for="index in indices" 
          :key="index.code" 
          :span="6"
        >
          <el-card class="index-card" shadow="hover">
            <div class="index-content">
              <div class="index-header">
                <span class="index-name">{{ index.name }}</span>
                <span class="index-code">{{ index.code }}</span>
              </div>
              <div class="index-price">
                {{ formatPrice(index.value) }}
              </div>
              <div class="index-change" :class="getPriceClass(index.change)">
                <span class="change-value">{{ formatChange(index.change) }}</span>
                <span class="change-percent">{{ formatPercent(index.changePercent) }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 市场统计 -->
    <div class="market-stats">
      <h3 class="section-title">
        <el-icon><DataAnalysis /></el-icon>
        市场统计
      </h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value up">{{ stats.rising || 0 }}</div>
            <div class="stat-label">上涨</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value down">{{ stats.falling || 0 }}</div>
            <div class="stat-label">下跌</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value neutral">{{ stats.unchanged || 0 }}</div>
            <div class="stat-label">平盘</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <div class="stat-value">{{ stats.total || 0 }}</div>
            <div class="stat-label">总计</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 热门板块 -->
    <div class="hot-sectors">
      <h3 class="section-title">
        <el-icon><Grid /></el-icon>
        热门板块
      </h3>
      <div class="sectors-grid">
        <div 
          v-for="sector in hotSectors" 
          :key="sector.name"
          class="sector-item"
          :class="getPriceClass(sector.changePercent)"
          @click="$emit('sector-click', sector)"
        >
          <div class="sector-name">{{ sector.name }}</div>
          <div class="sector-change">{{ formatPercent(sector.changePercent) }}</div>
          <div class="sector-count">{{ sector.stocksCount }}只</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { TrendCharts, DataAnalysis, Grid } from '@element-plus/icons-vue'
import { formatPrice, formatChange, formatPercent } from '@/utils/formatters'
import type { IndexData, MarketStats, SectorData } from '@/types/market'

interface Props {
  indices?: IndexData[]
  stats?: MarketStats
  hotSectors?: SectorData[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  indices: () => [],
  stats: () => ({
    total: 0,
    rising: 0,
    falling: 0,
    unchanged: 0
  }),
  hotSectors: () => [],
  loading: false
})

const emit = defineEmits<{
  (e: 'sector-click', sector: SectorData): void
  (e: 'index-click', index: IndexData): void
}>()

// 计算价格变化样式类
const getPriceClass = (change: number) => {
  if (change > 0) return 'up'
  if (change < 0) return 'down'
  return 'neutral'
}

// 默认数据
const defaultIndices = ref<IndexData[]>([
  { code: '000001.SH', name: '上证指数', value: 3245.67, change: 1.23, changePercent: 0.038 },
  { code: '399001.SZ', name: '深证成指', value: 10876.54, change: -45.32, changePercent: -0.415 },
  { code: '399006.SZ', name: '创业板指', value: 2234.89, change: 17.45, changePercent: 0.787 },
  { code: '000300.SH', name: '沪深300', value: 4123.45, change: 8.76, changePercent: 0.213 }
])

const defaultSectors = ref<SectorData[]>([
  { name: '银行', changePercent: 1.2, stocksCount: 42 },
  { name: '科技', changePercent: -0.8, stocksCount: 156 },
  { name: '医药', changePercent: 2.1, stocksCount: 89 },
  { name: '地产', changePercent: -1.5, stocksCount: 67 },
  { name: '汽车', changePercent: 0.9, stocksCount: 78 },
  { name: '军工', changePercent: 3.2, stocksCount: 34 }
])

// 使用传入数据或默认数据
const indices = computed(() => props.indices.length > 0 ? props.indices : defaultIndices.value)
const hotSectors = computed(() => props.hotSectors.length > 0 ? props.hotSectors : defaultSectors.value)
</script>

<style scoped>
.market-overview {
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.indices-section {
  margin-bottom: 32px;
}

.index-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.index-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.index-content {
  text-align: center;
}

.index-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.index-name {
  font-weight: 600;
  color: #303133;
}

.index-code {
  font-size: 12px;
  color: #909399;
}

.index-price {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #303133;
}

.index-change {
  display: flex;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
}

.market-stats {
  margin-bottom: 32px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.hot-sectors {
  margin-bottom: 32px;
}

.sectors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.sector-item {
  padding: 16px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sector-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.sector-name {
  font-weight: 600;
  margin-bottom: 8px;
  color: #303133;
}

.sector-change {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 4px;
}

.sector-count {
  font-size: 12px;
  color: #909399;
}

/* 价格变化颜色 */
.up {
  color: #f56c6c;
}

.down {
  color: #67c23a;
}

.neutral {
  color: #909399;
}

.stat-value.up {
  color: #f56c6c;
}

.stat-value.down {
  color: #67c23a;
}

.stat-value.neutral {
  color: #909399;
}

.sector-item.up {
  border-color: #f56c6c;
  background: #fef0f0;
}

.sector-item.down {
  border-color: #67c23a;
  background: #f0f9ff;
}
</style>
